# Use the golang alpine image as builder
FROM golang:1.24.2-alpine3.21 AS build

# Set the build directory
WORKDIR /build

# Copy and build the server, caching build artifacts for quicker building
COPY . .
RUN --mount=type=cache,id=gocache,target=/root/.cache/go-build --mount=type=cache,id=gocache,target=/go/pkg/mod go build -o server-executable -v main.go

# ---------------------------

# Use bare alpine for the final build
FROM alpine

RUN adduser -D -s /bin/sh -u 1000 server && \
    mkdir /server && \
    chown 1000:1000 /server

WORKDIR /server

COPY --chown=1000:1000 --from=build /build/server-executable .

EXPOSE 19132/udp

USER server

CMD ["/server/server-executable"]
