{"Commands": {"Permission": "<red>You are not allowed to use this feature.</red>\n<gold>To access this command, you need to purchase </gold>%v<gold>rank at https://play.massacremc.net/</gold>", "Target": "<red>You must specify only one player.</red>", "Hub": {"Success": "<green>Teleported to hub.</green>"}, "GameMode": {"Success": "%v <green>Your game mode is set to <grey>%v</grey>.</green>", "Error": "<red>The game mode <grey>%v</grey> does not exist.</red>"}, "GiveRank": {"Success": "%v <green>Successfully given <grey>%v</grey> the <grey>%v</grey> rank.</green>", "HierarchyError": "<red>You cannot give this player a rank higher or same as yours.</red>"}, "Bank": {"DepositSuccess": "%v <green>You deposited <grey>%v</grey> to your faction's bank account.</green>", "ViewSuccess": "%v <green>Your faction currently has <grey>%v</grey> in its bank account.</green>", "NotInFaction": "<red>You must be in a faction in order to do this.</red>", "NotEnoughDoubloons": "<red>You do not have enough doubloons to do this.</red>", "NotEnoughBankDoubloons": "<red>Your faction's bank does not have enough doubloons to do this.</red>"}, "Home": {"SetSuccess": "%v <green>You have a new home called <grey>%v</grey> located at <grey>%v</grey>.</green>", "DeleteSuccess": "%v <green>You deleted the home <grey>%v</grey> located at <grey>%v</grey>.</green>"}, "Duel": {"InviteSuccess": "%v <green>You are invited to a Duels game <yellow>[Keep Inventory: <grey>%v</grey>]</yellow> with the player <grey>%v</grey>. Type '/duels accept' within 60 seconds!</green>", "InviteSent": "%v <green>Request sent!</green>", "InviteExpired": "%v <green>Your invite request to <grey>%v</grey> has expired.</green>", "CannotInviteSelf": "<red>You cannot invite yourself.</red>", "NoRequester": "<red>No one sent you a Duel request within the last minute. Please ask them to invite you with '/duel invite <player>'!</red>"}, "Sell": {"Success": "%v <green>You sold item(s) and gained <grey>%v</grey> doubloons.</green>", "CannotSell": "<red>You cannot sell this item.</red>"}, "Ping": {"Success": "OwO in eng"}, "Fly": {"Success": "%v <green>Successfully <green>%v</green> fly mode.</green>"}, "Bounty": {"Success": "%v <green>You added <grey>%v</grey> doubloons on <grey>%v</grey>'s head.</green>", "Error": "<red>You do not have sufficient funds to spend <grey>%v</grey> doubloons on a bounty</red>"}, "Pay": {"PaySuccess": "%v <green>You payed the player <grey>%v</grey> a total amount of <grey>%v</grey> doubloons.</green>", "ObtainSuccess": "%v <green>The player <grey>%v</grey> sent you a total amount of <grey>%v</grey> doubloons.</green>", "CannotPaySelf": "<red>You cannot pay yourself.</red>"}, "TPA": {"Request": "%v <green>Request sent to the player <grey>%v</grey>.</green>", "RequestReceive": "%v <green>The player <grey>%v</grey> is requesting to teleport to you. Please type '/tpa accept' <bold>within 60 seconds</bold> to accept their request.</green>", "HereRequest": "%v <green>The player <grey>%v</grey> is requesting that you get teleported to them. Please type '/tpa accept' <bold>within 60 seconds</bold> to accept their request.</green>", "Accept": "%v <green>The teleport is successful.</green>", "TimeoutSender": "%v <dark-yellow>The player <grey>%v</grey> ignored your tpa request.</dark-yellow>", "TimeoutTarget": "%v <dark-yellow>You ignored <grey>%v</grey>'s tpa request.</dark-yellow>"}, "WorldTools": {"PosSet": "%v <green>Position <grey>%v</grey> set to <grey>%v</grey></green>", "Wand": "%v <green>Added <emerald><bold>WT Wand</bold></emerald> to your inventory.</green>", "Fill": "%v <green>Filled blocks successfully.</green>", "Paste": "%v <green>Pasted successfully.</green>", "Replace": "%v <green>Replaced successfully</green>", "Rotate": "%v <green>Rotated Successfully</green>", "Mirror": "%v <green>Mirrored Successfully</green>", "Wall": "%v <green>Created walls successfully</green>", "Undo": "%v <green>Undid successfully</green>", "Redo": "%v <green>Redid successfully</green>", "Up": "%v <green>Poof!</green>", "Sphere": "%v <green>Sphere of radius <grey>%v</grey> created</green>", "Cylinder": "%v <green>Cylinder of radius <grey>%v</grey> and height <grey>%v</grey> created</green>", "Error": {"PosNotSet": "You did not set first or second position.", "BlockNotExist": "Block <grey>%v</grey> does not exist.", "NothingToUndo": "There is nothing to undo.", "NothingToRedo": "There is nothing to redo."}}}, "GiveShovel": "%v <green>You successfully received the Claim Shovel</green>", "ClaimRemoved": "%v <green>Successfully unclaimed this chunk.</green>", "ClaimSuccess": "%v <green>Successfully claimed chunk from <grey>%v</grey> till <grey>%v</grey>. You can still claim <grey>%v</grey> more chunks</green>", "ClaimStealSuccess": "%v <green>Successfully stole from <grey>%v</grey> and claimed the chunk from <grey>%v</grey> till <grey>%v</grey>.", "ShopPurchaseSuccess": "%v <green>You bought <grey>%v</grey> [<grey>%v</grey> pieces] for <grey>%v</grey> doubloons.</green>", "ClaimHead": "%v <green>You claimed <grey>%v</grey> from <grey>%v</grey>.</green>", "ClaimKit": "%v <green>You obtained the <grey>%v</grey>.</green>", "NoClaimArea": "%v <green>You did not claim an area for your faction yet! Use the <grey>%v</grey> to claim an area right now!</green>", "GiveBackpack": "%v <green>You were given a <grey>%v</grey>. Use it to store items.</green>", "DailyDoubloons": "%v <green>You earned <grey>%v</grey> daily doubloons & <grey>%v</grey> daily crate keys!</green>", "CombatLogged": "<red>You are now combat logged for 15 seconds. Please do not quit the server during that time or you will DIE.</red", "YouDied": "<bold><dark-red>YOU DIED!</dark-red></bold>", "ObtainedBankNote": "%v <green>You obtained a bank note worth <grey>%v</grey> doubloons.</green>", "ClaimedBankNote": "%v <green>You claimed a bank note worth <grey>%v</grey> doubloons</green>", "Whoosh": "<green>Whoosh!</green>", "MinedKey": "%v <green>You received a <grey>%v</grey> key as a reward for mining!</green>", "CombatMode": "<red>You cannot do this since you are still in combat for <grey>%v</grey> seconds!</red>", "WorldSaved": "%v <dark-aqua>World and player data saved in <grey>%v</grey> ms.</dark-aqua>", "ItemClearCooldown": "%v <red><bold><dark-red>WATCH OUT!</dark-red></bold> All items will clear in <grey>%v</grey>.</red>", "ItemsCleared": "%v <red><grey>%v</grey> items have been cleared from the ground.</red>", "DragonSpawn": "<bold><red>A DRAGON STARTED CAUSING TERROR AT <grey>%v</grey>.\nCOME PREPARED AND ATTACK FOR GOOD REWARDS!</red></bold>", "Enchants": {"FrostBite": "<red>You were hit by a Frost Bite attack</red>", "FrostShot": "<red>You were shot by a Frost Shot arrow</red>", "TarShot": "<red>You were shot by a Tar Shot arrow</red>"}, "FactionManage": {"Disband": "%v <green>You successfully disbanded your faction.</green>", "Leave": "%v <green>You successfully left the faction.</green>", "Create": {"Success": "%v <green>You have created a new faction called <grey>%v</grey>.</green>", "Error": {"BadLength": "<red>You must insert a name greater than 1 and smaller than 15.</red>", "ExistingName": "<red>This name is already taken. Choose another.</red>"}}, "Members": {"Kick": "%v <green>You successfully kicked <grey>%v</grey> from the faction.</green>", "Role": "%v <green>You successfully changed <grey>%v</grey>'s role to <grey>%v</grey></green>"}, "Request": {"Success": "%v <green>Your request has been sent.</green>", "Accepted": "%v <green>Your request has been accepted</green>", "Rejected": "%v <green>Your request has been rejected</green>", "Error": {"RequestReplaced": "<dark-yellow>The request to <grey>%v</grey> is replaced by the request you just submitted.</dark-yellow>", "EmptyFactionName": "<red>Please input a faction name.</red>", "FactionNotExist": "<red>The faction <grey>%v</grey> does not exist.</red>", "EmptyDescription": "<red>Please input a description [at least 10 characters].</red>", "ShortDescription": "<red>The description you input is too short [must be at least 10 characters].</red>", "AlreadyInFaction": "<red>You are already in a faction. You need to leave first before applying for another faction.</red>", "SameFaction": "<red>You cannot request alliance from the same faction you are already in.</red>"}}}, "Error": {"PlayerNotExist": "<red>Player does not exist! Make sure you spelt it correctly and is case sensitive.</red>", "InventoryFull": "<red>Your inventory is full! The items you were supposed to receive got dropped. Make some available space in your inventory!</red>", "CoolDown": "<red>You need to wait <dark-grey>%.2f</dark-grey> before doing this again!</red>", "NotInFaction": "<red>You must be a faction leader in order to do so.</red>", "OccupiedArea": "<red>You cannot do that here because it is claimed by the faction <grey>%v</grey>.</red", "CannotStealFactionClaim": "<red>The faction <grey>%v</grey> has strength <grey>%v</grey> which is higher than yours by <grey>%v</grey>. Grind for more strength points!</red>", "ClaimHub": "<red>You cannot claim blocks in the hub.</red", "ClaimInsufficient": "<red>Your faction does not have enough strength to claim more chunks.</red", "NotEnoughDoubloons": "<red>You do not have enough doubloons to buy that.</red>", "NotEnoughXP": "<red>You do not have enough XP to buy that.</red>", "RequestFailed": "<red>Request failed.</red>", "BlockProtected": "<red>You cannot place or break blocks here!</red>", "PvPDisabled": "<red>You cannot hit other players here. Drop into the war zone if you want to PvP!</red>", "NotSpawnerItem": "<red>You cannot use this item on a spawner. Please make sure you get a head item from a mob to insert it into the spawner!</red>", "NotEnoughExperience": "<red>You must have 20 experience levels to combine enchants.</red>", "NotCompatible": "<red>The enchant(s) are not compatible with this item.</red>"}}