package main

import (
	"github.com/ThronesMC/camera"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/player/chat"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	"server/server"
	"server/server/command/base"
	"server/server/command/worldedit"
	"server/server/database"
	"server/server/entity/mobs"
	"server/server/entity/mobs/boss"
	"server/server/factions/listeners"
	"server/server/language"
	"server/server/schedulers"
	"server/server/utils"

	_ "server/server/api"
)

func main() {
	chat.Global.Subscribe(chat.StdoutSubscriber{})
	log := slog.Default()

	schedulers.RegisterConfigurations()
	language.RegisterLanguages()
	schedulers.InitMongo(log)

	base.RegisterLobbyCommands()
	base.RegisterBuildCommands()
	base.RegisterFactionCommands()
	worldedit.RefreshBlocks()

	conf := utils.Panics(server.DefaultConfig().Config(log))
	conf.Entities = entity.DefaultRegistry.Config().New(utils.ConcatMultipleSlices[world.EntityType]([][]world.EntityType{
		{
			entity.AreaEffectCloudType,
			entity.ArrowType,
			entity.BottleOfEnchantingType,
			entity.EggType,
			entity.EnderPearlType,
			entity.ExperienceOrbType,
			entity.FallingBlockType,
			entity.FireworkType,
			entity.ItemType,
			entity.LightningType,
			entity.LingeringPotionType,
			entity.SnowballType,
			entity.SplashPotionType,
			entity.TNTType,
			entity.TextType,
		},
		{
			mobs.Zombie{},
			boss.Dragon{DragonIdentifier: "custom:dark_dragon"},
			boss.Dragon{DragonIdentifier: "custom:earth_dragon"},
			boss.Dragon{DragonIdentifier: "custom:fire_dragon"},
			boss.Dragon{DragonIdentifier: "custom:ice_dragon"},
			boss.Dragon{DragonIdentifier: "custom:light_dragon"},
			boss.Dragon{DragonIdentifier: "custom:water_dragon"},
		},
	}))
	srv := conf.New()
	srv.CloseOnProgramEnd()
	srv.Listen()
	srv.World().SetTime(1200)
	srv.World().StopTime()
	srv.World().StopWeatherCycle()
	srv.World().StopRaining()
	srv.World().StopThundering()
	srv.World().Handle(listeners.WorldHandler{})
	server.MCServer = srv

	go schedulers.ScheduleWorldSaving(conf)
	go schedulers.ScheduleItemsClearing()
	go schedulers.ScheduleDragonSpawning()

	schedulers.RefreshTextEntities()

	for pl := range srv.Accept() {
		camera.SendPresets(pl)

		pl.Handle(&listeners.FactionHandler{})
		listeners.HandlePlayerJoin(pl)
	}

	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		log.With("code", errorCode).With("identifier", identifier).Error(err.Error())
	}
}
