package language

type Config struct {
	Commands struct {
		Permission string
		Target     string

		Hub struct {
			Success string
		}

		GameMode struct {
			Success string
			Error   string
		}

		GiveRank struct {
			Success        string
			HierarchyError string
		}

		Bank struct {
			DepositSuccess         string
			ViewSuccess            string
			NotInFaction           string
			NotEnoughDoubloons     string
			NotEnoughBankDoubloons string
		}

		Home struct {
			SetSuccess    string
			DeleteSuccess string
		}

		Duel struct {
			InviteSuccess    string
			InviteSent       string
			InviteExpired    string
			CannotInviteSelf string
			NoRequester      string
		}

		Sell struct {
			Success    string
			CannotSell string
		}

		Ping struct {
			Success string
		}

		Fly struct {
			Success string
		}

		Bounty struct {
			Success string
			Error   string
		}

		Pay struct {
			PaySuccess    string
			ObtainSuccess string
			CannotPaySelf string
		}

		TPA struct {
			Request        string
			RequestReceive string
			HereRequest    string
			Accept         string
			TimeoutSender  string
			TimeoutTarget  string
		}

		WorldTools struct {
			PosSet   string
			Wand     string
			Fill     string
			Paste    string
			Replace  string
			Rotate   string
			Mirror   string
			Wall     string
			Undo     string
			Redo     string
			Up       string
			Sphere   string
			Cylinder string
			Error    struct {
				PosNotSet     string
				BlockNotExist string
				NothingToUndo string
				NothingToRedo string
			}
		}
	}

	GiveShovel          string
	ClaimRemoved        string
	ClaimSuccess        string
	ClaimStealSuccess   string
	ShopPurchaseSuccess string
	ClaimHead           string
	ClaimKit            string
	NoClaimArea         string
	GiveBackpack        string
	DailyDoubloons      string
	CombatLogged        string
	YouDied             string
	ObtainedBankNote    string
	ClaimedBankNote     string
	Whoosh              string
	MinedKey            string
	CombatMode          string
	WorldSaved          string
	ItemClearCooldown   string
	ItemsCleared        string
	DragonSpawn         string

	Enchants struct {
		FrostBite string
		FrostShot string
		TarShot   string
	}

	FactionManage struct {
		Create struct {
			Success string
			Error   struct {
				BadLength    string
				ExistingName string
			}
		}
		Members struct {
			Kick string
			Role string
		}
		Request struct {
			Success  string
			Accepted string
			Rejected string
			Error    struct {
				RequestReplaced  string
				EmptyFactionName string
				FactionNotExist  string
				EmptyDescription string
				ShortDescription string
				AlreadyInFaction string
				SameFaction      string
			}
		}
		Disband string
		Leave   string
	}

	Error struct {
		PlayerNotExist          string
		InventoryFull           string
		CoolDown                string
		NotInFaction            string
		OccupiedArea            string
		CannotStealFactionClaim string
		ClaimHub                string
		ClaimInsufficient       string
		NotEnoughDoubloons      string
		NotEnoughXP             string
		RequestFailed           string
		BlockProtected          string
		PvPDisabled             string
		NotSpawnerItem          string
		NotEnoughExperience     string
		NotCompatible           string
	}
}
