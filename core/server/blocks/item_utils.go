package blocks

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"image"
	"image/png"
	"os"
	"server/server/utils"
)

var specialItems = make(map[ItemAction]world.Item)

func RegisterSpecialItem(action ItemAction, it world.Item) {
	specialItems[action] = it
}

func SpecialItem(action ItemAction) world.Item {
	return specialItems[action]
}

type ClickType uint8

const (
	OnStartBreak ClickType = iota
	OnPunchAir
	OnItemUse
	OnItemUseOnBlock
	OnItemUseOnEntity
)

type ItemAction int

const (
	Wand ItemAction = iota
	ClaimShovel
	ThrowableTNT
	ThrowableNuke
	FakePearl
	PlayerHead
	MobHead
	Kit
	Backpack
	BankNote
)

func Texture(file string) image.Image {
	texture, err := os.OpenFile(file, os.O_RDONLY, os.ModePerm)
	if err != nil {
		panic(err)
	}
	defer func(texture *os.File) {
		err := texture.Close()
		if err != nil {

		}
	}(texture)
	img, err := png.Decode(texture)
	if err != nil {
		panic(err)
	}
	return img
}

func AddEnchantmentLore(i item.Stack) item.Stack {
	var lore []string

	for _, e := range i.Enchantments() {
		enchant := e.Type()
		if enchant.Rarity().Weight() == 0 {
			lore = append(lore, text.Colourf("<dark-purple>%s %s</dark-purple>", enchant.Name(), utils.IntToRoman(e.Level())))
		}
	}
	return i.WithLore(lore...)
}
