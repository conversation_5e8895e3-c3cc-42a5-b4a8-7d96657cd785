package schedulers

import (
	"github.com/df-mc/dragonfly/server"
	"log/slog"
	core "server/server"
	"server/server/database"
	"server/server/utils"
	"time"
)

func ScheduleWorldSaving(conf server.Config) {
	for range time.NewTicker(1 * time.Minute).C {
		core.MCServer.World().Save()

		for identifier, err := range database.DB.SaveAll() {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("identifier", identifier).Error(err.Error())
		}

		for pl := range core.MCServer.Players(nil) {
			utils.Panic(conf.PlayerProvider.Save(pl.UUID(), pl.Data(), core.MCServer.World()))
			//pl.Message(text.Colourf(language.Translate(pl).WorldSaved, core.Config.Prefix, time.Now().Sub(old).Milliseconds()))
		}
	}
}
