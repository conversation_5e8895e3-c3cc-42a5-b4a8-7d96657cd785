package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	core "server/server"
	"server/server/database"
	"server/server/utils"
)

type StopCommand struct{}

func (StopCommand) Allow(src cmd.Source) bool {
	return Stop.Test(src)
}

func (StopCommand) PermissionMessage(src cmd.Source) string {
	return Stop.PermissionMessage(src)
}

func (StopCommand) Run(_ cmd.Source, _ *cmd.Output, tx *world.Tx) {
	tx.World().Save()
	utils.Panic(core.MCServer.Close())

	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("identifier", identifier).Error(err.Error())
	}
}
