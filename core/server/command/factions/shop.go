package factions

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math"
	"server/server"
	"server/server/blocks/vanilla"
	"server/server/factions/items"
	"server/server/factions/spawner"
	"server/server/language"
	"server/server/ui"
	"server/server/user"
	"server/server/utils"
	"strconv"
	_ "unsafe"
)

type ShopCommand struct{}

func (ShopCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		ShopUI{}.SendTo(pl, Shop())
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type ItemType int

type BuyableItem struct {
	Name         string
	BuyableItems []*BuyableItem
	Item         item.Stack
	SellPrice    float64
	BuyPrice     float64
	BuyXP        int
}

func (bt *BuyableItem) IsCategory() bool {
	return len(bt.BuyableItems) > 0
}

func (bt *BuyableItem) Equals(b *BuyableItem) bool {
	if bt.IsCategory() {
		return bt.Name == b.Name
	}
	return bt.Item.Equal(b.Item)
}

func Shop() *BuyableItem {
	var flowers []*BuyableItem
	for _, ft := range block.FlowerTypes() {
		flowers = append(flowers, &BuyableItem{Item: item.NewStack(block.Flower{Type: ft}, 1).WithCustomName(ft.Name()), SellPrice: 0, BuyPrice: 5})
	}

	var eBooks []*BuyableItem
	for _, ench := range item.Enchantments() {
		if id, ok := item.EnchantmentID(ench); ok && id > 1000 { // 1000 is ID of glitter ebook which is only for internal use
			eBooks = append(eBooks, &BuyableItem{
				Item:      item.NewStack(item.EnchantedBook{}, 1).WithEnchantments(item.NewEnchantment(ench, 1)).WithCustomName(text.Colourf("<amethyst>%v</amethyst> Enchanted Book", ench.Name())),
				SellPrice: 0,
				BuyXP:     15,
				BuyPrice:  50000,
			})
		}
	}

	return &BuyableItem{Name: "Shop", BuyableItems: []*BuyableItem{
		{Name: "Blocks", BuyableItems: []*BuyableItem{
			{Name: "Terrain", BuyableItems: []*BuyableItem{
				{Item: item.NewStack(block.Stone{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Chest{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.EnderChest{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.CraftingTable{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Dirt{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Grass{}, 1).WithCustomName("Grass"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Gravel{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Sand{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Sandstone{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Netherrack{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Clay{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Purpur{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Quartz{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Cobblestone{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Andesite{}, 1).WithCustomName("Andesite"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Diorite{}, 1).WithCustomName("Diorite"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Granite{}, 1).WithCustomName("Granite"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Prismarine{}, 1), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.RawCopper{}, 1), SellPrice: 25, BuyPrice: 30},
				{Item: item.NewStack(block.EndStone{}, 1), SellPrice: 25, BuyPrice: 30},
				{Item: item.NewStack(vanilla.Ice{}, 1), SellPrice: 25, BuyPrice: 30},
				{Item: item.NewStack(block.Snow{}, 1), SellPrice: 25, BuyPrice: 30},
				{Item: item.NewStack(vanilla.Magma{}, 1), SellPrice: 100, BuyPrice: 200},
				{Item: item.NewStack(block.Amethyst{}, 1), SellPrice: 300, BuyPrice: 250},
				{Item: item.NewStack(block.Obsidian{}, 1), SellPrice: 8500, BuyPrice: 450},
				{Item: item.NewStack(block.Bedrock{}, 1), SellPrice: 12000, BuyPrice: 10000},
			}},
			{Name: "Orders & Drops", BuyableItems: []*BuyableItem{
				{Item: item.NewStack(item.Coal{}, 1), SellPrice: 25, BuyPrice: 50},
				{Item: item.NewStack(item.Slimeball{}, 1), SellPrice: 25, BuyPrice: 50},
				{Item: item.NewStack(item.Bone{}, 1), SellPrice: 25, BuyPrice: 50},
				{Item: item.NewStack(item.IronIngot{}, 1), SellPrice: 50, BuyPrice: 50},
				{Item: item.NewStack(item.GoldIngot{}, 1), SellPrice: 75, BuyPrice: 150},
				{Item: item.NewStack(item.Diamond{}, 1), SellPrice: 100, BuyPrice: 200},
				{Item: item.NewStack(item.Emerald{}, 1), SellPrice: 150, BuyPrice: 300},
			}},
			{Name: "Logs", BuyableItems: []*BuyableItem{
				{Item: item.NewStack(block.Log{Wood: block.OakWood()}, 1).WithCustomName("Oak Wood"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Log{Wood: block.SpruceWood()}, 1).WithCustomName("Spruce Wood"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Log{Wood: block.CherryWood()}, 1).WithCustomName("Cherry"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Log{Wood: block.JungleWood()}, 1).WithCustomName("Jungle Wood"), SellPrice: 10, BuyPrice: 15},
				{Item: item.NewStack(block.Log{Wood: block.AcaciaWood()}, 1).WithCustomName("Acacia Wood"), SellPrice: 10, BuyPrice: 15},
			}},
			{Name: "Decorations", BuyableItems: []*BuyableItem{
				{Name: "Glass", BuyableItems: []*BuyableItem{
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourWhite()}, 1).WithCustomName("White Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourOrange()}, 1).WithCustomName("Orange Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourMagenta()}, 1).WithCustomName("Magenta Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourLightBlue()}, 1).WithCustomName("Light Blue Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourYellow()}, 1).WithCustomName("Yellow Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourLime()}, 1).WithCustomName("Lime Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourPink()}, 1).WithCustomName("Pink Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourGrey()}, 1).WithCustomName("Grey Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourLightGrey()}, 1).WithCustomName("Light Grey Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourCyan()}, 1).WithCustomName("Cyan Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourPurple()}, 1).WithCustomName("Purple Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourBlue()}, 1).WithCustomName("Blue Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourBrown()}, 1).WithCustomName("Brown Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourGreen()}, 1).WithCustomName("Green Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourRed()}, 1).WithCustomName("Red Stained Glass"), SellPrice: 0, BuyPrice: 40},
					{Item: item.NewStack(block.StainedGlass{Colour: item.ColourBlack()}, 1).WithCustomName("Black Stained Glass"), SellPrice: 0, BuyPrice: 40},
				}},
				{Name: "Concrete", BuyableItems: []*BuyableItem{
					{Item: item.NewStack(block.Concrete{Colour: item.ColourWhite()}, 1).WithCustomName("White Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourOrange()}, 1).WithCustomName("Orange Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourMagenta()}, 1).WithCustomName("Magenta Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourLightBlue()}, 1).WithCustomName("Light Blue Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourYellow()}, 1).WithCustomName("Yellow Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourLime()}, 1).WithCustomName("Lime Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourPink()}, 1).WithCustomName("Pink Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourGrey()}, 1).WithCustomName("Grey Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourLightGrey()}, 1).WithCustomName("Light Grey Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourCyan()}, 1).WithCustomName("Cyan Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourPurple()}, 1).WithCustomName("Purple Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourBlue()}, 1).WithCustomName("Blue Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourBrown()}, 1).WithCustomName("Brown Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourGreen()}, 1).WithCustomName("Green Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourRed()}, 1).WithCustomName("Red Concrete"), SellPrice: 0, BuyPrice: 60},
					{Item: item.NewStack(block.Concrete{Colour: item.ColourBlack()}, 1).WithCustomName("Black Concrete"), SellPrice: 0, BuyPrice: 60},
				}},
				{Name: "Terracotta", BuyableItems: []*BuyableItem{
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourWhite()}, 1).WithCustomName("White Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourOrange()}, 1).WithCustomName("Orange Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourMagenta()}, 1).WithCustomName("Magenta Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourLightBlue()}, 1).WithCustomName("Light Blue Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourYellow()}, 1).WithCustomName("Yellow Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourLime()}, 1).WithCustomName("Lime Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourPink()}, 1).WithCustomName("Pink Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourGrey()}, 1).WithCustomName("Grey Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourLightGrey()}, 1).WithCustomName("Light Grey Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourCyan()}, 1).WithCustomName("Cyan Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourPurple()}, 1).WithCustomName("Purple Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourBlue()}, 1).WithCustomName("Blue Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourBrown()}, 1).WithCustomName("Brown Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourGreen()}, 1).WithCustomName("Green Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourRed()}, 1).WithCustomName("Red Terracotta"), SellPrice: 0, BuyPrice: 50},
					{Item: item.NewStack(block.StainedTerracotta{Colour: item.ColourBlack()}, 1).WithCustomName("Black Terracotta"), SellPrice: 0, BuyPrice: 50},
				}},
				{Name: "Flowers", BuyableItems: flowers},
				{Item: item.NewStack(block.Lantern{}, 1), SellPrice: 0, BuyPrice: 10},
				{Item: item.NewStack(block.SeaLantern{}, 1), SellPrice: 0, BuyPrice: 10},
				{Item: item.NewStack(block.Glowstone{}, 1), SellPrice: 0, BuyPrice: 10},
				{Item: item.NewStack(block.Chain{}, 1), SellPrice: 0, BuyPrice: 30},
				{Item: item.NewStack(block.Honeycomb{}, 1), SellPrice: 0, BuyPrice: 35},
				{Item: item.NewStack(block.Bookshelf{}, 1), SellPrice: 0, BuyPrice: 65},
			}},
		}},
		{Name: "Food & Farming", BuyableItems: []*BuyableItem{
			{Name: "Seeds", BuyableItems: []*BuyableItem{
				{Item: item.NewStack(block.BeetrootSeeds{}, 1), SellPrice: 15, BuyPrice: 20},
				{Item: item.NewStack(block.WheatSeeds{}, 1), SellPrice: 20, BuyPrice: 25},
				{Item: item.NewStack(block.PumpkinSeeds{}, 1), SellPrice: 25, BuyPrice: 30},
				{Item: item.NewStack(block.MelonSeeds{}, 1), SellPrice: 30, BuyPrice: 35},
				{Item: item.NewStack(block.NetherWart{}, 1), SellPrice: 100, BuyPrice: 150},
			}},
			{Item: item.NewStack(block.Cactus{}, 1), SellPrice: 100, BuyPrice: 300},
			{Item: item.NewStack(block.SugarCane{}, 1), SellPrice: 50, BuyPrice: 75},
			{Item: item.NewStack(item.Beef{Cooked: true}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(block.Carrot{}, 1), SellPrice: 4, BuyPrice: 8},
			{Item: item.NewStack(item.Rabbit{Cooked: true}, 1), SellPrice: 5, BuyPrice: 111},
			{Item: item.NewStack(block.Potato{}, 1), SellPrice: 10, BuyPrice: 20},
			{Item: item.NewStack(block.Cake{}, 1), SellPrice: 15, BuyPrice: 30},
			{Item: item.NewStack(item.Beef{}, 1), SellPrice: 15, BuyPrice: 20},
			{Item: item.NewStack(item.RottenFlesh{}, 1), SellPrice: 15, BuyPrice: 20},
			{Item: item.NewStack(item.Wheat{}, 1), SellPrice: 15, BuyPrice: 20},
			{Item: item.NewStack(block.Melon{}, 1), SellPrice: 15, BuyPrice: 20},
			{Item: item.NewStack(item.Porkchop{}, 1), SellPrice: 15, BuyPrice: 20},
			{Item: item.NewStack(block.Pumpkin{}, 1), SellPrice: 25, BuyPrice: 30},
			{Item: item.NewStack(item.GoldenApple{}, 1), SellPrice: 30, BuyPrice: 60},
			{Item: item.NewStack(item.PumpkinPie{}, 1), SellPrice: 75, BuyPrice: 150},
			{Item: item.NewStack(item.EnchantedApple{}, 1), SellPrice: 0, BuyPrice: 250},
			{Item: item.NewStack(item.GoldenCarrot{}, 1), SellPrice: 500, BuyPrice: 1000},
		}},
		{Name: "Enchanted Books", BuyableItems: eBooks},
		{Name: "Special Items & Raiding", BuyableItems: []*BuyableItem{
			{Item: item.NewStack(block.Torch{}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(item.Arrow{}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(block.ItemFrame{}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(item.EnderPearl{}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(block.Hopper{}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(block.TNT{}, 1), SellPrice: 1562, BuyPrice: 3125},
			{Item: item.NewStack(item.FlintAndSteel{}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(item.Bucket{Content: item.LiquidBucketContent(block.Water{})}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(item.Bucket{Content: item.LiquidBucketContent(block.Lava{})}, 1), SellPrice: 0, BuyPrice: 25},
			{Item: item.NewStack(spawner.Spawner{}, 1), SellPrice: 250000, BuyPrice: 500000},
			{Item: items.FakePearl{}.Stack(), SellPrice: 0, BuyPrice: 5000},
			{Item: items.ThrowableTNT{}.Stack(), SellPrice: 0, BuyPrice: 30000},
			{Item: items.ThrowableNuke{}.Stack(), SellPrice: 0, BuyPrice: 1500000},
		}},
	}}
}

func shopReverse(searchFor *BuyableItem) *BuyableItem {
	return shopReverseHelper(Shop(), searchFor)
}

func shopReverseHelper(bt *BuyableItem, searchFor *BuyableItem) *BuyableItem {
	for _, b := range bt.BuyableItems {
		if b.Equals(searchFor) {
			return bt
		}
	}
	for _, b := range bt.BuyableItems {
		if bt.IsCategory() {
			if foundBt := shopReverseHelper(b, searchFor); foundBt != nil {
				return foundBt
			}
		}
	}
	return nil
}

func ItemFinder(searchFor item.Stack) *BuyableItem {
	return itemFinderHelper(Shop(), searchFor)
}

func itemFinderHelper(bt *BuyableItem, searchFor item.Stack) *BuyableItem {
	if !bt.IsCategory() && bt.Item.Comparable(searchFor) {
		return bt
	}
	for _, b := range bt.BuyableItems {
		if bt.IsCategory() {
			if foundBt := itemFinderHelper(b, searchFor); foundBt != nil {
				return foundBt
			}
		}
	}
	return nil
}

type ShopUI struct {
	bt *BuyableItem
}

func NewShopUI(bt *BuyableItem) ShopUI {
	return ShopUI{bt: bt}
}

func (s ShopUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	bt := ui.Load[*BuyableItem](pl, button.Text)
	if bt.IsCategory() {
		ShopUI{}.SendTo(pl, bt)
	} else {
		u := user.GetUser(pl)
		if u.Data.Faction.Stats.Doubloons < bt.BuyPrice {
			pl.Message(text.Colourf(language.Translate(pl).Error.NotEnoughDoubloons))
			return
		}

		if pl.Experience() < bt.BuyXP {
			pl.Message(text.Colourf(language.Translate(pl).Error.NotEnoughXP))
			return
		}
		BuyAmountUI{}.SendTo(pl, bt)
	}
}

func (s ShopUI) Close(submitter form.Submitter, _ *world.Tx) {
	if shopReverse(s.bt) != nil {
		ShopUI{}.SendTo(submitter.(*player.Player), shopReverse(s.bt))
	}
}

func (s ShopUI) SendTo(pl *player.Player, bts *BuyableItem) {
	fm := form.NewMenu(NewShopUI(bts), text.Colourf("<green>Factions Shop</green>"))
	for _, bt := range bts.BuyableItems {
		if bt.IsCategory() {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>%v</gold>", bt.Name), "", bt))
		} else {
			xpPriceText := ""
			if bt.BuyXP > 0 {
				xpPriceText = " and " + strconv.Itoa(bt.BuyXP) + " XP"
			}
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<yellow>%v</yellow>\n<dark-grey><italic>Buy for %v dubbs%v</italic></dark-grey>", utils.ItemDisplay(bt.Item), utils.ShortenNumber(bt.BuyPrice, 2), xpPriceText), "", bt))
		}
	}

	pl.SendForm(fm)
}

type BuyAmountUI struct {
	bt *BuyableItem

	Amount form.Slider
}

func NewBuyAmountUI(u *user.User, bt *BuyableItem) BuyAmountUI {
	return BuyAmountUI{
		bt:     bt,
		Amount: form.NewSlider("How much do you want to buy?", 1, math.Min(u.Data.Faction.Stats.Doubloons/bt.BuyPrice, 256), 1, 1),
	}
}

func (ba BuyAmountUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	a := int(ba.Amount.Value())

	if _, err := pl.Inventory().AddItem(ba.bt.Item.Grow(a - 1)); err != nil {
		pl.Drop(ba.bt.Item.Grow(a - 1))
		pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
	}

	u.Data.Faction.Stats.Doubloons -= ba.bt.BuyPrice * float64(a)
	pl.RemoveExperience(ba.bt.BuyXP * a)
	pl.Message(text.Colourf(language.Translate(pl).ShopPurchaseSuccess, server.Config.Prefix, utils.ItemDisplay(ba.bt.Item), a, utils.ShortenNumber(ba.bt.BuyPrice*float64(a), 0)))
}

func (ba BuyAmountUI) Close(submitter form.Submitter, _ *world.Tx) {
	ShopUI{}.SendTo(submitter.(*player.Player), shopReverse(ba.bt))
}

func (ba BuyAmountUI) SendTo(pl *player.Player, bt *BuyableItem) {
	fm := form.New(NewBuyAmountUI(user.GetUser(pl), bt), text.Colourf("<green>Select an amount</green>"))
	pl.SendForm(fm)
}
