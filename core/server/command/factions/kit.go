package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/database"
	"server/server/factions/items"
	"server/server/language"
	"server/server/ui"
	"server/server/user"
	"time"
)

type KitCommand struct{}

func (KitCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		KitUI{pl: pl}.SendTo(pl)
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type KitUI struct {
	pl *player.Player

	Beginner form.Button
	VIP      form.Button
	MVP      form.Button
	MMP      form.Button
	MLP      form.Button
	MGP      form.Button
}

func NewKitUI(pl *player.Player) KitUI {
	k := KitUI{
		pl:       pl,
		Beginner: ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Beginner</dark-grey> <green>Kit</green>\n%v", kitText(pl, database.Player)), "", database.Player),
		VIP:      ui.AddButtonWithValue(pl, text.Colourf("%v<green>Kit</green>\n%v", database.VIP.Prefix(), kitText(pl, database.VIP)), "", database.VIP),
		MVP:      ui.AddButtonWithValue(pl, text.Colourf("%v<green>Kit</green>\n%v", database.MVP.Prefix(), kitText(pl, database.MVP)), "", database.MVP),
		MMP:      ui.AddButtonWithValue(pl, text.Colourf("%v<green>Kit</green>\n%v", database.MMP.Prefix(), kitText(pl, database.MMP)), "", database.MMP),
		MLP:      ui.AddButtonWithValue(pl, text.Colourf("%v<green>Kit</green>\n%v", database.MLP.Prefix(), kitText(pl, database.MLP)), "", database.MLP),
		MGP:      ui.AddButtonWithValue(pl, text.Colourf("%v<green>Kit</green>\n%v", database.MGP.Prefix(), kitText(pl, database.MGP)), "", database.MGP),
	}
	return k
}

func kitText(pl *player.Player, r database.Rank) string {
	if !KitFromRank(r).Test(pl) {
		return text.Colourf("<italic><dark-red>Required rank | Buy at <store-link>!</dark-red></italic>") //TODO Put store link
	}
	t := user.GetUser(pl).Data.Faction.Stats.Kits[r]
	if t.Before(time.Now()) {
		return text.Colourf("<italic><green>Click to obtain!</green></italic>")
	}
	return text.Colourf("<italic><dark-red>Cooldown: %v</dark-red></italic>", t.String())
}

func (k KitUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	r := ui.Load[database.Rank](pl, button.Text)
	t := user.GetUser(pl).Data.Faction.Stats.Kits[r]
	if !KitFromRank(r).Test(pl) || !t.Before(time.Now()) {
		pl.Message(text.Colourf(language.Translate(pl).Error.RequestFailed))
		return
	}

	if _, err := pl.Inventory().AddItem(items.Kit{Type: r}.Stack()); err != nil {
		pl.Drop(items.Kit{Type: r}.Stack())
		pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
	}

	user.GetUser(pl).Data.Faction.Stats.Kits[r] = time.Now().Add(7 * time.Hour)
	pl.Message(text.Colourf(language.Translate(pl).ClaimKit, server.Config.Prefix, items.Kit{Type: r}.Stack().CustomName()))
}

func (k KitUI) SendTo(pl *player.Player) {
	fm := form.NewMenu(NewKitUI(pl), text.Colourf("<green>Kit Menu</green>"))
	pl.SendForm(fm)
}

func KitFromRank(r database.Rank) command.Permission {
	switch r {
	case database.Player:
		return command.BeginnerKit
	case database.VIP:
		return command.VIPKit
	case database.MVP:
		return command.MVPKit
	case database.MMP:
		return command.MMPKit
	case database.MLP:
		return command.MLPKit
	case database.MGP:
		return command.MGPKit
	default:
		panic("Unknown rank")
	}
}
