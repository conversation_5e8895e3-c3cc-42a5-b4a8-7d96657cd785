package listeners

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/enchantment"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/chat"
	"github.com/df-mc/dragonfly/server/player/title"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/protocol"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math"
	"math/rand"
	"server/server"
	items2 "server/server/blocks"
	"server/server/database"
	"server/server/entity/mobs"
	"server/server/factions"
	"server/server/factions/backpack"
	"server/server/factions/items"
	"server/server/factions/ui"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
	"slices"
	"time"
)

type FactionHandler struct {
	player.NopHandler
}

func (h FactionHandler) HandleHurt(ctx *player.Context, damage *float64, _ bool, attackImmunity *time.Duration, src world.DamageSource) {
	pl := ctx.Val()
	u := user.GetUser(pl)

	if _, ok := src.(entity.FallDamageSource); ok {
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.NoPvp.C1
	c2 := server.Config.Hub.NoPvp.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pl.Position()) {
		ctx.Cancel()
		pl.Message(text.Colourf(language.Translate(pl).Error.PvPDisabled))
		return
	}

	var killer world.Entity
	if s1, ok := src.(entity.AttackDamageSource); ok {
		killer = s1.Attacker
	} else if s2, ok := src.(entity.ProjectileDamageSource); ok {
		killer = s2.Owner
	}

	if killer, ok := killer.(*player.Player); ok {
		uk := user.GetUser(killer)
		if u.Data.Faction.HasFaction() && uk.Data.Faction.HasFaction() && u.Data.Faction.Faction() == uk.Data.Faction.Faction() {
			return
		}

		if !u.IsCoolDownActive(user.Combat, 15*time.Second, true, true, false) {
			killer.Message(text.Colourf(language.Translate(killer).CombatLogged))
			pl.Message(text.Colourf(language.Translate(pl).CombatLogged))
		}
	}

	if pl.Health()+pl.Absorption() <= *damage {
		up := user.GetUser(pl)
		up.Data.Faction.Stats.Deaths++
		up.Data.Faction.Stats.BestKillStreak = max(up.Data.Faction.Stats.KillStreak, up.Data.Faction.Stats.BestKillStreak)
		up.Data.Faction.Stats.KillStreak = 0

		performFactionDeath(pl)

		if _, err := pl.Inventory().AddItem(backpack.Backpack{}.Stack()); err == nil {
			pl.Message(text.Colourf(language.Translate(pl).GiveBackpack, server.Config.Prefix, backpack.Backpack{}.Stack().CustomName()))
		}

		pl.Heal(pl.MaxHealth(), nil)
		pl.SetFood(20)
		for _, e := range pl.Effects() {
			pl.RemoveEffect(e.Type())
		}
		pl.Extinguish()
		pl.RemoveExperience(pl.Experience())
		pl.RemoveBossBar()
		pl.SendTitle(title.New(text.Colourf(language.Translate(pl).YouDied)))
		if killer, ok := killer.(*player.Player); ok {
			uk := user.GetUser(killer)
			uk.Data.Faction.Stats.Strength += 0.5
			if uk.Data.Faction.HasFaction() {
				uk.Data.Faction.Faction().Strength += 5
			}
			uk.Data.Faction.Stats.Kills++
			uk.Data.Faction.Stats.KillStreak++
			if _, err := killer.Inventory().AddItem(items.PlayerHead{KilledUuid: pl.UUID()}.Stack()); err != nil {
				killer.Message(text.Colourf(language.Translate(killer).Error.InventoryFull))
			}

			if up.Data.Faction.Stats.Strength >= 0.5 {
				up.Data.Faction.Stats.Strength -= 0.5
			}
			if up.Data.Faction.HasFaction() && up.Data.Faction.Faction().Strength >= 10 {
				up.Data.Faction.Faction().Strength -= 10
			}
		}
		ctx.Cancel()
	}

	EnchantsHandleHurt(ctx, damage, attackImmunity, src)
}

func performFactionDeath(pl *player.Player) {
	pos := pl.Position()
	for _, orb := range entity.NewExperienceOrbs(pos, int(math.Min(float64(pl.ExperienceLevel()*7), 100))) {
		pl.Tx().AddEntity(orb)
	}
	pl.SetExperienceLevel(0)
	utils.Session(pl).SendExperience(pl.ExperienceLevel(), pl.ExperienceProgress())

	pl.MoveItemsToInventory()
	for _, it := range append(pl.Inventory().Clear(), pl.Armour().Clear()...) {
		if _, ok := it.Enchantment(enchantment.CurseOfVanishing); ok {
			continue
		}
		opts := world.EntitySpawnOpts{Position: pos, Velocity: mgl64.Vec3{rand.Float64()*0.2 - 0.1, 0.2, rand.Float64()*0.2 - 0.1}}
		pl.Tx().AddEntity(entity.NewItem(opts, it))
	}

	pl.Teleport(server.Config.Hub.SpawnPoint)
}

func (h FactionHandler) HandleQuit(pl *player.Player) {
	u := user.GetUser(pl)
	u.Data.LastLogin = time.Now()
	u.Data.Online = false
	user.Save(pl)

	if u.CoolDownTimeRemaining(user.Combat) > 0 {
		pl.Hurt(10000, entity.VoidDamageSource{})
	}
}

func (h FactionHandler) HandleChat(ctx *player.Context, msg *string) {
	pl := ctx.Val()
	u := user.GetUser(pl)

	ctx.Cancel()
	*msg = text.Colourf("%v<white>:</white> %v", user.FactionNameDisplay.Name(u.Data), *msg)
	_, _ = fmt.Fprintf(chat.Global, *msg)
}

func CheckPlayerChangeTerritory(u *user.User, pos cube.Pos) bool {
	facWithin := database.FactionWithin(pos.Vec3())
	if facWithin != nil {
		if u.Data.Faction.HasFaction() && u.Data.Faction.Name != facWithin.Name {
			return false
		} else {
			return u.Data.Faction.Stats.Strength > facWithin.Strength
		}
	}
	return true
}

func (h FactionHandler) HandleItemUse(ctx *player.Context) {
	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.IsCoolDownActive(user.Use, 50*time.Millisecond, false, true, false) {
		return
	}

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok && main.Count() != 0 {
		if _, ok := main.Value("wild_only"); ok {
			if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
				pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
				ctx.Cancel()
				return
			}

			c1 := server.Config.Hub.BlockProtectionZone.C1
			c2 := server.Config.Hub.BlockProtectionZone.C2
			box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
			if box.Vec3Within(pl.Position()) && pl.GameMode() != world.GameModeCreative {
				pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
				ctx.Cancel()
				return
			}
		}

		if uit, ok := items2.SpecialItem(items2.ItemAction(v.(int16))).(item.Usable); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.Use(pl.Tx(), pl, &useCtx)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
	}
}

func (h FactionHandler) HandleItemUseOnBlock(ctx *player.Context, pos cube.Pos, face cube.Face, clickPos mgl64.Vec3) {
	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.IsCoolDownActive(user.Use, 50*time.Millisecond, false, true, false) {
		return
	}

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok && main.Count() != 0 {
		if _, ok := main.Value("wild_only"); ok {
			if v, ok := main.Value("special_item"); (!ok || v != int16(items2.ClaimShovel)) && !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
				pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
				ctx.Cancel()
				return
			}

			c1 := server.Config.Hub.BlockProtectionZone.C1
			c2 := server.Config.Hub.BlockProtectionZone.C2
			box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
			if box.Vec3Within(pl.Position()) && pl.GameMode() != world.GameModeCreative {
				pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
				ctx.Cancel()
				return
			}
		}

		if uit, ok := items2.SpecialItem(items2.ItemAction(v.(int16))).(item.UsableOnBlock); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.UseOnBlock(pos, face, clickPos, pl.Tx(), pl, &useCtx)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
	}

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	b := pl.Tx().Block(pos)
	if _, ok := b.(block.EnderChest); ok {
		var ct *database.CrateType
		for _, t := range []database.CrateType{database.Common, database.Rare, database.Legendary, database.Ancient} {
			if equal(pos.Vec3(), server.Config.Hub.Crates[t], 0.5) {
				ct = &t
				break
			}
		}

		if ct != nil {
			ui.SendCrateTo(pl, ct)
			ctx.Cancel()
		}
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	if _, ok := main.Item().(item.Firework); ok {
		if u.CoolDownTimeRemaining(user.Combat) > 0 {
			ctx.Cancel()
		}
	}
}

func (h FactionHandler) HandleItemUseOnEntity(ctx *player.Context, e world.Entity) {
	pl := ctx.Val()

	if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(e.Position())) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(e.Position()).Name))
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(e.Position()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleHeldSlotChange(ctx *player.Context, from, to int) {
	pl := ctx.Val()
	u := user.GetUser(pl)
	toStack, _ := pl.Inventory().Item(to)
	fromStack, _ := pl.Inventory().Item(from)

	if v, ok := toStack.Value("special_item"); ok && v == int16(items2.ClaimShovel) && u.Data.Faction.HasFaction() {
		u.Data.Faction.Faction().RefreshChunkLines(pl)
	} else if v, ok := fromStack.Value("special_item"); ok && v == int16(items2.ClaimShovel) && u.Data.Faction.HasFaction() {
		utils.Session(pl).RemoveAllDebugShapes()
	}
}

func (h FactionHandler) HandleStartBreak(ctx *player.Context, pos cube.Pos) {
	pl := ctx.Val()

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok {
		ctx.Cancel()

		if uit, ok := items2.SpecialItem(items2.ItemAction(v.(int16))).(ActivatedOnStartBreak); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.OnStartBreak(pl, pos)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
	}

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
	pl := ctx.Val()
	u := user.GetUser(pl)

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	if !slices.Contains(u.FactionInfo.IgnoreDrilledBlocksAt, pos.Vec3()) {
		EnchantsHandleBlockBreak(ctx, pos, drops, xp)
	}
}

func (h FactionHandler) HandleBlockPlace(ctx *player.Context, pos cube.Pos, _ world.Block) {
	pl := ctx.Val()

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	c1 := server.Config.Hub.BlockProtectionZone.C1
	c2 := server.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleMove(ctx *player.Context, _ mgl64.Vec3, _ cube.Rotation) {
	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.CoolDownTimeRemaining(user.Combat) > 0 {
		if pl.Gliding() {
			pl.StopGliding()
			pl.SetVelocity(mgl64.Vec3{0, 0, 0})
		}

		if pl.Flying() {
			pl.StopFlying()
			pl.SetGameMode(world.GameModeSurvival)
			pl.SetVelocity(mgl64.Vec3{0, 0, 0})
		}
	}
}

func HandlePlayerJoin(pl *player.Player) {
	u := user.GetUser(pl)
	defer user.Save(pl)

	u.Data.Online = true

	if pl.Name() == "AlphICEter" || pl.Name() == "Studgi" {
		u.Data.RankId = database.Owner.Shortened()
	}

	pl.Teleport(server.Config.Hub.SpawnPoint)
	pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
	pl.SetGameMode(world.GameModeSurvival)
	pl.ShowCoordinates()

	giveMissingItems(pl)
	giveDailyDoubloons(pl)
	go updateCrateText(pl)
	go spawnMob(pl)
}

func giveMissingItems(pl *player.Player) {
	u := user.GetUser(pl)

	if u.Data.Faction.HasFaction() && u.Data.Faction.Role == database.Leader {
		if !pl.Inventory().ContainsItem(items.ClaimShovel{}.Stack()) {
			if _, err := pl.Inventory().AddItem(items.ClaimShovel{}.Stack()); err == nil {
				pl.Message(text.Colourf(language.Translate(pl).NoClaimArea, server.Config.Prefix, items.ClaimShovel{}.Stack().CustomName()))
			}
		}
	}

	if !pl.Inventory().ContainsItem(backpack.Backpack{}.Stack()) {
		if _, err := pl.Inventory().AddItem(backpack.Backpack{}.Stack()); err == nil {
			pl.Message(text.Colourf(language.Translate(pl).GiveBackpack, server.Config.Prefix, backpack.Backpack{}.Stack().CustomName()))
		}
	}
}

func giveDailyDoubloons(pl *player.Player) {
	u := user.GetUser(pl)
	offlineMul := (int)(time.Now().Sub(u.Data.LastLogin).Seconds() / (time.Hour * 24).Seconds())
	var dailyDoubloons int
	switch u.Data.Rank() {
	case database.MGP, database.MLP:
		dailyDoubloons = 500000
	case database.MMP:
		dailyDoubloons = 450000
	case database.MVP:
		dailyDoubloons = 350000
	case database.VIP:
		dailyDoubloons = 250000
	default:
		dailyDoubloons = 0
	}
	dailyDoubloons *= offlineMul
	u.Data.Faction.Stats.Doubloons += float64(dailyDoubloons)
	for i := 0; i < offlineMul; i++ {
		u.Data.Faction.Stats.CrateKeys[database.RandCrateType()]++
	}

	if dailyDoubloons != 0 {
		pl.Message(text.Colourf(language.Translate(pl).DailyDoubloons, server.Config.Prefix, dailyDoubloons, offlineMul))
	}
}

func updateCrateText(pl *player.Player) {
	for range time.NewTicker(5 * time.Second).C {
		u := user.GetUser(pl)
		if u == nil {
			break
		}
		factions.SendMainScoreboard(pl)

		for t := range server.Config.Hub.Crates {
			p := server.Config.Hub.Crates[t].Add(mgl64.Vec3{0, 1.25, 0})
			pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
				for e := range tx.Entities() {
					if ent, ok := e.(*entity.Ent); ok {
						if e.H().Type() == entity.TextType && ent.Position() == p {
							ct := database.CrateType(t)

							session := utils.Session(pl)
							md := utils.ParseEntityMetadata(session, e)
							md[protocol.EntityDataKeyName] = text.Colourf("%v\n<dark-green>You have <dark-grey>%v</dark-grey> keys</dark-green>\n<italic><grey>Tap to open!</grey></italic>", ct.Name(), u.Data.Faction.Stats.CrateKeys[ct])
							utils.WritePacket(session, &packet.SetActorData{
								EntityRuntimeID: utils.EntityRuntimeID(session, e),
								EntityMetadata:  md,
							})
						}
					}
				}
			})
		}
	}
}

func spawnMob(pl *player.Player) {
	for {
		time.Sleep((3 + time.Duration(rand.Intn(3))) * time.Minute)
		if utils.Distance(server.Config.Hub.SpawnPoint, pl.Position()) >= 500 {
			x := pl.Position().X() + float64(rand.Intn(15))
			z := pl.Position().Z() + float64(rand.Intn(15))
			pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
				y := float64(tx.HighestBlock(int(x), int(z)) + 3)
				v := mgl64.Vec3{x, y, z}
				facWithin := database.FactionWithin(v)
				if facWithin == nil {
					mobs.SpawnEntity(v, tx)
				}
			})
		}
	}
}

func equal(v1, v2 mgl64.Vec3, tolerance float64) bool {
	return math.Abs(v1.X()-v2.X()) <= tolerance &&
		math.Abs(v1.Y()-v2.Y()) <= tolerance &&
		math.Abs(v1.Z()-v2.Z()) <= tolerance
}

type ActivatedOnStartBreak interface {
	OnStartBreak(pl *player.Player, pos cube.Pos) bool
}
