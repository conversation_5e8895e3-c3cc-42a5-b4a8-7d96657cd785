package listeners

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
)

type WorldHandler struct {
	world.NopHandler
}

func (WorldHandler) HandleExplosion(ctx *world.Context, position mgl64.Vec3, entities *[]world.Entity, blocks *[]cube.Pos, itemDropChance *float64, spawnFire *bool) {
	tx := ctx.Val()
	center := cube.PosFromVec3(position)

	if *itemDropChance == 0.5 {
		for x := -5.0; x <= 5.0; x++ {
			for y := -5.0; y <= 5.0; y++ {
				for z := -5.0; z <= 5.0; z++ {
					pos := center.Add(cube.PosFromVec3(mgl64.Vec3{x, y, z}))
					b := tx.Block(pos)
					if _, ok := b.(block.Bedrock); ok {
						tx.SetBlock(pos, block.Air{}, nil)
					}
				}
			}
		}
	}
}
