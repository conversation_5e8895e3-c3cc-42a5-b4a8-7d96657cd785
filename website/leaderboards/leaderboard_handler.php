<?php
require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/db_access.php';

header('Content-Type: application/json');

// Set cache headers for 1 minute (60 seconds)
header('Cache-Control: public, max-age=60');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 300) . ' GMT');


$entity_type = isset($_GET['entity']) ? $_GET['entity'] : 'player'; // 'player' or 'faction'
$data_type = isset($_GET['type']) ? $_GET['type'] : 'kills';       // 'balance', 'kills', 'strength', etc.
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'desc';             // 'asc' or 'desc'

try {

    if (!in_array($entity_type, ['player', 'faction'])) {
        throw new Exception("Invalid entity type");
    }

    if (!in_array($data_type, ['balance', 'kills', 'strength', 'kdr', 'deaths', 'killstreaks', 'bounties'])) {
        throw new Exception("Invalid data type");
    }

    if (!in_array($sort, ['asc', 'desc'])) {
        throw new Exception("Invalid sort direction");
    }

    // Create a unique cache key for this leaderboard request
    $cache_key = "leaderboard_{$entity_type}_{$data_type}_{$sort}";

    // Use cached query with 1-minute TTL (60 seconds)
    $data = cached_query($cache_key, function() use ($entity_type, $data_type, $sort) {
        $db = new DatabaseAccess();

        if ($entity_type === 'player') {
            return $db->get_leaderboard($data_type, $sort);
        } else {
            return $db->get_faction_leaderboard($data_type, $sort);
        }
    }, 60); 


    echo json_encode([
        'success' => true,
        'data' => $data
    ]);

} catch (Exception $e) {

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}