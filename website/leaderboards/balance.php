<?php
require_once '../includes/layout-unified.php';
renderHeader('Balance Leaderboard', ['/css/leaderboard-new.css']);
renderNavbar();
?>
    <div class="container">
        <div class="leaderboard-card p-4">
            <header class="text-center mb-4">
                <h1 class="display-4 fw-bold"><i class="fas fa-crown me-2"></i>TOP BALANCE</h1>
            </header>

            <div class="mb-3 d-flex">
                <div class="input-group me-2">
                    <span class="input-group-text bg-dark text-light"><i class="fas fa-search"></i></span>
                    <input type="text" id="searchInput" class="form-control bg-dark text-light" placeholder="Search by username...">
                </div>
            </div>

            <div class="table-responsive">
    <table id="leaderboard-balance" class="table table-dark table-hover">
        <thead>
            <tr>
                <th><i class="fas fa-medal me-2"></i>Rank</th>
                <th><i class="fas fa-user me-2"></i>Username</th>
                <th><i class="fas fa-coins me-2"></i>Balance</th>
            </tr>
        </thead>
        <tbody>
            <tr id="loading" style="display: none;">
                <td colspan="3" class="text-center">
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </td>
            </tr>
            <tr id="error-message" style="display: none;">
                <td colspan="3" class="text-center text-danger"></td>
            </tr>
            <!-- Leaderboard data will be populated here -->
        </tbody>
    </table>
</div>

<div id="pagination" class="d-flex justify-content-center mt-4 gap-2">
            </div>
        </div>
    </div>

    <?php
    renderFooter(['/js/leaderboards.min.js?v=5']);
    ?>