<?php
require_once __DIR__ . '/includes/core.php';
require_once __DIR__ . '/includes/csrf.php';
require_once __DIR__ . '/includes/layout-unified.php';
require_once __DIR__ . '/includes/ticket_notifications.php';
require_once __DIR__ . '/includes/player-auth.php';

// Require player authentication
require_player_auth();

// Get authenticated player data
$player_data = get_player_data();
// Set cache headers
header('Cache-Control: public, max-age=300');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 300) . ' GMT');

$pageTitle = "Support Center - MassacreMC";
$error = null;
$success = null;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validate_csrf_token()) {
        $error = "Invalid form submission. Please try again.";
    } else {
        try {
            // Use authenticated player's data
            $customerName = $player_data['discord_username'];
            $customerEmail = $player_data['discord_email'];
            $discordId = $player_data['discord_id'];
            
            $subject = validate($_POST['subject'] ?? '', 'text', [
                'required' => true,
                'max_length' => 200
            ]);
            
            $category = validate($_POST['category'] ?? '', 'enum', [
                'required' => true,
                'allowed_values' => ['technical', 'billing', 'general', 'bug', 'feature', 'privacy']
            ]);
            
            $priority = validate($_POST['priority'] ?? '', 'enum', [
                'required' => true,
                'allowed_values' => ['low', 'medium', 'high']
            ]);
            
            $description = validate($_POST['description'] ?? '', 'text', [
                'required' => true,
                'max_length' => 2000,
                'min_length' => 20
            ]);
            
            if (!$subject || !$category || !$priority || !$description) {
                $error = "All fields are required and must be valid.";
            } else {
                // Apply rate limiting
                apply_rate_limit('ticket_submission', 3, 3600, true); // 3 tickets per hour

                // Submit ticket with Discord data
                $result = submitTicket($customerName, $customerEmail, $discordId, $subject, $category, $priority, $description);
                
                if ($result['success']) {
                    $success = "Your support ticket has been submitted successfully! Ticket ID: " . $result['ticket_id'];
                    if ($result['email_sent'] ?? false) {
                        $success .= " You will receive an email confirmation shortly with a link to view your ticket.";
                    } else {
                        $success .= " Please save this ticket ID for your records. Email notifications are currently unavailable.";
                    }
                } else {
                    $error = $result['error'] ?? 'Failed to submit ticket. Please try again.';
                }
            }
        } catch (Exception $e) {
            secure_log("Ticket submission error: " . $e->getMessage(), "error");
            $error = "An error occurred while submitting your ticket. Please try again.";
        }
    }
}

function submitTicket($customerName, $customerEmail, $discordId, $subject, $category, $priority, $description) {
    try {
        // Check if user is blocked from tickets
        if (!empty($discordId)) {
            require_once __DIR__ . '/includes/player-service-blocks.php';
            $blockInfo = isUserBlockedFromService($discordId, 'tickets');
            if ($blockInfo) {
                return [
                    'success' => false,
                    'blocked' => true,
                    'error' => getBlockMessage($blockInfo, 'tickets')
                ];
            }
        }

        require_once __DIR__ . '/includes/db_access.php';

        $db = new DatabaseAccess();
        
        // Generate unique ticket ID
        $ticketId = '' . strtoupper(bin2hex(random_bytes(4)));
        
        // Generate conversation token for email links
        $conversationToken = bin2hex(random_bytes(32));
        
        // Create ticket document & conversation entry
        $ticket = [
            'ticket_id' => $ticketId,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'discord_id' => $discordId,
            'subject' => $subject,
            'description' => $description,
            'category' => $category,
            'priority' => $priority,
            'status' => 'open',
            'conversation_token' => $conversationToken,
            'created_at' => new MongoDB\BSON\UTCDateTime(),
            'last_activity' => new MongoDB\BSON\UTCDateTime(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        // Insert ticket
        $result = $db->db->tickets->insertOne($ticket);
        
        if ($result->getInsertedCount() > 0) {
            // Add initial conversation entry
            $conversation = [
                'ticket_id' => $ticketId,
                'message' => $description,
                'author_type' => 'customer',
                'author_name' => $customerName,
                'is_internal' => false,
                'created_at' => new MongoDB\BSON\UTCDateTime()
            ];

            $db->db->ticket_conversations->insertOne($conversation);

            // Send Discord webhook notification to staff
            try {
                sendTicketDiscordWebhook($ticketId, [
                    'customer_name' => $customerName,
                    'subject' => $subject,
                    'category' => $category,
                    'priority' => $priority,
                    'created_at' => date('F d, Y \a\t h:i A') . ' ET'
                ]);
                secure_log("Discord webhook sent for ticket: " . $ticketId, "info");
            } catch (Exception $webhook_error) {
                // Don't fail the entire operation if webhook fails
                secure_log("Error sending Discord webhook for ticket: " . $webhook_error->getMessage(), "error");
            }

            // Send confirmation email
            secure_log("Attempting to send confirmation email for ticket: $ticketId to: $customerEmail", "info");
            $emailSent = sendTicketConfirmationEmail($ticketId, $customerName, $customerEmail, $subject, $conversationToken);
            if (!$emailSent) {
                secure_log("Failed to send confirmation email for ticket: $ticketId to: $customerEmail", "warning");
            } else {
                secure_log("Successfully sent confirmation email for ticket: $ticketId to: $customerEmail", "info");
            }

            return ['success' => true, 'ticket_id' => $ticketId, 'email_sent' => $emailSent];
        } else {
            return ['success' => false, 'error' => 'Failed to create ticket'];
        }
        
    } catch (Exception $e) {
        secure_log("Error submitting ticket: " . $e->getMessage(), "error");
        return ['success' => false, 'error' => 'Database error occurred'];
    }
}

/**
 * Send Discord webhook notification for new ticket submission
 *
 * @param string $ticket_id Ticket ID
 * @param array $ticket_data Ticket data including customer_name, subject, category, priority, created_at
 * @return bool Whether webhook was sent successfully
 */
function sendTicketDiscordWebhook($ticket_id, $ticket_data) {
    // Load environment configuration
    require_once __DIR__ . '/includes/config_parser.php';
    try {
        $ENV = load_env_config([
            '/etc/massacremc/config/admin.env',
            __DIR__ . '/admin/.env'
        ]);
        error_log("Ticket webhook: Successfully loaded config from admin/.env. DISCORD_TICKETS_WEBHOOK_URL = " . ($ENV['DISCORD_TICKETS_WEBHOOK_URL'] ?? 'NOT FOUND'));
    } catch (Exception $e) {
        error_log("Failed to load environment config for ticket webhook: " . $e->getMessage());
        return false;
    }

    // Discord webhook URL for tickets
    $webhookUrl = $ENV['DISCORD_TICKETS_WEBHOOK_URL'] ?? getenv('DISCORD_TICKETS_WEBHOOK_URL') ?? null;

    if (empty($webhookUrl)) {
        error_log("Discord tickets webhook URL not configured");
        return false;
    }

    // Priority colors and labels
    $priority_config = [
        'high' => ['color' => 0xFF0000, 'label' => '🔴 HIGH'],      // Red
        'medium' => ['color' => 0xFF8C00, 'label' => '🟠 MEDIUM'],  // Orange
        'low' => ['color' => 0x00FF00, 'label' => '🟢 LOW']         // Green
    ];

    $priority = $ticket_data['priority'] ?? 'low';
    $priority_info = $priority_config[$priority] ?? $priority_config['low'];

    // Category icons
    $category_icons = [
        'technical' => '🔧',
        'billing' => '💳',
        'general' => '❓',
        'bug' => '🐛',
        'feature' => '💡',
        'privacy' => '🔏'
    ];

    $category_icon = $category_icons[$ticket_data['category']] ?? '🎫';

    // Create Discord embed
    $embed = [
        'title' => '🎫 New Support Ticket',
        'color' => $priority_info['color'],
        'timestamp' => date('c'), // ISO 8601 format
        'fields' => [
            [
                'name' => 'Customer',
                'value' => $ticket_data['customer_name'],
                'inline' => true
            ],
            [
                'name' => 'Subject',
                'value' => $ticket_data['subject'],
                'inline' => true
            ],
            [
                'name' => 'Priority',
                'value' => $priority_info['label'],
                'inline' => true
            ],
            [
                'name' => $category_icon . ' Category',
                'value' => ucfirst($ticket_data['category']),
                'inline' => true
            ],
            [
                'name' => 'Ticket ID',
                'value' => "`{$ticket_id}`",
                'inline' => true
            ],
            [
                'name' => 'Submitted',
                'value' => $ticket_data['created_at_formatted'],
                'inline' => true
            ]
        ],
        'footer' => [
            'text' => 'MassacreMC Support System',
            'icon_url' => 'https://cdn.discordapp.com/attachments/your-icon-url.png'
        ]
    ];

    // Create webhook payload
    $payload = [
        'content' => '🎫 **New Support Ticket**',
        'embeds' => [$embed]
    ];

    // Send webhook
    $ch = curl_init($webhookUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if (!empty($curlError)) {
        error_log("Ticket webhook cURL error: $curlError");
        return false;
    }

    if ($httpCode !== 204 && $httpCode !== 200) {
        error_log("Failed to send ticket webhook. HTTP Code: $httpCode, Response: $response");
        return false;
    }

    return true;
}

renderHeader($pageTitle, ['/css/services.css']);
renderNavbar();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold">
                    <i class="fas fa-headset me-3 text-primary"></i>Support Center
                </h1>
                <p class="lead text-muted">Get help with your MassacreMC experience</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>

                <!-- Ticket Access Information -->
                <div class="card shadow-lg border-0 mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Access Your Ticket
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">You can access your ticket conversation using the information below:</p>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Ticket ID:</strong><br>
                                <code class="fs-5"><?php echo htmlspecialchars($result['ticket_id']); ?></code>
                            </div>
                            <div class="col-md-6">
                                <strong>Your Email:</strong><br>
                                <code><?php echo htmlspecialchars($player_data['discord_email']); ?></code>
                            </div>
                        </div>
                        <hr>
                        <p class="mb-2"><strong>You can view your tickets and conversation history in:</strong></p>
                        <div class="d-flex flex-column flex-md-row gap-3">
                            <a href="https://portal.massacremc.net/account/tickets" class="btn btn-primary btn-lg me-md-3 mb-3 mb-md-0">
                                <i class="fas fa-user me-2"></i>Account Portal
                            </a>
                        </div>

                    </div>
                </div>
            <?php else: ?>
                <!-- Ticket Submission Form -->
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-ticket-alt me-2"></i>Submit a Support Ticket
                        </h3>
                    </div>
                    <div class="card-body p-4">
                        <!-- Account Information -->
                        <div class="alert alert-info mb-4" role="alert">
                            <i class="fas fa-user me-2"></i>
                            <strong>Submitting as:</strong> <?php echo htmlspecialchars($player_data['discord_username']); ?>
                            <?php if (!empty($player_data['discord_email'])): ?>
                                (<?php echo htmlspecialchars($player_data['discord_email']); ?>)
                            <?php endif; ?>
                        </div>

                        <form method="POST" action="">
                            <?php echo csrf_token_field(); ?>

                            <div class="mb-3">
                                <label for="subject" class="form-label">Subject *</label>
                                <input type="text" class="form-control" id="subject" name="subject" 
                                       value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>" 
                                       placeholder="Brief description of your issue" required>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="category" class="form-label">Category *</label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">Select a category</option>
                                        <option value="technical" <?php echo ($_POST['category'] ?? '') === 'technical' ? 'selected' : ''; ?>>
                                            Technical Support
                                        </option>
                                        <option value="billing" <?php echo ($_POST['category'] ?? '') === 'billing' ? 'selected' : ''; ?>>
                                            Billing & Payments
                                        </option>
                                        <option value="general" <?php echo ($_POST['category'] ?? '') === 'general' ? 'selected' : ''; ?>>
                                            General Inquiry
                                        </option>
                                        <option value="bug" <?php echo ($_POST['category'] ?? '') === 'bug' ? 'selected' : ''; ?>>
                                            Bug Report
                                        </option>
                                        <option value="feature" <?php echo ($_POST['category'] ?? '') === 'feature' ? 'selected' : ''; ?>>
                                            Feature Request
                                        </option>
                                        <option value="privacy" <?php echo ($_POST['category'] ?? '') === 'privacy' ? 'selected' : ''; ?>>
                                            GDPR Request
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="priority" class="form-label">Priority *</label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="">Select priority</option>
                                        <option value="low" <?php echo ($_POST['priority'] ?? '') === 'low' ? 'selected' : ''; ?>>
                                            Low - General question
                                        </option>
                                        <option value="medium" <?php echo ($_POST['priority'] ?? '') === 'medium' ? 'selected' : ''; ?>>
                                            Medium - Issue affecting gameplay
                                        </option>
                                        <option value="high" <?php echo ($_POST['priority'] ?? '') === 'high' ? 'selected' : ''; ?>>
                                            High - Critical issue
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control" id="description" name="description" rows="6" 
                                          placeholder="Please provide detailed information about your issue..." required><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                                <div class="form-text">
                                    Please include as much detail as possible to help us resolve your issue quickly.
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Ticket
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Help Resources -->
            <div class="row mt-5">
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-book fa-3x text-info mb-3"></i>
                            <h5>Knowledge Base</h5>
                            <p class="text-muted">Find answers to common questions and learn about server features.</p>
                            <a href="/rules" class="btn btn-outline-info">View Rules</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fab fa-discord fa-3x text-primary mb-3"></i>
                            <h5>Discord Community</h5>
                            <p class="text-muted">Join our Discord server for real-time support and community chat.</p>
                            <a href="https://discord.gg/mBjd5cXSxR" class="btn btn-outline-primary" target="_blank">
                                Join Discord
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
renderFooter([
    '/js/main-new.min.js',
    'https://cdn.jsdelivr.net/npm/sweetalert2@11'
]);
?>
