<?php
/**
 * Player Account Portal - Settings
 * Manage account settings and linked Minecraft usernames
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/layout-unified.php';
require_once __DIR__ . '/../includes/csrf.php';
require_once __DIR__ . '/../includes/validation.php';

// Require player authentication
require_player_auth();

$player_data = get_player_data();
$error = $_SESSION['error'] ?? null;
$success = $_SESSION['success'] ?? null;

// Clear session messages after displaying
unset($_SESSION['error']);
unset($_SESSION['success']);

// Get linked accounts
$linked_accounts = [];
try {
    $dbAccess = new DatabaseAccess();
    $db = $dbAccess->db;

    $linkedAccountsData = $db->linked_accounts->find([
        'discord_id' => $player_data['discord_id'],
        'status' => 'verified'
    ]);

    foreach ($linkedAccountsData as $account) {
        $linked_accounts[$account['platform']] = [
            'username' => $account['username'],
            'verified_at' => $account['verified_at'] ?? null,
            'xbox_id' => $account['xbox_id'] ?? null
        ];
    }
} catch (Exception $e) {
    secure_log("Error fetching linked accounts", "error", [
        'discord_id' => $player_data['discord_id'],
        'error' => $e->getMessage()
    ]);
    $error = "Unable to load linked accounts. Please refresh the page.";
}

// Handle session messages
$error = $_SESSION['error'] ?? null;
$success = $_SESSION['success'] ?? null;

// Clear session messages after displaying
unset($_SESSION['error']);
unset($_SESSION['success']);

// Handle form submissions (currently no forms to process)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validate_csrf_token()) {
        $error = "Invalid form submission. Please try again.";
    }
    // Future form handling can be added here
}

$pageTitle = "Account Settings - MassacreMC";
renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
renderNavbar();
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-cog me-3 text-primary"></i>Account Settings
                </h1>
                <p class="lead text-muted">Manage your Discord account information and preferences</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Discord Account Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fab fa-discord me-2" style="color:rgb(255, 255, 255);"></i>Discord Account</h4>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <?php
                            // Construct avatar URL - handle null/empty avatars
                            if (!empty($player_data['discord_avatar']) && $player_data['discord_avatar'] !== 'null') {
                                $avatar_url = "https://cdn.discordapp.com/avatars/{$player_data['discord_id']}/{$player_data['discord_avatar']}.png?size=64";
                            } else {
                                // Use default avatar - calculate based on user ID
                                $default_avatar_id = intval($player_data['discord_id']) % 5;
                                $avatar_url = "https://cdn.discordapp.com/embed/avatars/{$default_avatar_id}.png";
                            }
                            ?>
                            <img src="<?php echo $avatar_url; ?>" alt="Avatar" class="rounded-circle" width="64" height="64">
                        </div>
                        <div class="col-md-10">
                            <h5><?php echo htmlspecialchars($player_data['discord_username']); ?></h5>
                            <p class="text-muted mb-0">
                                <i class="fas fa-check-circle me-2 text-success"></i>
                                Connected and authenticated
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Details -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-id-card me-2"></i>Account Details</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-hashtag me-2 text-primary"></i>Discord ID</h6>
                            <p class="font-monospace p-2 rounded" style="background-color: #343a40 !important; color: #fff !important; border: 1px solid #495057 !important;"><?php echo htmlspecialchars($player_data['discord_id']); ?></p>
                            <small class="text-muted">This unique ID links your submissions automatically.</small>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-envelope me-2 text-info"></i>Email Address</h6>
                            <?php if (!empty($player_data['discord_email'])): ?>
                                <p class="p-2 rounded" style="background-color: #343a40 !important; color: #fff !important; border: 1px solid #495057 !important;"><?php echo htmlspecialchars($player_data['discord_email']); ?></p>
                                <small class="text-muted">Used to match tickets and communications.</small>
                            <?php else: ?>
                                <p class="text-muted p-2 rounded" style="background-color: #343a40 !important; color: #fff !important; border: 1px solid #495057 !important;">No email provided by Discord</p>
                                <small class="text-muted">Some submissions may not be visible without email.</small>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>How it works:</strong> Your tickets, appeals, and reports are automatically linked to your account using your Discord ID and email address. No manual linking required!
                    </div>
                </div>
            </div>

            <!-- Xbox Account Info (if linked) -->
            <?php if (isset($linked_accounts['xbox'])): ?>
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fab fa-xbox me-2"></i>Linked Xbox Account</h4>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fab fa-xbox fa-3x text-success"></i>
                            </div>
                            <div>
                                <h5 class="mb-1"><?php echo htmlspecialchars($linked_accounts['xbox']['username']); ?></h5>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-link me-1"></i>
                                    Linked on <?php echo isset($linked_accounts['xbox']['verified_at']) ? $linked_accounts['xbox']['verified_at']->toDateTime()->format('F j, Y') : 'Unknown'; ?>
                                </p>
                            </div>
                        </div>
                        <div>
                            <button onclick="unlinkXboxAccount()" class="btn btn-outline-danger">
                                <i class="fas fa-unlink me-1"></i>Unlink Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Linked Accounts -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-link me-2"></i>Linked Accounts</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">Connect your gaming accounts to enhance your experience and enable cross-platform features.</p>

                    <!-- Discord Account (Already Connected) -->
                    <div class="d-flex align-items-center justify-content-between p-3 mb-3 rounded" style="background-color: #343a40; border: 1px solid #495057;">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fab fa-discord fa-2x" style="color: #5865F2;"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-white">Discord</h6>
                                <small class="text-secondary">Connected as <?php echo htmlspecialchars($player_data['discord_username']); ?></small>
                            </div>
                        </div>
                        <div>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Connected
                            </span>
                        </div>
                    </div>

                    <!-- Xbox Account -->
                    <div class="d-flex align-items-center justify-content-between p-3 mb-3 rounded" style="background-color: #343a40; border: 1px solid #495057;">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fab fa-xbox fa-2x text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 text-white">Xbox Account</h6>
                                <small class="text-secondary">Link your Xbox account to get access to punishment history and other features</small>
                            </div>
                        </div>
                        <div>
                            <?php if (isset($linked_accounts['xbox'])): ?>
                                <span class="badge bg-success me-2">
                                    <i class="fas fa-check me-1"></i>Connected
                                </span>
                            <?php else: ?>
                                <a href="/auth/xbox-oauth" class="btn btn-sm btn-success">
                                    <i class="fab fa-xbox me-1"></i>Connect with Xbox Live
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>





                    <!-- Benefits Info -->
                    <div class="alert alert-info mt-4" role="alert">
                        <i class="fas fa-star me-2"></i>
                        <strong>Benefits of linking accounts:</strong>
                        <ul class="mb-0 mt-2">
                            <li>See in-game punishments and history</li>
                            <li>Unified statistics and achievements and settings</li>
                            <li>Easier account recovery and support</li>
                            <li>Access to exclusive cross-platform events</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Important Information -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>Important Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-shield-alt me-2 text-success"></i>Privacy & Security</h6>
                            <ul class="list-unstyled small">
                                <li>• Your Discord information is kept secure</li>
                                <li>• We only access your username and email</li>
                                <li>• Data is never shared with third parties</li>
                                <li>• You can logout anytime to end your session</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-link me-2 text-primary"></i>Automatic Linking</h6>
                            <ul class="list-unstyled small">
                                <li>• Submissions linked by Discord ID and email</li>
                                <li>• No manual account linking required</li>
                                <li>• View all your tickets, appeals, and reports</li>
                                <li>• Secure and automatic identification</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Actions -->
            <div class="text-center">
                <a href="/account/" class="btn btn-secondary me-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <a href="/auth/player-logout" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Link Account Modals -->




<script>
// Linked Accounts JavaScript Functions

function linkXboxOAuth() {
    // Redirect to Xbox OAuth endpoint
    console.log('Xbox OAuth function called');
    alert('Redirecting to Xbox OAuth...'); // Temporary debug alert
    window.location.href = '/auth/xbox-oauth';
}







// Xbox unlinking function
function unlinkXboxAccount(confirmed = false) {
    if (!confirmed && !confirm('Are you sure you want to unlink your Xbox account? This action cannot be undone and you will lose access to Xbox-related features.')) {
        return;
    }

    // Show loading state - find the button by onclick attribute since event might not be available
    const unlinkBtn = confirmed ?
        document.querySelector('button[onclick*="unlinkXboxAccount"]') :
        event.target.closest('button');

    if (!unlinkBtn) {
        alert('Error: Could not find unlink button');
        return;
    }

    const originalText = unlinkBtn.innerHTML;
    unlinkBtn.disabled = true;
    unlinkBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Unlinking...';

    // Use XMLHttpRequest instead of fetch to handle 409 properly
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '/api/unlink-account.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            console.log('XHR Status:', xhr.status);
            console.log('XHR Response:', xhr.responseText);

            let data;
            try {
                data = JSON.parse(xhr.responseText);
            } catch (e) {
                console.error('Failed to parse JSON:', e);
                alert('Error: Invalid response from server');
                unlinkBtn.disabled = false;
                unlinkBtn.innerHTML = originalText;
                return;
            }

            console.log('Parsed data:', data);

            if (xhr.status === 200 && data.success) {
                // Show success message and reload page
                let message = data.message || 'Xbox account unlinked successfully!';
                if (data.applications_denied && data.applications_denied > 0) {
                    message += `\n\nNote: ${data.applications_denied} pending staff application(s) were automatically denied.`;
                }
                alert(message);
                window.location.reload();
            } else if (xhr.status === 409) {
                console.log('Handling 409 response');
                // Handle warning about pending staff applications
                if (data.warning && data.pending_applications > 0) {
                    console.log('Showing warning dialog');
                    let warningMessage = `⚠️ WARNING: You have ${data.pending_applications} pending staff application(s) that will be automatically DENIED if you unlink your Xbox account.\n\n`;
                    warningMessage += 'Pending applications:\n';
                    if (data.applications && Array.isArray(data.applications)) {
                        data.applications.forEach(app => {
                            warningMessage += `• ${app.position.charAt(0).toUpperCase() + app.position.slice(1)} (submitted ${app.submitted_at})\n`;
                        });
                    }
                    warningMessage += '\nXbox account linking is required for all staff applications. Are you sure you want to proceed?';

                    console.log('About to show confirm dialog');

                    if (confirm(warningMessage)) {
                        console.log('User confirmed unlinking');
                        // User confirmed, proceed with unlinking
                        unlinkXboxAccount(true);
                        return;
                    } else {
                        console.log('User cancelled unlinking');
                        // User cancelled, restore button
                        unlinkBtn.disabled = false;
                        unlinkBtn.innerHTML = originalText;
                        return;
                    }
                } else {
                    console.log('409 but no warning data:', data);
                    alert('Warning: ' + (data.message || 'Conflict occurred'));
                    unlinkBtn.disabled = false;
                    unlinkBtn.innerHTML = originalText;
                }
            } else {
                // Show error message
                alert('Error: ' + (data.message || 'Failed to unlink Xbox account') + ' (Status: ' + xhr.status + ')');
                // Restore button
                unlinkBtn.disabled = false;
                unlinkBtn.innerHTML = originalText;
            }
        }
    };

    xhr.send(JSON.stringify({
        platform: 'xbox',
        confirmed: confirmed
    }));
}
</script>

<?php
renderFooter(['/js/account-portal.js', '/js/account-portal-mobile.js']);
?>
