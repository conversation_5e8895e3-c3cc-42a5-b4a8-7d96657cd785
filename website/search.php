<?php
/**
 * Search Page
 * Allows users to search for players and factions
 */


require_once __DIR__ . '/includes/core.php';
require_once __DIR__ . '/includes/layout-unified.php';
require_once __DIR__ . '/includes/api_client.php';
require_once __DIR__ . '/includes/mongo_security.php';


if (isset($_GET['ajax_search']) && isset($_GET['query'])) {

    header('Content-Type: application/json');

    try {

        $query = validate($_GET['query'], 'text', [
            'max_length' => 100,
            'required' => true
        ]);

        if ($query === null) {
            json_response(['error' => 'Invalid search query'], 400);
        }


        apply_rate_limit('search', 10, 60, true);


        $result = cached_query('search_' . md5($query), function() use ($query) {
            // Use API-based search instead of direct database access
            require_once __DIR__ . '/includes/db_access.php';
            $db = new DatabaseAccess();

            // First get the UUID/name for API calls
            $search_result = $db->search_player_or_faction($query);

            if (isset($search_result['player']) && $search_result['player']) {
                // Get up-to-date player data from API
                if (isset($search_result['player_uuid'])) {
                    $apiClient = new ApiClient();
                    $api_player_data = $apiClient->getPlayerData($search_result['player_uuid']);

                    if (!isset($api_player_data['error'])) {
                        $search_result['player_data'] = $api_player_data;
                    }
                }
            }

            if (isset($search_result['faction']) && $search_result['faction']) {
                // Get up-to-date faction data from API
                if (isset($search_result['faction_name'])) {
                    $apiClient = new ApiClient();
                    $api_faction_data = $apiClient->getFactionData($search_result['faction_name']);

                    if (!isset($api_faction_data['error'])) {
                        $search_result['faction_data'] = $api_faction_data;
                    }
                }
            }

            return $search_result;
        }, 300); // Cache for 5 minutes


        json_response($result);
    } catch (Exception $e) {

        secure_log("Search error: " . $e->getMessage(), "error");


        json_response(['error' => 'An error occurred while searching'], 500);
    }
}

renderHeader('Player Search', ['/css/search-new.css', '/js/search-api-bridge.js']);
renderNavbar();
?>
<div class="container">
    <div class="search-hero d-flex flex-column justify-content-center align-items-center">
        <div class="search-header text-center mb-4">
            <h1 class="display-5 fw-bold">
                Portal Search <span class="fas fa-search"></span>
            </h1>
            <p class="lead text-light">Find players and factions across the MassacreMC network</p>
        </div>

        <div class="search-container mb-3">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control bg-dark text-light" placeholder="Enter username or faction name...">
                <button class="btn btn-primary" type="button" id="searchButton">
                    <i class="fas fa-search"></i><span class="d-none d-md-inline ms-2">Search</span>
                </button>
            </div>

            <div id="loading" class="mt-3 text-center" style="display: none;">
                <div class="spinner-container">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-light">Searching...</p>
                </div>
            </div>

            <div id="error-message" class="mt-4 text-center" style="display: none;">
                <div class="alert alert-danger py-3" style="background-color: rgba(220, 53, 69, 0.2); border: 2px solid rgba(220, 53, 69, 0.5); border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                    <i class="fas fa-search-minus fa-lg me-2"></i>
                    <span id="error-text" style="font-size: 1.1rem; font-weight: 600;">An error occurred while searching.</span>
                </div>
            </div>
        </div>
    </div>

    <div class="leaderboard-card p-4">

        <!-- Search Results Container -->
        <div id="search-results" class="mt-4" style="display: none;">
            <!-- Results will be populated by JavaScript -->
        </div>

        <div class="search-tips mt-4 mb-3">
            <h5 class="text-center mb-3"><i class="fas fa-lightbulb me-2 text-warning"></i>Search Tips</h5>
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="tip-card">
                        <h6><i class="fas fa-user me-2"></i>Player Search</h6>
                        <p>Enter a player's exact username to view their stats, history, and faction information.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="tip-card">
                        <h6><i class="fas fa-users me-2"></i>Faction Search</h6>
                        <p>Enter a faction name to view its members, power, and other faction details.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="tip-card">
                        <h6><i class="fas fa-api me-2"></i>Enhanced Data</h6>
                        <p>Search results now include real-time data from our enhanced API system.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="selectionModal" tabindex="-1" aria-labelledby="selectionModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content bg-dark text-light">
                    <div class="modal-header">
                        <h5 class="modal-title" id="selectionModalLabel">Choose Lookup Type</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>We found both a player and a faction with the name "<span id="searchName"></span>". What would you like to look up?</p>
                        <div class="d-flex justify-content-around">
                            <button class="btn btn-primary" id="lookupPlayer">Player</button>
                            <button class="btn btn-secondary" id="lookupFaction">Faction</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.result-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.result-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.search-results {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tip-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
    height: 100%;
    transition: background 0.3s ease;
}

.tip-card:hover {
    background: rgba(255, 255, 255, 0.1);
}
</style>

<?php echo nonce_script('
    document.addEventListener("DOMContentLoaded", function() {
        const searchInput = document.getElementById("searchInput");
        const searchButton = document.getElementById("searchButton");
        const loadingElement = document.getElementById("loading");
        const errorElement = document.getElementById("error-message");
        const errorText = document.getElementById("error-text");
        const searchNameElement = document.getElementById("searchName");
        const lookupPlayerButton = document.getElementById("lookupPlayer");
        const lookupFactionButton = document.getElementById("lookupFaction");
        const selectionModal = document.getElementById("selectionModal");


        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }


        // Initialize the search API bridge (check if class exists)
        let searchBridge = null;
        const searchResultsContainer = document.getElementById("search-results");

        if (typeof SearchApiBridge !== "undefined") {
            searchBridge = new SearchApiBridge("http://localhost:8080"); // Update this URL to your Go API
        } else {
            console.warn("SearchApiBridge class not found, falling back to legacy search");
        }

        function performSearch() {
            const query = searchInput.value.trim();

            if (!query) {
                Swal.fire({
                    icon: "warning",
                    title: "Empty Search",
                    text: "Please enter a username or faction name.",
                    confirmButtonColor: "#3085d6"
                });
                return;
            }

            // Show loading state
            loadingElement.style.display = "block";
            errorElement.style.display = "none";
            if (searchResultsContainer) {
                searchResultsContainer.style.display = "none";
            }

            // Use the new search bridge if available, otherwise fall back to legacy
            if (searchBridge) {
                // Use the new search bridge for enhanced results
                searchBridge.search(query, "both")
                    .then(data => {
                        loadingElement.style.display = "none";

                        if (!data.success) {
                            errorElement.style.display = "block";
                            errorText.textContent = data.error;
                            return;
                        }

                        // Display results using the new system
                        if (searchResultsContainer) {
                            searchResultsContainer.style.display = "block";
                            searchBridge.displaySearchResults("#search-results", data);
                        }

                        // Handle legacy behavior for single results
                        if (data.results.length === 1) {
                            const result = data.results[0];
                            // Auto-redirect after a short delay to show the result
                            setTimeout(() => {
                                window.location.href = result.local_endpoint;
                            }, 1500);
                        }
                    })
                    .catch(error => {
                        console.error("Search error:", error);
                        loadingElement.style.display = "none";
                        errorElement.style.display = "block";
                        errorText.textContent = "An error occurred while searching. Please contact an administrator if this persists.";
                    });
            } else {
                // Fall back to legacy search
                fetch(`/search?ajax_search=1&query=${encodeURIComponent(query)}`, {
                    method: "GET",
                    headers: {
                        "X-Requested-With": "XMLHttpRequest"
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    loadingElement.style.display = "none";

                    if (data.error) {
                        errorElement.style.display = "block";
                        errorText.textContent = data.error;
                        return;
                    }

                    const playerName = data.player_name || query;
                    const factionName = data.faction_name || query;

                    if (data.player && data.faction) {
                        searchNameElement.textContent = sanitizeText(query);
                        const modal = new bootstrap.Modal(selectionModal);
                        modal.show();

                        lookupPlayerButton.onclick = () => {
                            window.location.href = `/player/${encodeURIComponent(playerName)}`;
                        };

                        lookupFactionButton.onclick = () => {
                            window.location.href = `/faction/${encodeURIComponent(factionName)}`;
                        };
                    } else if (data.player) {
                        window.location.href = `/player/${encodeURIComponent(playerName)}`;
                    } else if (data.faction) {
                        window.location.href = `/faction/${encodeURIComponent(factionName)}`;
                    } else {
                        errorElement.style.display = "block";
                        errorText.innerHTML = `<strong>No results found for "${sanitizeText(query)}"</strong><br><span class="text-muted mt-2 d-block">This player or faction does not exist.</span>`;
                    }
                })
                .catch(error => {
                    console.error("Search error:", error);
                    loadingElement.style.display = "none";
                    errorElement.style.display = "block";
                    errorText.textContent = "An error occurred while searching. Please contact an administrator if this persists.";
                });
            }
        }


        function sanitizeText(text) {
            const div = document.createElement("div");
            div.textContent = text;
            return div.innerHTML;
        }


        searchButton.addEventListener("click", performSearch);


        const debouncedSearch = debounce(performSearch, 300);
        searchInput.addEventListener("keydown", function(event) {
            if (event.key === "Enter") {
                event.preventDefault();
                debouncedSearch();
            }
        });


        searchInput.addEventListener("input", debounce(function() {
            const query = searchInput.value.trim();
            if (query.length >= 2) {
                fetch(`/api/search-players.php?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        // Handle autocomplete suggestions here if needed
                    })
                    .catch(error => console.error("Autocomplete error:", error));
            }
        }, 500));
    });
', $csp_nonce); ?>

<?php
renderFooter([
    'https://cdn.jsdelivr.net/npm/sweetalert2@11'
]);
?>