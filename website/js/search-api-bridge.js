/**
 * Search API Bridge
 * 
 * Handles searching and integrating with both PHP and Go APIs
 */

class SearchApiBridge {
    constructor(goApiBaseUrl = 'http://localhost:8080') {
        this.goApiBaseUrl = goApiBaseUrl;
        this.phpApiBaseUrl = window.location.origin;
    }

    /**
     * Search for players and factions using the bridge API
     */
    async search(query, type = 'both') {
        try {
            const response = await fetch(
                `${this.phpApiBaseUrl}/api/search-bridge.php?query=${encodeURIComponent(query)}&type=${type}&go_api_url=${encodeURIComponent(this.goApiBaseUrl)}`
            );
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Search failed');
            }
            
            return data;
        } catch (error) {
            console.error('Search error:', error);
            throw error;
        }
    }

    /**
     * Get player data from Go API using UUID
     */
    async getPlayerDataFromGoApi(uuid) {
        try {
            const response = await fetch(`${this.goApiBaseUrl}/api/players/${uuid}`, {
                headers: {
                    'Authorization': 'Bearer YOUR_JWT_TOKEN', // You'll need to handle JWT tokens
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return data.data; // Go API returns data in a "data" field
        } catch (error) {
            console.error('Error getting player data from Go API:', error);
            throw error;
        }
    }

    /**
     * Get faction data from Go API using name
     */
    async getFactionDataFromGoApi(name) {
        try {
            const response = await fetch(`${this.goApiBaseUrl}/api/factions/${encodeURIComponent(name)}`, {
                headers: {
                    'Authorization': 'Bearer YOUR_JWT_TOKEN', // You'll need to handle JWT tokens
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return data.data; // Go API returns data in a "data" field
        } catch (error) {
            console.error('Error getting faction data from Go API:', error);
            throw error;
        }
    }

    /**
     * Search and get full data (combines search + Go API calls)
     */
    async searchAndGetData(query, type = 'both') {
        try {
            // Step 1: Search for matches
            const searchResults = await this.search(query, type);
            
            // Step 2: Get full data for each result
            const enrichedResults = [];
            
            for (const result of searchResults.results) {
                try {
                    let fullData = null;
                    
                    if (result.type === 'player') {
                        fullData = await this.getPlayerDataFromGoApi(result.uuid);
                    } else if (result.type === 'faction') {
                        fullData = await this.getFactionDataFromGoApi(result.name);
                    }
                    
                    enrichedResults.push({
                        ...result,
                        data: fullData,
                        success: true
                    });
                } catch (error) {
                    // If Go API fails, still include the result but mark as failed
                    enrichedResults.push({
                        ...result,
                        data: null,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            return {
                success: true,
                query: searchResults.query,
                results: enrichedResults
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Display search results in a container
     */
    displaySearchResults(containerSelector, searchResult) {
        const container = document.querySelector(containerSelector);
        if (!container) {
            console.error('Container not found:', containerSelector);
            return;
        }

        if (!searchResult.success) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>Search Failed</h4>
                    <p>${searchResult.error}</p>
                </div>
            `;
            return;
        }

        if (searchResult.results.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <h4>No Results Found</h4>
                    <p>No players or factions found matching "${searchResult.query}"</p>
                </div>
            `;
            return;
        }

        let html = `<div class="search-results">`;
        
        for (const result of searchResult.results) {
            html += `
                <div class="result-card mb-3 p-3 border rounded ${result.success ? 'border-success' : 'border-warning'}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">
                                <i class="fas fa-${result.type === 'player' ? 'user' : 'users'} me-2"></i>
                                ${result.name}
                                <span class="badge bg-${result.type === 'player' ? 'primary' : 'secondary'} ms-2">
                                    ${result.type}
                                </span>
                            </h5>
                            ${result.type === 'player' && result.uuid ? `<small class="text-muted">UUID: ${result.uuid}</small>` : ''}
                        </div>
                        <div>
                            <a href="${result.local_endpoint}" class="btn btn-primary btn-sm me-2">
                                View Details
                            </a>
                            ${result.success ? 
                                '<span class="badge bg-success">API Data Loaded</span>' : 
                                '<span class="badge bg-warning">API Data Failed</span>'
                            }
                        </div>
                    </div>
                    
                    ${result.success && result.data ? this.renderDataPreview(result) : ''}
                    ${!result.success ? `<div class="text-warning mt-2"><small>API Error: ${result.error}</small></div>` : ''}
                </div>
            `;
        }
        
        html += `</div>`;
        container.innerHTML = html;
    }

    /**
     * Render a preview of the data
     */
    renderDataPreview(result) {
        if (!result.data) return '';
        
        if (result.type === 'player') {
            const stats = result.data.faction?.stats || {};
            return `
                <div class="mt-2 pt-2 border-top">
                    <small class="text-muted">
                        <strong>Quick Stats:</strong>
                        Balance: ${(stats.doubloons || 0).toLocaleString()} doubloons |
                        Kills: ${stats.kills || 0} |
                        Deaths: ${stats.deaths || 0} |
                        Online: ${result.data.online ? 'Yes' : 'No'}
                    </small>
                </div>
            `;
        } else if (result.type === 'faction') {
            return `
                <div class="mt-2 pt-2 border-top">
                    <small class="text-muted">
                        <strong>Faction Info:</strong>
                        Members: ${result.data.members?.length || 0} |
                        Power: ${result.data.power || 0}
                    </small>
                </div>
            `;
        }
        
        return '';
    }
}

// Global instance
window.searchApiBridge = new SearchApiBridge();
