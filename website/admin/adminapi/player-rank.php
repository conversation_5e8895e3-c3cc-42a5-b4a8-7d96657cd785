<?php
/**
 * Player Rank Management API
 * Handles rank changes for players - <PERSON><PERSON><PERSON> and Owner access only
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Require authentication and Dev<PERSON>per+ role
require_auth(ROLE_DEVELOPER);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Verify CSRF token
require_csrf_check(true); // true for API mode

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
        exit;
    }

    // Validate required fields
    $required_fields = ['xuid', 'username', 'new_rank'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
            exit;
        }
    }

    $xuid = trim($data['xuid']);
    $username = trim($data['username']);
    $new_rank = strtolower(trim($data['new_rank']));
    $reason = isset($data['reason']) ? trim($data['reason']) : '';

    // Define valid ranks
    $valid_ranks = [
        'player', 'vip', 'mvp', 'mmp', 'mgp', 'mlp', 'c', 'builder', 
        'trainee', 'support', 'moderator', 'admin', 'owner', 'yt', 'youtuber', 'secretary'
    ];

    // Validate rank
    if (!in_array($new_rank, $valid_ranks)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid rank specified']);
        exit;
    }

    // Get database connection
    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        throw new Exception("Failed to connect to database");
    }

    // Check if player exists
    $player = $db->player_data->findOne(['xuid' => $xuid]);
    if (!$player) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Player not found']);
        exit;
    }

    // Get current rank for logging
    $current_rank = isset($player['groupsettings']['rankid']) ? $player['groupsettings']['rankid'] : 'player';

    // Update player rank
    $update_result = $db->player_data->updateOne(
        ['xuid' => $xuid],
        ['$set' => ['groupsettings.rankid' => $new_rank]]
    );

    if ($update_result->getModifiedCount() === 0 && $update_result->getMatchedCount() === 0) {
        throw new Exception("Failed to update player rank");
    }

    // Log the rank change for audit purposes
    $audit_data = [
        'action' => 'rank_change',
        'target_xuid' => $xuid,
        'target_username' => $username,
        'old_rank' => $current_rank,
        'new_rank' => $new_rank,
        'reason' => $reason,
        'staff_id' => $_SESSION['discord_user_id'] ?? 'unknown',
        'staff_username' => $_SESSION['discord_username'] ?? 'Unknown',
        'staff_role' => $_SESSION['discord_user_role'] ?? 'UNKNOWN',
        'timestamp' => new MongoDB\BSON\UTCDateTime(),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];

    // Insert audit log
    $db->audit_logs->insertOne($audit_data);

    // Log to error log as well
    error_log("RANK CHANGE: {$_SESSION['discord_username']} changed {$username} ({$xuid}) rank from {$current_rank} to {$new_rank}. Reason: {$reason}");

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Player rank updated successfully',
        'data' => [
            'xuid' => $xuid,
            'username' => $username,
            'old_rank' => $current_rank,
            'new_rank' => $new_rank,
            'updated_by' => $_SESSION['discord_username'] ?? 'Unknown'
        ]
    ]);

} catch (Exception $e) {
    error_log("Player Rank API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
