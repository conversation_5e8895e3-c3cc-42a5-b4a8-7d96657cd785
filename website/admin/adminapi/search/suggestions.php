<?php
/**
 * Search Suggestions API Endpoint
 * Provides autocomplete suggestions for player and faction searches
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start([
        'cookie_secure' => true,
        'cookie_httponly' => true,
        'cookie_path' => '/',
        'cookie_samesite' => 'Lax',
        'use_strict_mode' => true,
        'gc_maxlifetime' => 86400 // 24 hours
    ]);
}


apply_rate_limit($_SERVER['REMOTE_ADDR'], 120, 60); // 120 requests per minute for suggestions

try {



    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
        if (!$token || !verify_csrf_token($token)) {
            error_log("Suggestions API: CSRF token validation failed");
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token invalid or missing']);
            exit;
        }
    }


    if (!is_authenticated()) {
        $reason = !isset($_SESSION['discord_user_id']) ? "discord_user_id not set" :
                 (empty($_SESSION['discord_user_id']) ? "discord_user_id empty" :
                 (session_expired() ? "session expired" : "unknown reason"));


        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated', 'reason' => $reason]);
        exit;
    }


    if (!has_role(ROLE_TRAINEE)) {
        error_log("Search Suggestions API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to access search suggestions.']);
        exit;
    }


    refresh_session();


    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null,
        'search_suggestions' // Activity type
    );


    if ($_SERVER['REQUEST_METHOD'] === 'GET') {

        $query = isset($_GET['query']) ? sanitize_input($_GET['query']) : null;
        $type = isset($_GET['type']) ? sanitize_input($_GET['type']) : 'player';


        if (empty($query) || strlen($query) < 2) {
            http_response_code(400);
            echo json_encode(['error' => 'Query must be at least 2 characters']);
            exit;
        }

        if (!in_array($type, ['player', 'faction'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid search type']);
            exit;
        }


        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $query = sanitize_mongo_query($query);


        $regex_pattern = preg_quote($query, '/');


        $suggestions = [];
        $limit = 10; // Maximum number of suggestions

        if ($type === 'player') {


            try {

                $collections = [];
                foreach ($db->listCollections() as $collectionInfo) {
                    $collections[] = $collectionInfo->getName();
                }

                if (in_array('player_data', $collections)) {
                    $cursor = $db->player_data->find(
                        ['playernames' => ['$regex' => $regex_pattern, '$options' => 'i']],
                        [
                            'projection' => ['playernames' => 1, '_id' => 0],
                            'limit' => $limit,
                            'sort' => ['lastlogin' => -1] // Sort by most recently active
                        ]
                    );

                    foreach ($cursor as $player) {
                        if (isset($player['playernames']) && is_array($player['playernames']) && !empty($player['playernames'])) {

                            $suggestions[] = $player['playernames'][0];
                        }
                    }
                }
            } catch (Exception $e) {


            }


            if (empty($suggestions)) {

                try {
                    $cursor = $db->players->find(
                        ['username' => ['$regex' => $regex_pattern, '$options' => 'i']],
                        [
                            'projection' => ['username' => 1, '_id' => 0],
                            'limit' => $limit,
                            'sort' => ['last_login' => -1] // Sort by most recently active
                        ]
                    );

                    foreach ($cursor as $player) {
                        $suggestions[] = $player['username'];
                    }
                } catch (Exception $e) {

                }
            }


            if (empty($suggestions)) {
                $suggestions[] = $query;
            }
        } else { // faction
            $cursor = $db->factions->find(
                ['name' => ['$regex' => $regex_pattern, '$options' => 'i']],
                [
                    'projection' => ['name' => 1, '_id' => 0],
                    'limit' => $limit
                ]
            );

            foreach ($cursor as $faction) {
                $suggestions[] = $faction['name'];
            }
        }


        echo json_encode(['suggestions' => $suggestions]);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {

    error_log("Search Suggestions API Error: " . $e->getMessage());


    if (!$is_production) {
        http_response_code(500);
        echo json_encode([
            'error' => 'An internal server error occurred',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => explode("\n", $e->getTraceAsString())
        ]);
    } else {

        http_response_code(500);
        echo json_encode(['error' => 'An internal server error occurred']);
    }
}
?>
