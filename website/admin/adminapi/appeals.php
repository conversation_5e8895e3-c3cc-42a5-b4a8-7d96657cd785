<?php

header('Content-Type: application/json');
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}




if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}


if (!has_role(ROLE_ADMIN)) {
    error_log("Appeals API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['error' => 'Permission denied. You do not have the required role to access appeals.']);
    exit;
}


$headers = getallheaders();
update_staff_activity(
    $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
    $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
    $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
    $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
);


if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $options = [
            'sort' => ['created_at' => -1]
        ];


        $cursor = $db->appeals->find([], $options);

        $appeals = [];
        foreach ($cursor as $appeal) {

            $appeal_array = (array)$appeal;


            $formatted_appeal = [
                '_id' => isset($appeal_array['_id']) ? (string)$appeal_array['_id'] : 'Unknown',
                'id' => isset($appeal_array['id']) ? $appeal_array['id'] :
                      (isset($appeal_array['appeal_id']) ? $appeal_array['appeal_id'] :
                      (isset($appeal_array['_id']) ? (string)$appeal_array['_id'] : 'Unknown')),
                'player_name' => isset($appeal_array['player_name']) ? $appeal_array['player_name'] : 'Unknown',
                'status' => isset($appeal_array['status']) ? $appeal_array['status'] : 'Pending',
                'punishment_id' => isset($appeal_array['punishment_id']) ? $appeal_array['punishment_id'] : 'Unknown'
            ];


            if (isset($appeal_array['created_at'])) {
                if ($appeal_array['created_at'] instanceof MongoDB\BSON\UTCDateTime) {

                    $date = $appeal_array['created_at']->toDateTime();
                    $date->setTimezone(new DateTimeZone('America/New_York'));
                    $formatted_appeal['created_at'] = $date->format('Y-m-d H:i:s') . ' ET';
                    $formatted_appeal['created_at_formatted'] = $date->format('c'); // ISO 8601 format
                } else {

                    $formatted_appeal['created_at'] = $appeal_array['created_at'];
                }
            } else {
                $formatted_appeal['created_at'] = 'Unknown';
            }


            if (isset($appeal_array['created_at_formatted'])) {
                $formatted_appeal['created_at_formatted'] = $appeal_array['created_at_formatted'];
            } else if (isset($appeal_array['created_at']) && $appeal_array['created_at'] instanceof MongoDB\BSON\UTCDateTime) {
                $date = $appeal_array['created_at']->toDateTime();
                $date->setTimezone(new DateTimeZone('America/New_York'));
                $formatted_appeal['created_at_formatted'] = $date->format('c'); // ISO 8601 format
            }


            if (isset($appeal_array['punishment_type'])) {
                $formatted_appeal['punishment_type'] = $appeal_array['punishment_type'];
            }

            if (isset($appeal_array['offense_type'])) {
                $formatted_appeal['offense_type'] = $appeal_array['offense_type'];
            }

            if (isset($appeal_array['reason'])) {
                $formatted_appeal['reason'] = $appeal_array['reason'];
            }

            if (isset($appeal_array['history'])) {
                $formatted_appeal['history'] = $appeal_array['history'];
            }

            $appeals[] = $formatted_appeal;
        }

        echo json_encode($appeals);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to load appeals: ' . $e->getMessage()]);
    }
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $input = json_decode(file_get_contents('php://input'), true);


    if (isset($input['appeal_id']) && isset($input['action'])) {
        $appeal_id = sanitize_input($input['appeal_id']);
        $action = sanitize_input($input['action']);

        error_log("Appeals API: Processing action=$action for appeal_id=$appeal_id");

        if (in_array($action, ['approve', 'deny', 'accept'])) {
            $staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
            $staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';


            $staff_notes = '';
            if (isset($input['staff_notes'])) {
                $staff_notes = sanitize_input($input['staff_notes']);
            }


            $normalized_action = $action;
            if ($action === 'accept') {
                $normalized_action = 'approve';
            }


            update_staff_activity(
                $staff_id,
                $staff_name,
                $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
                $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null,
                $normalized_action === 'approve' ? 'Approved appeal' : 'Denied appeal',
                "Appeal ID: {$appeal_id}"
            );

            $result = handle_appeal_action($appeal_id, $normalized_action, $staff_id, $staff_name, $staff_notes);
            echo json_encode($result);
            exit;
        } else {
            error_log("Appeals API: Invalid action: $action");
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action. Must be one of: approve, deny, accept']);
            exit;
        }
    } else {
        error_log("Appeals API: Missing required fields in request");
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields: appeal_id and action']);
        exit;
    }
}

http_response_code(405);
echo json_encode(['error' => 'Method not allowed']);
?>