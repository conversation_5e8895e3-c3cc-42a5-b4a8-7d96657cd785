<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/notifications.php';
require_once __DIR__ . '/../../includes/db_access.php';


session_start();
if (!isset($_SESSION['discord_user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_DEVELOPER)) {
    error_log("Maintenance Schedule API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Developer privileges required']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}


$data = json_decode(file_get_contents('php://input'), true);


if (empty($data['title']) || empty($data['message']) || empty($data['scheduled_time']) || empty($data['duration'])) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}


$title = $data['title'];
$message = $data['message'];
$scheduledTime = $data['scheduled_time'];
$duration = intval($data['duration']);
$notifyUsers = $data['notify_users'] ?? true;

try {

    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');
    $collection = $db->selectCollection('maintenance_schedule');


    $maintenance = [
        'title' => $title,
        'message' => $message,
        'scheduled_time' => $scheduledTime,
        'duration' => $duration,
        'created_by' => $_SESSION['discord_user_id'] ?? '',
        'created_at' => date('c'),
        'status' => 'scheduled'
    ];


    $result = $collection->insertOne($maintenance);
    $success = $result->getInsertedCount() > 0;


    if ($success && $notifyUsers) {
        create_maintenance_notification(
            $title,
            $message,
            $scheduledTime,
            $duration
        );
    }


    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'maintenance_id' => $success ? (string)$result->getInsertedId() : null
    ]);

} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
