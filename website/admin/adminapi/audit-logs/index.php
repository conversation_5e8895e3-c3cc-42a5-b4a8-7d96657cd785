<?php
/**
 * Audit Logs API Endpoint (Directory Index)
 * Handles fetching audit logs from the database
 */


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


header('Content-Type: application/json');


header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (!is_authenticated()) {

    $headers = getallheaders();
    if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {

        $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
        $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
        $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
        $_SESSION['auth_time'] = time();
    } else {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authentication required']);
        exit;
    }
}


if (!has_role(ROLE_ADMIN)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - Admin role required']);
    exit;
}


refresh_session();


$headers = getallheaders();
$user_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null;
$username = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null;
$user_role = $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null;
$user_avatar = $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null;

update_staff_activity($user_id, $username, $user_role, $user_avatar);


if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Failed to connect to database");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {


            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
            $db->staff_activity_log->createIndex(['user_id' => 1]);


            $db->staff_activity_log->insertOne([
                'user_id' => 'SYSTEM',
                'username' => 'System',
                'action' => 'Created audit log collection',
                'type' => 'system',
                'target' => '',
                'details' => 'Initialized the audit logs system',
                'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                'description' => 'System initialization'
            ]);


            echo json_encode([
                'success' => true,
                'logs' => [],
                'count' => 0,
                'message' => 'No audit logs found. Collection was just created.'
            ]);
            exit;
        }


        $collection = $db->staff_activity_log;


        $filter = [];


        if (!empty($_GET['staff'])) {
            $filter['user_id'] = $_GET['staff'];
        }


        if (!empty($_GET['type'])) {
            $filter['type'] = $_GET['type'];
        }


        if (!empty($_GET['date_from']) || !empty($_GET['date_to'])) {
            $filter['timestamp'] = [];

            if (!empty($_GET['date_from'])) {
                $dateFrom = new DateTime($_GET['date_from']);
                $dateFrom->setTime(0, 0, 0);
                $filter['timestamp']['$gte'] = new MongoDB\BSON\UTCDateTime($dateFrom->getTimestamp() * 1000);
            }

            if (!empty($_GET['date_to'])) {
                $dateTo = new DateTime($_GET['date_to']);
                $dateTo->setTime(23, 59, 59);
                $filter['timestamp']['$lte'] = new MongoDB\BSON\UTCDateTime($dateTo->getTimestamp() * 1000);
            }
        }


        $options = [
            'sort' => ['timestamp' => -1], // Sort by timestamp descending (newest first)
            'limit' => 1000 // Limit to 1000 most recent logs
        ];


        $cursor = $collection->find($filter, $options);


        $logs = [];
        foreach ($cursor as $document) {

            $log = [
                'id' => (string)$document['_id'],
                'staff' => $document['username'] ?? 'Unknown',
                'action' => $document['action'] ?? $document['description'] ?? 'Unknown action',
                'type' => ucfirst($document['type'] ?? 'unknown'),
                'target' => $document['target'] ?? '',
                'details' => $document['details'] ?? '',
                'timestamp' => isset($document['timestamp']) ? $document['timestamp']->toDateTime()->format('c') : date('c')
            ];

            $logs[] = $log;
        }


        echo json_encode([
            'success' => true,
            'logs' => $logs,
            'count' => count($logs)
        ]);

    } catch (Exception $e) {

        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
} else {

    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
