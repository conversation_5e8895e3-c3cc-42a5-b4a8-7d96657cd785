<?php
/**
 * Add Player Note API Endpoint
 * Dedicated endpoint for adding player notes
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


$requestPath = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];
secure_log("Add Note API Request", "info", [
    'method' => $requestMethod,
    'path' => $requestPath,
    'user_id' => $_SESSION['discord_user_id'] ?? 'unknown'
]);

try {

    if (!is_authenticated()) {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }


    if (!has_role(ROLE_TRAINEE)) {
        error_log("Add Note API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to add notes.']);
        exit;
    }


    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }


    require_csrf_check(true); // true for API mode


    $json = file_get_contents('php://input');
    $data = json_decode($json, true);


    secure_log("Add Note POST request", "info", [
        'data' => $data,
        'url_parameters' => $_GET
    ]);


    if (!$data || !is_array($data)) {
        secure_log("No valid JSON data in request body", "error");
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        exit;
    }


    if (!isset($data['username']) && isset($_GET['username'])) {
        $data['username'] = $_GET['username'];
        error_log("Using username from URL parameter: " . $data['username']);
    }

    if (!isset($data['xuid']) && isset($_GET['xuid'])) {
        $data['xuid'] = $_GET['xuid'];
        error_log("Using xuid from URL parameter: " . $data['xuid']);
    }


    if (!isset($data['xuid']) || !isset($data['username']) || !isset($data['content']) || !isset($data['type'])) {
        secure_log("Missing required fields in request data", "error", [
            'data' => $data
        ]);
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit;
    }


    $xuid_validation = validate_input($data['xuid'], 'text', ['required' => true]);
    $username_validation = validate_input($data['username'], 'text', ['required' => true]);
    $content_validation = validate_input($data['content'], 'text', ['required' => true, 'min_length' => 3, 'max_length' => 2000]);
    $type_validation = validate_input($data['type'], 'enum', ['required' => true, 'allowed' => ['behavior', 'support', 'payment', 'bug', 'other']]);


    if (!$xuid_validation['valid']) {
        secure_log("Invalid XUID in request", "error", ['error' => $xuid_validation['error']]);
        http_response_code(400);
        echo json_encode(['error' => 'Invalid XUID: ' . $xuid_validation['error']]);
        exit;
    }

    if (!$username_validation['valid']) {
        secure_log("Invalid username in request", "error", ['error' => $username_validation['error']]);
        http_response_code(400);
        echo json_encode(['error' => 'Invalid username: ' . $username_validation['error']]);
        exit;
    }

    if (!$content_validation['valid']) {
        secure_log("Invalid note content in request", "error", ['error' => $content_validation['error']]);
        http_response_code(400);
        echo json_encode(['error' => 'Invalid note content: ' . $content_validation['error']]);
        exit;
    }

    if (!$type_validation['valid']) {
        secure_log("Invalid note type in request", "error", ['error' => $type_validation['error']]);
        http_response_code(400);
        echo json_encode(['error' => 'Invalid note type: ' . $type_validation['error']]);
        exit;
    }


    $xuid = $xuid_validation['value'];
    $username = $username_validation['value'];
    $content = $content_validation['value'];
    $type = $type_validation['value'];
    $important = isset($data['important']) ? (bool)$data['important'] : false;


    $staff_id = $_SESSION['user_id'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
    $staff_name = $_SESSION['username'] ?? $_SESSION['discord_username'] ?? 'Unknown Staff';

    error_log("Adding note for player: $username ($xuid) by staff: $staff_name ($staff_id)");


    try {

        $connection = get_db_connection();
        $client = $connection['client'];
        $db = $client->selectDatabase('mmc');

        error_log("Connected to database: " . $db->getDatabaseName());


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        error_log("Available collections: " . implode(', ', $collections));

        if (!in_array('player_notes', $collections)) {
            error_log("Creating player_notes collection");
            $db->createCollection('player_notes');
            $db->player_notes->createIndex(['xuid' => 1]);
            $db->player_notes->createIndex(['username' => 1]);
            $db->player_notes->createIndex(['created_at' => -1]);
        }


        $note = [
            'xuid' => $xuid,
            'username' => $username,
            'content' => $content,
            'type' => $type,
            'important' => (bool)$important,
            'staff_id' => $staff_id,
            'staff_member' => $staff_name,
            'created_at' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'note_id' => uniqid('note_')
        ];

        error_log("Inserting note: " . json_encode($note));


        $result = $db->player_notes->insertOne($note);

        error_log("Insert result: " . json_encode([
            'insertedCount' => $result->getInsertedCount(),
            'insertedId' => (string)$result->getInsertedId(),
            'acknowledged' => $result->isAcknowledged()
        ]));

        if ($result->getInsertedCount() === 0) {
            throw new Exception("Failed to insert note");
        }


        try {
            $activity = [
                'user_id' => $staff_id,
                'username' => $staff_name,
                'action' => "Added note to player $username",
                'type' => 'note',
                'target' => $username,
                'details' => "Note type: $type" . ($important ? ' (Important)' : ''),
                'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                'description' => "Added " . ($important ? 'important ' : '') . "$type note to $username"
            ];

            $db->staff_activity_log->insertOne($activity);
            error_log("Staff activity logged for adding note");
        } catch (Exception $e) {
            error_log("Error logging staff activity for note: " . $e->getMessage());

        }


        echo json_encode([
            'success' => true,
            'message' => 'Note added successfully',
            'note_id' => $note['note_id']
        ]);

    } catch (Exception $e) {
        error_log("Error adding note directly: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to add note: ' . $e->getMessage()]);
    }

} catch (Exception $e) {

    error_log("Add Note API Error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());


    http_response_code(500);
    echo json_encode(['error' => 'An internal server error occurred']);
}
?>
