<?php
/**
 * List Announcements API Endpoint
 * Retrieves announcements for the admin panel
 */


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


error_log("List Announcements API Request - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("List Announcements API Request - Session ID: " . session_id());
error_log("List Announcements API Request - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));
error_log("List Announcements API Request - User Role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));


if (!is_authenticated()) {
    error_log("List Announcements API - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    error_log("List Announcements API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Permission denied. You do not have the required role to access announcements.']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            error_log("List Announcements API - Database connection failed");
            throw new Exception("Failed to connect to database");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('announcements', $collections)) {
            error_log("List Announcements API - Announcements collection does not exist");
            echo json_encode([
                'success' => true,
                'announcements' => [],
                'count' => 0
            ]);
            exit;
        }


        $user_id = $_SESSION['discord_user_id'];
        $filter = [
            '$or' => [
                ['is_global' => true],
                ['recipients' => $user_id]
            ]
        ];


        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;


        $cursor = $db->announcements->find($filter, [
            'sort' => ['timestamp' => -1],
            'limit' => $limit
        ]);


        $announcements = [];
        foreach ($cursor as $document) {

            try {
                $announcement = [
                    'id' => (string)$document['_id'],
                    'title' => $document['title'] ?? 'No Title',
                    'message' => $document['message'] ?? 'No Message',
                    'sender_name' => $document['sender_name'] ?? 'Unknown',
                    'timestamp' => isset($document['timestamp']) ? $document['timestamp']->toDateTime()->format('c') : date('c'),
                    'is_global' => $document['is_global'] ?? false,
                    'is_read' => false // Default value
                ];
            } catch (Exception $e) {
                error_log("Error processing announcement document: " . $e->getMessage());

                continue;
            }


            try {
                if (isset($document['read_by'])) {
                    $read_by_array = iterator_to_array($document['read_by']);
                    $announcement['is_read'] = in_array($user_id, $read_by_array);
                }
            } catch (Exception $e) {
                error_log("Error processing read_by field: " . $e->getMessage());

            }

            $announcements[] = $announcement;
        }


        echo json_encode([
            'success' => true,
            'announcements' => $announcements,
            'count' => count($announcements)
        ]);

    } catch (Exception $e) {

        error_log("List Announcements API Error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());


        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
} else {

    error_log("List Announcements API - Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
