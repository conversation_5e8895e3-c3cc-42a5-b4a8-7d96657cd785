<?php
/**
 * Staff Application Details API
 * Get detailed information about a specific staff application
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';

// Set content type
header('Content-Type: text/html');

// Require admin authentication
require_auth(ROLE_ADMIN);

try {
    $application_id = $_GET['id'] ?? null;
    
    if (!$application_id) {
        echo '<div class="alert alert-danger">Application ID is required</div>';
        exit;
    }
    
    // Get application from database
    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        echo '<div class="alert alert-danger">Database connection failed</div>';
        exit;
    }

    $application = $db->staff_applications->findOne([
        'application_id' => $application_id
    ]);

    if (!$application) {
        echo '<div class="alert alert-danger">Application not found</div>';
        exit;
    }

    // Get Xbox username for this applicant
    $xbox_username = null;
    try {
        if (isset($application['discord_id'])) {
            $xbox_link = $db->linked_accounts->findOne([
                'discord_id' => $application['discord_id'],
                'platform' => 'xbox',
                'status' => 'verified'
            ]);
            if ($xbox_link) {
                $xbox_username = $xbox_link['username'];
            }
        }
    } catch (Exception $e) {
        error_log("Error fetching Xbox username for application details: " . $e->getMessage());
    }
    
    // Display application details
    ?>
    <div class="row">
        <div class="col-md-6">
            <h6><i class="fas fa-user me-2 text-primary"></i>Applicant Information</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Discord Username:</strong></td>
                    <td><?php echo htmlspecialchars($application['discord_username']); ?></td>
                </tr>
                <tr>
                    <td><strong>Discord ID:</strong></td>
                    <td><?php echo htmlspecialchars($application['discord_id']); ?></td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td><?php echo htmlspecialchars($application['discord_email'] ?? 'Not provided'); ?></td>
                </tr>
                <tr>
                    <td><strong>Xbox Account:</strong></td>
                    <td>
                        <?php if ($xbox_username): ?>
                            <span class="badge bg-success">
                                <?php echo htmlspecialchars($xbox_username); ?>
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-exclamation-triangle me-1"></i>No Xbox Linked
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>Position:</strong></td>
                    <td><span class="badge bg-primary"><?php echo htmlspecialchars(ucfirst($application['position'])); ?></span></td>
                </tr>
                <tr>
                    <td><strong>Age:</strong></td>
                    <td><?php echo htmlspecialchars($application['age']); ?> years old</td>
                </tr>
                <tr>
                    <td><strong>Timezone:</strong></td>
                    <td><?php echo htmlspecialchars($application['timezone']); ?></td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <?php
                        $status = $application['status'];
                        $statusClass = '';
                        switch ($status) {
                            case 'pending':
                                $statusClass = 'bg-warning text-dark';
                                break;
                            case 'approved':
                                $statusClass = 'bg-success';
                                break;
                            case 'denied':
                                $statusClass = 'bg-danger';
                                break;
                            case 'under_review':
                                $statusClass = 'bg-info';
                                break;
                            default:
                                $statusClass = 'bg-secondary';
                        }
                        ?>
                        <span class="badge <?php echo $statusClass; ?>">
                            <?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $status))); ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Submitted:</strong></td>
                    <td><?php echo $application['submitted_at']->toDateTime()->format('M j, Y \a\t g:i A'); ?></td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6><i class="fas fa-clipboard-list me-2 text-primary"></i>Application Responses</h6>
            
            <div class="mb-3">
                <strong>Previous Experience:</strong>
                <div class="border rounded p-2 bg-dark border-secondary">
                    <?php echo nl2br(htmlspecialchars($application['experience'])); ?>
                </div>
            </div>

            <div class="mb-3">
                <strong>Why do you want to be staff?</strong>
                <div class="border rounded p-2 bg-dark border-secondary">
                    <?php echo nl2br(htmlspecialchars($application['why_staff'])); ?>
                </div>
            </div>

            <div class="mb-3">
                <strong>Scenario Response:</strong>
                <div class="border rounded p-2 bg-dark border-secondary">
                    <?php echo nl2br(htmlspecialchars($application['scenario_response'])); ?>
                </div>
            </div>

            <div class="mb-3">
                <strong>Availability:</strong>
                <div class="border rounded p-2 bg-dark border-secondary">
                    <?php echo nl2br(htmlspecialchars($application['availability'])); ?>
                </div>
            </div>

            <?php if (!empty($application['additional_info'])): ?>
            <div class="mb-3">
                <strong>Additional Information:</strong>
                <div class="border rounded p-2 bg-dark border-secondary">
                    <?php echo nl2br(htmlspecialchars($application['additional_info'])); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($application['reviewer_notes']) && !empty($application['reviewer_notes'])): ?>
            <div class="mb-3">
                <strong>Staff Notes:</strong>
                <div class="border rounded p-2 bg-warning bg-opacity-10 border-warning">
                    <?php echo nl2br(htmlspecialchars($application['reviewer_notes'])); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if ($application['status'] === 'pending'): ?>
    <div class="mt-4 text-center">
        <button class="btn btn-success me-2" onclick="updateStatusFromModal('<?php echo $application['application_id']; ?>', 'approved')">
            <i class="fas fa-check me-1"></i>Approve Application
        </button>
        <button class="btn btn-danger" onclick="updateStatusFromModal('<?php echo $application['application_id']; ?>', 'denied')">
            <i class="fas fa-times me-1"></i>Deny Application
        </button>
    </div>
    
    <script>
        function updateStatusFromModal(applicationId, status) {
            // Close the modal first
            $('#applicationModal').modal('hide');
            
            // Call the main update function
            updateStatus(applicationId, status);
        }
    </script>
    <?php endif; ?>
    
    <?php
    
} catch (Exception $e) {
    error_log("Error fetching staff application details: " . $e->getMessage());

    echo '<div class="alert alert-danger">An error occurred while loading application details</div>';
}
?>
