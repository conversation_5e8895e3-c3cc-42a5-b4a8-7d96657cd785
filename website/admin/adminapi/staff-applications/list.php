<?php
/**
 * Staff Applications List API
 * Get filtered list of staff applications
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../../includes/db_access.php'; // Main website's DatabaseAccess class

// Set content type
header('Content-Type: text/html');

// Require admin authentication
require_auth(ROLE_ADMIN);

try {
    // Get filter parameters
    $input = json_decode(file_get_contents('php://input'), true);
    $statusFilter = $input['status'] ?? 'all';
    $positionFilter = $input['position'] ?? 'all';
    $searchFilter = $input['search'] ?? '';
    
    // Get database connection
    $dbAccess = new DatabaseAccess();
    if (!$dbAccess || !$dbAccess->db) {
        echo '<div class="alert alert-danger">Database connection failed</div>';
        exit;
    }
    
    // Build query
    $query = [];
    
    if ($statusFilter !== 'all') {
        $query['status'] = $statusFilter;
    }
    
    if ($positionFilter !== 'all') {
        $query['position'] = $positionFilter;
    }
    
    if (!empty($searchFilter)) {
        $query['$or'] = [
            ['discord_username' => new MongoDB\BSON\Regex($searchFilter, 'i')],
            ['discord_id' => new MongoDB\BSON\Regex($searchFilter, 'i')]
        ];
    }
    
    // Get applications
    $applications = [];
    try {
        $applicationsData = $dbAccess->db->staff_applications->find($query, [
            'sort' => ['submitted_at' => -1],
            'maxTimeMS' => 5000
        ]);
        
        $applications = iterator_to_array($applicationsData);
    } catch (MongoDB\Driver\Exception\RuntimeException $e) {
        if (strpos($e->getMessage(), 'not found') !== false) {
            $applications = [];
        } else {
            throw $e;
        }
    }
    
    if (empty($applications)) {
        echo '<div class="text-center py-5">
                <i class="fas fa-user-tie fa-5x text-muted mb-4"></i>
                <h3>No Applications Found</h3>
                <p class="text-muted">No staff applications match your current filters.</p>
              </div>';
        exit;
    }
    
    // Display applications table
    ?>
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Applicant</th>
                            <th>Position</th>
                            <th>Age</th>
                            <th>Timezone</th>
                            <th>Status</th>
                            <th>Submitted</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($applications as $app): ?>
                        <tr class="application-row" data-status="<?php echo htmlspecialchars($app['status']); ?>">
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($app['discord_username']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo htmlspecialchars($app['discord_id']); ?></small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?php echo htmlspecialchars(ucfirst($app['position'])); ?></span>
                            </td>
                            <td><?php echo htmlspecialchars($app['age']); ?></td>
                            <td><?php echo htmlspecialchars($app['timezone']); ?></td>
                            <td>
                                <?php
                                $status = $app['status'];
                                $statusClass = '';
                                switch ($status) {
                                    case 'pending':
                                        $statusClass = 'bg-warning text-dark';
                                        break;
                                    case 'approved':
                                        $statusClass = 'bg-success';
                                        break;
                                    case 'denied':
                                        $statusClass = 'bg-danger';
                                        break;
                                    case 'under_review':
                                        $statusClass = 'bg-info';
                                        break;
                                    default:
                                        $statusClass = 'bg-secondary';
                                }
                                ?>
                                <span class="badge <?php echo $statusClass; ?>">
                                    <?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $status))); ?>
                                </span>
                            </td>
                            <td>
                                <small><?php echo $app['submitted_at']->toDateTime()->format('M j, Y g:i A'); ?></small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewApplication('<?php echo $app['application_id']; ?>')" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if ($app['status'] === 'pending'): ?>
                                    <button class="btn btn-outline-success" onclick="updateStatus('<?php echo $app['application_id']; ?>', 'approved')" title="Approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="updateStatus('<?php echo $app['application_id']; ?>', 'denied')" title="Deny">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    error_log("Error fetching staff applications list: " . $e->getMessage());
    echo '<div class="alert alert-danger">An error occurred while loading applications</div>';
}
?>
