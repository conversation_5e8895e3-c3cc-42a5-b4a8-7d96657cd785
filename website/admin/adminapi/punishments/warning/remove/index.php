<?php

require_once __DIR__ . '/../../../../includes/config.php';
require_once __DIR__ . '/../../../../includes/auth.php';
require_once __DIR__ . '/../../../../includes/db_access.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}


if (!is_authenticated()) {

    $headers = getallheaders();
    if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {

        $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
        $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
        $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
        $_SESSION['auth_time'] = time();

        error_log("Warning Remove API: Using header information for authentication");
    } else {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authentication required']);
        exit;
    }
}


refresh_session();


if (!has_role(ROLE_ADMIN)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'You do not have permission to access this resource']);
    exit;
}


$headers = getallheaders();
$staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
$staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';


if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);


    if (empty($data['punishment_id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Punishment ID is required']);
        exit;
    }

    try {

        $punishment_id = $data['punishment_id'];


        $reason = $data['reason'] ?? 'Manually removed by staff';


        $result = remove_punishment(
            $punishment_id,         // punishment ID
            $staff_id,              // staff ID
            $staff_name,            // staff name
            $reason                 // reason for removal
        );


        echo json_encode($result);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
