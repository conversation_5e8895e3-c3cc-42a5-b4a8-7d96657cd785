<?php

require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


header('Content-Type: application/json');


header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


$headers = getallheaders();
error_log("Punishment Details API: Headers: " . json_encode($headers));


if (!is_authenticated()) {

    if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {

        $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
        $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
        $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
        $_SESSION['auth_time'] = time();

        error_log("Punishment Details API: Using header information for authentication");
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }
}


refresh_session();


if (!has_role(ROLE_TRAINEE)) {
    error_log("Punishment Details API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['error' => 'Permission denied. You do not have the required role to access this resource.']);
    exit;
}


$headers = getallheaders();
update_staff_activity(
    $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
    $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
    $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
    $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
);


if ($_SERVER['REQUEST_METHOD'] === 'GET') {

    if (empty($_GET['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing punishment ID']);
        exit;
    }

    $punishment_id = $_GET['id'];

    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('punishments', $collections)) {
            http_response_code(404);
            echo json_encode(['error' => 'Punishment not found']);
            exit;
        }


        $punishment = null;


        if (strpos($punishment_id, 'PUN-') === 0) {
            $punishment = $db->punishments->findOne(['punishment_id' => $punishment_id]);
        }


        if (!$punishment) {
            $punishment = $db->punishments->findOne(['_id' => $punishment_id]);
        }


        if (!$punishment && strlen($punishment_id) == 24) {
            try {
                $objectId = new MongoDB\BSON\ObjectId($punishment_id);
                $punishment = $db->punishments->findOne(['_id' => $objectId]);
            } catch (Exception $e) {

            }
        }

        if (!$punishment) {
            http_response_code(404);
            echo json_encode(['error' => 'Punishment not found']);
            exit;
        }


        $result = [
            'punishment_id' => $punishment['punishment_id'] ?? ('PUN-' . substr((string)$punishment['_id'], -8)),
            'punishment_type' => $punishment['punishment_type'] ?? 'unknown',
            'player_name' => $punishment['player_name'] ?? 'Unknown Player',
            'player_xuid' => $punishment['player_xuid'] ?? '',
            'reason' => $punishment['reason'] ?? '',
            'staff_name' => $punishment['staff_name'] ?? 'Unknown Staff',
            'staff_id' => $punishment['staff_id'] ?? '',
            'active' => $punishment['active'] ?? false,
            'issued_at' => isset($punishment['issued_at']) ? $punishment['issued_at']->toDateTime()->format('c') : null,
            'expires_at' => isset($punishment['expires_at']) ? $punishment['expires_at']->toDateTime()->format('c') : null,
            'removed_at' => isset($punishment['removed_at']) ? $punishment['removed_at']->toDateTime()->format('c') : null,
            'removed_by' => $punishment['removed_by'] ?? null,
            'removed_by_name' => $punishment['removed_by_name'] ?? null,
            'removed_reason' => $punishment['removed_reason'] ?? null,
            'evidence' => $punishment['evidence'] ?? '',
            'staff_notes' => $punishment['staff_notes'] ?? '',
            'offense_type' => $punishment['offense_type'] ?? '',
            'offense_count' => $punishment['offense_count'] ?? 1,
            'public' => $punishment['public'] ?? true
        ];


        echo json_encode($result);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
?>
