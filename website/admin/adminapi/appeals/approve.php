<?php
/**
 * Appeal Approve API Endpoint
 * Handles approving an appeal
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (getenv('APP_ENV') !== 'production') {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    error_log("Appeal Approve API: Endpoint called");
}


if (session_status() === PHP_SESSION_NONE) {
    session_start();
    if (getenv('APP_ENV') !== 'production') {
        error_log("Appeal Approve API: Started new session, ID: " . session_id());
    }
} else {
    if (getenv('APP_ENV') !== 'production') {
        error_log("Appeal Approve API: Using existing session, ID: " . session_id());
    }
}


apply_rate_limit($_SERVER['REMOTE_ADDR'], 60, 60); // 60 requests per minute

try {

    if (!is_authenticated()) {
        if (getenv('APP_ENV') !== 'production') {
            error_log("Appeal Approve API: Authentication failed - User not authenticated");
            error_log("Appeal Approve API: Session data: " . json_encode($_SESSION));
        }


        $headers = getallheaders();
        if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {

            $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
            $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
            $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
            $_SESSION['auth_time'] = time();

            error_log("Appeal Approve API: Using header information for authentication");
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Not authenticated']);
            exit;
        }
    }


    if (!has_role(ROLE_ADMIN)) {
        error_log("Appeal Approve API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to approve appeals.']);
        exit;
    }


    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );


    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }


    $appeal_id = null;


    if (isset($_GET['id']) && !empty($_GET['id'])) {
        $appeal_id = sanitize_input($_GET['id']);
        error_log("Appeal Approve API: Got appeal_id=$appeal_id from query string");
    } else {

        $uri_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        error_log("Appeal Approve API: URI parts: " . json_encode($uri_parts));



        if (count($uri_parts) >= 4) {
            $appeals_pos = array_search('appeals', $uri_parts);
            if ($appeals_pos !== false && isset($uri_parts[$appeals_pos + 1]) &&
                (isset($uri_parts[$appeals_pos + 2]) && $uri_parts[$appeals_pos + 2] === 'approve')) {
                $appeal_id = sanitize_input($uri_parts[$appeals_pos + 1]);
                error_log("Appeal Approve API: Extracted appeal_id=$appeal_id from URL path (pattern 1)");
            }
        }


        if (empty($appeal_id) && count($uri_parts) >= 3) {
            $appeals_pos = array_search('appeals.php', $uri_parts);
            if ($appeals_pos !== false && isset($uri_parts[$appeals_pos + 1])) {
                $appeal_id = sanitize_input($uri_parts[$appeals_pos + 1]);
                error_log("Appeal Approve API: Extracted appeal_id=$appeal_id from URL path (pattern 2)");
            }
        }


        if (empty($appeal_id) && count($uri_parts) >= 2) {
            $appeal_id = sanitize_input($uri_parts[count($uri_parts) - 2]);
            error_log("Appeal Approve API: Extracted appeal_id=$appeal_id from URL path (fallback method)");
        }
    }


    if (empty($appeal_id)) {
        error_log("Appeal Approve API: No appeal ID found in request");
        http_response_code(400);
        echo json_encode(['error' => 'Missing appeal ID']);
        exit;
    }


    $input = json_decode(file_get_contents('php://input'), true);
    $staff_notes = '';

    if (!$input && isset($_POST['staff_notes'])) {

        $staff_notes = sanitize_input($_POST['staff_notes']);
    } elseif ($input && isset($input['staff_notes'])) {

        $staff_notes = sanitize_input($input['staff_notes']);
    }


    $staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
    $staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';

    error_log("Appeal Approve API: Processing approve action for appeal_id=$appeal_id by staff=$staff_name");

    $result = handle_appeal_action($appeal_id, 'approve', $staff_id, $staff_name, $staff_notes);
    echo json_encode($result);

} catch (Exception $e) {

    error_log("Appeal Approve API Error: " . $e->getMessage());
    error_log("Appeal Approve API Error Stack Trace: " . $e->getTraceAsString());


    http_response_code(500);
    echo json_encode(['error' => 'An internal server error occurred']);
}
?>
