<?php
/**
 * Reports API Endpoint
 * Handles player reports management
 *
 * Security features:
 * - Email addresses are redacted for all users
 * - IP addresses are only visible to users with DEVELOPER role
 */

header('Content-Type: application/json');

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/auth.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

apply_rate_limit($_SERVER['REMOTE_ADDR'], 60, 60); // 60 requests per minute

try {
    if (!is_authenticated()) {
        $headers = getallheaders();
        if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {
            $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
            $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
            $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
            $_SESSION['auth_time'] = time();
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Not authenticated']);
            exit;
        }
    }

    if (!has_role(ROLE_TRAINEE)) {
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to access reports.']);
        exit;
    }

    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        try {
            $limit = isset($_GET['limit']) ? min(intval($_GET['limit']), 200) : 100;
            $offset = isset($_GET['offset']) ? max(intval($_GET['offset']), 0) : 0;

            $reports = get_reports($limit, $offset);

            // Remove sensitive data for security
            foreach ($reports as &$report) {
                // Remove IP addresses for non-developers
                if (!has_role(ROLE_DEVELOPER)) {
                    unset($report['ip_address']);
                }

                // Remove email addresses for all users
                unset($report['reporter_email']);
                unset($report['reporters_email']);
            }
            unset($report); // Break the reference

            echo json_encode($reports);
        } catch (Exception $e) {
            error_log("Reports API Error in GET: " . $e->getMessage());

            if (strpos($e->getMessage(), 'No servers yet eligible for rescan') !== false) {
                http_response_code(503); // Service Unavailable
                echo json_encode([
                    'error' => 'Database is initializing. Please try again in a moment.',
                    'retry_after' => 5, // Suggest retry after 5 seconds
                    'reports' => [] // Return empty array instead of error
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to fetch reports: ' . $e->getMessage()]);
            }
        }
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        require_csrf_check(true, false); // (API mode, not strict)

        $uri_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));

        if (count($uri_parts) >= 3) {
            $report_id = sanitize_input($uri_parts[count($uri_parts) - 2]);
            $action = sanitize_input($uri_parts[count($uri_parts) - 1]);

            if (in_array($action, ['accept', 'reject'])) {
                $staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
                $staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';

                $result = handle_report_action($report_id, $action, $staff_id, $staff_name);
                echo json_encode($result);
                exit;
            }
        }

        http_response_code(400);
        echo json_encode(['error' => 'Invalid request format']);
        exit;
    }

    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);

} catch (Exception $e) {
    error_log("Reports API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>