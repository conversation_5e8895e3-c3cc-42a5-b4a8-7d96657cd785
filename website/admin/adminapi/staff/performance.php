<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';


session_start();
if (!isset($_SESSION['discord_user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    error_log("Staff Performance API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Permission denied. You do not have the required role to access staff performance.']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    require_csrf_check(true);
}


$userId = $_SESSION['discord_user_id'] ?? '';

if (empty($userId)) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'User ID not found']);
    exit;
}


$timePeriod = $_GET['period'] ?? '30days';
$periodLabel = '';


$now = new DateTime();
$startDate = new DateTime();

switch ($timePeriod) {
    case '7days':
        $startDate->modify('-7 days');
        $periodLabel = 'Last 7 days';
        break;
    case '90days':
        $startDate->modify('-90 days');
        $periodLabel = 'Last 90 days';
        break;
    case 'year':
        $startDate->modify('-1 year');
        $periodLabel = 'Last year';
        break;
    case 'alltime':
        $startDate->modify('-10 years'); // Effectively "all time"
        $periodLabel = 'All time';
        break;
    case '30days':
    default:
        $startDate->modify('-30 days');
        $periodLabel = 'Last 30 days';
        break;
}


try {
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');


    $performance = [
        'reportsHandled' => 0,
        'playerNotes' => 0,
        'punishmentsIssued' => 0,
        'announcementsSent' => 0,
        'reportAcceptRate' => 0,
        'timePeriod' => $periodLabel
    ];


    $reportsCollection = $db->selectCollection('reports');


    $startDateObj = new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000);

    $reportsFilter = [
        'processed_by' => $userId,
        'processed_at' => [
            '$gte' => $startDateObj
        ]
    ];

    try {
        $performance['reportsHandled'] = $reportsCollection->countDocuments($reportsFilter);


        $acceptedReportsFilter = array_merge($reportsFilter, ['status' => 'Accepted']);
        $acceptedCount = $reportsCollection->countDocuments($acceptedReportsFilter);

        if ($performance['reportsHandled'] > 0) {
            $performance['reportAcceptRate'] = $acceptedCount / $performance['reportsHandled'];
        }
    } catch (Exception $e) {
        secure_log("Error counting reports", "error", [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        $performance['reportsHandled'] = 0;
        $performance['reportAcceptRate'] = 0;
    }


    $notesCollection = $db->selectCollection('player_notes');
    $notesFilter = [
        'staff_id' => $userId,
        'created_at' => [
            '$gte' => new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000)
        ]
    ];

    try {
        $performance['playerNotes'] = $notesCollection->countDocuments($notesFilter);


        if ($performance['playerNotes'] == 0) {
            $altNotesFilter = [
                'staff_member' => $_SESSION['discord_username'] ?? '',
                'created_at' => [
                    '$gte' => new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000)
                ]
            ];

            if (!empty($altNotesFilter['staff_member'])) {
                $performance['playerNotes'] = $notesCollection->countDocuments($altNotesFilter);
            }
        }
    } catch (Exception $e) {
        secure_log("Error counting notes", "error", [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        $performance['playerNotes'] = 0;
    }


    $punishmentsCollection = $db->selectCollection('punishments');
    $punishmentsFilter = [
        'issued_by' => $userId,
        'issued_at' => [
            '$gte' => new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000)
        ]
    ];

    try {
        $performance['punishmentsIssued'] = $punishmentsCollection->countDocuments($punishmentsFilter);


        if ($performance['punishmentsIssued'] == 0) {
            $altPunishmentsFilter = [
                'staff_name' => $_SESSION['discord_username'] ?? '',
                'issued_at' => [
                    '$gte' => new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000)
                ]
            ];

            if (!empty($altPunishmentsFilter['staff_name'])) {
                $performance['punishmentsIssued'] = $punishmentsCollection->countDocuments($altPunishmentsFilter);
            }
        }


        try {
            $activityCollection = $db->selectCollection('staff_activity_log');
            $activityFilter = [
                'user_id' => $userId,
                'type' => 'punishment',
                'timestamp' => [
                    '$gte' => new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000)
                ]
            ];

            $activityPunishmentCount = $activityCollection->countDocuments($activityFilter);
            $performance['punishmentsIssued'] += $activityPunishmentCount;
        } catch (Exception $e) {
            secure_log("Error counting punishment activities", "error", [
                'error' => $e->getMessage(),
                'user_id' => $userId
            ]);
        }
    } catch (Exception $e) {
        secure_log("Error counting punishments", "error", [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        $performance['punishmentsIssued'] = 0;
    }


    try {
        $activityCollection = $db->selectCollection('staff_activity_log');
        $announcementsFilter = [
            'user_id' => $userId,
            'type' => 'announcement',
            'timestamp' => [
                '$gte' => new MongoDB\BSON\UTCDateTime($startDate->getTimestamp() * 1000)
            ]
        ];

        $performance['announcementsSent'] = $activityCollection->countDocuments($announcementsFilter);
    } catch (Exception $e) {
        secure_log("Error counting announcements", "error", [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        $performance['announcementsSent'] = 0;
    }





    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'performance' => $performance
    ]);

} catch (Exception $e) {
    secure_log("Database error in staff performance API", "error", [
        'error' => $e->getMessage(),
        'user_id' => $userId
    ]);

    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
