<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';


session_start();
if (!isset($_SESSION['discord_user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    error_log("Staff Activity API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Permission denied. You do not have the required role to access staff activity.']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    require_csrf_check(true);
}


$userId = $_SESSION['discord_user_id'] ?? '';

if (empty($userId)) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'User ID not found']);
    exit;
}


try {
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');


    $activities = [];


    $logCollection = $db->selectCollection('staff_activity_log');
    $filter = ['user_id' => $userId];
    $options = [
        'sort' => ['timestamp' => -1], // Sort by timestamp descending (newest first)
        'limit' => 20 // Limit to 20 most recent activities
    ];

    $cursor = $logCollection->find($filter, $options);


    foreach ($cursor as $document) {

        $activity = [
            'id' => (string)$document['_id'],
            'type' => $document['type'] ?? 'unknown',
            'description' => $document['action'] ?? $document['description'] ?? 'Unknown activity',
            'details' => $document['details'] ?? null
        ];


        if (isset($document['timestamp'])) {
            if ($document['timestamp'] instanceof MongoDB\BSON\UTCDateTime) {
                $activity['timestamp'] = $document['timestamp']->toDateTime()->format('c');
            } else {
                $activity['timestamp'] = $document['timestamp'];
            }
        } else {
            $activity['timestamp'] = date('c');
        }

        $activities[] = $activity;
    }


    if (count($activities) < 20) {
        $activityCollection = $db->selectCollection('staff_activity');
        $filter = ['user_id' => $userId];
        $options = [
            'sort' => ['last_active' => -1], // Sort by last_active descending
            'limit' => 20 - count($activities) // Only get what we need to reach 20
        ];

        $cursor = $activityCollection->find($filter, $options);


        foreach ($cursor as $document) {

            if (isset($document['last_active'])) {

                $now = time();
                $login_time = $document['last_active']->toDateTime()->getTimestamp();
                $time_diff = $now - $login_time;


                if ($time_diff > 300) {
                    $activity = [
                        'id' => (string)$document['_id'],
                        'type' => 'login',
                        'description' => 'Logged in to admin panel',
                        'details' => null
                    ];


                    if ($document['last_active'] instanceof MongoDB\BSON\UTCDateTime) {
                        $date = $document['last_active']->toDateTime();


                        $date->setTimezone(new DateTimeZone('America/New_York'));


                        $activity['formatted_date'] = $date->format('M j, Y g:i A') . ' ET';
                        $activity['timestamp'] = $date->format('c'); // ISO 8601 format
                    } else {
                        $activity['timestamp'] = $document['last_active'];
                    }

                    $activities[] = $activity;
                }
            }
        }
    }


    usort($activities, function($a, $b) {
        $timeA = is_string($a['timestamp']) ? strtotime($a['timestamp']) : 0;
        $timeB = is_string($b['timestamp']) ? strtotime($b['timestamp']) : 0;
        return $timeB - $timeA; // Descending order
    });





    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'activities' => $activities
    ]);

} catch (Exception $e) {
    secure_log("Database error in staff activity API", "error", [
        'error' => $e->getMessage(),
        'user_id' => $userId
    ]);

    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
