<?php

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 


require_auth(ROLE_ADMIN); // ADMIN_ROLE or higher (DEVELOP<PERSON> has access to ADMIN resources)
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Staff Management | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <script>
        window.AUTH_DATA = {
            user_id: "<?php echo $_SESSION['discord_user_id'] ?? ''; ?>",
            username: "<?php echo $_SESSION['discord_username'] ?? ''; ?>",
            role: "<?php echo $_SESSION['discord_user_role'] ?? ''; ?>",
            avatar: "<?php echo $_SESSION['discord_avatar'] ?? ''; ?>"
        };
    </script>
    <link rel="stylesheet" href="/css/admin-common.css">
    <link rel="stylesheet" href="/css/mobile.css">
    <link rel="stylesheet" href="/css/mobile-fixes.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500&display=swap">
    <style>
        .staff-table th, .staff-table td {
            vertical-align: middle;
        }

        .staff-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: 600;
            margin-right: 0.75rem;
            overflow: hidden;
        }

        .staff-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .staff-info {
            display: flex;
            align-items: center;
        }

        .staff-info .staff-name {
            font-weight: 600;
            margin-bottom: 0;
        }

        .role-badge {
            padding: 0.35rem 0.65rem;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-owner {
            background-color: rgba(155, 89, 182, 0.15);
            color: #9b59b6;
        }

        .role-developer {
            background-color: rgba(41, 128, 185, 0.15);
            color: #2980b9;
        }

        .role-supervisor {
            background-color: rgba(22, 160, 133, 0.15);
            color: #16a085;
        }

        .role-admin {
            background-color: rgba(231, 76, 60, 0.15);
            color: #e74c3c;
        }

        .role-moderator {
            background-color: rgba(243, 156, 18, 0.15);
            color: #f39c12;
        }

        .role-trainee {
            background-color: rgba(142, 68, 173, 0.15);
            color: #8e44ad;
        }

        .last-active {
            white-space: nowrap;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        /* DataTables dark mode fixes */
        body.dark-mode .dataTables_wrapper .dataTables_length,
        body.dark-mode .dataTables_wrapper .dataTables_filter,
        body.dark-mode .dataTables_wrapper .dataTables_info,
        body.dark-mode .dataTables_wrapper .dataTables_processing,
        body.dark-mode .dataTables_wrapper .dataTables_paginate {
            color: var(--text-light);
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button {
            color: var(--text-light) !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            color: #333 !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_length select,
        body.dark-mode .dataTables_wrapper .dataTables_filter input {
            background-color: var(--bg-darker);
            color: var(--text-light);
            border-color: var(--border-color);
        }

        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }

        /* Announcement styles */
        .announcement-item {
            transition: all 0.2s ease;
        }

        .announcement-item:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .bg-light-warning {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
        }

        .bg-light-read {
            background-color: rgba(108, 117, 125, 0.1);
            border-left: 3px solid #6c757d;
        }

        /* Button group styles */
        .btn-group .btn {
            padding: 0.25rem 0.5rem;
        }

        /* Recipients list styles */
        #recipientsList {
            max-height: 200px;
            overflow-y: auto;
        }

        .form-check-label {
            cursor: pointer;
        }

        /* Fix for content overlapping with sidebar */
        .admin-container {
            display: flex;
            width: 100%;
            min-height: 100vh;
        }

        .admin-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding-top: var(--navbar-height);
            min-height: 100vh;
            transition: margin-left var(--transition-speed);
        }

        @media (max-width: 992px) {
            .admin-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Include sidebar -->
        <?php include_once __DIR__ . '/../includes/sidebar.php'; ?>

        <div class="admin-content">
            <!-- Include navbar -->
            <?php include_once __DIR__ . '/../includes/navbar.php'; ?>

            <div class="content-wrapper">
                <!-- Content header removed -->

                <div class="content">
                    <div class="container-fluid">
                        <!-- Staff Management Header -->
                        <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
                            <h3 class="mb-3 mb-md-0">Staff Members</h3>
                            <div class="d-flex flex-wrap gap-2">
                                <button id="sendAnnouncementBtn" class="btn btn-primary">
                                    <i class="fas fa-bullhorn me-1"></i> Send Announcement
                                </button>
                                <button id="refreshStaffBtn" class="btn btn-outline-secondary">
                                    <i class="fas fa-sync-alt me-1"></i> Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Staff Search and Filter -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" id="staffSearch" class="form-control" placeholder="Search staff members...">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <select id="roleFilter" class="form-select">
                                            <option value="">All Roles</option>
                                            <option value="ADMIN">Admin</option>
                                            <option value="MODERATOR">Moderator</option>
                                            <option value="STAFF">Staff</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select id="statusFilter" class="form-select">
                                            <option value="">All Status</option>
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Staff Cards Container -->
                        <div id="staffCardsContainer" class="staff-card-container">
                            <!-- Loading state -->
                            <div class="text-center py-5 w-100">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading staff members...</span>
                                </div>
                                <p class="mt-3 text-muted">Loading staff members...</p>
                            </div>
                        </div>

                        <!-- Recent Announcements section moved to dashboard -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Edit Role Modal removed as requested -->

    <!-- Send Announcement Modal -->
    <div class="modal fade" id="announcementModal" tabindex="-1" aria-labelledby="announcementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="announcementModalLabel">Send Announcement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="announcementForm">
                        <div class="mb-3">
                            <label for="announcementTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="announcementTitle" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="announcementMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="announcementMessage" name="message" rows="5" required></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="isGlobal" name="is_global" checked>
                            <label class="form-check-label" for="isGlobal">Send to all staff members</label>
                        </div>
                        <div id="recipientsContainer" class="mb-3 d-none">
                            <label class="form-label">Select Recipients</label>
                            <div id="recipientsList" class="border p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                <!-- Recipients will be loaded here -->
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendAnnouncementSubmitBtn">Send Announcement</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="/js/admin-common.min.js?v=3"></script>
    <script src="/js/toggleSidebar-fix.js"></script>
    <script>
        $(document).ready(function() {

            let staffTable;


            function formatTimestamp(timestamp) {
                if (!timestamp) return 'Never';

                const date = new Date(timestamp);
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'America/New_York',
                    hour12: true
                };
                return `<span class="last-active">${date.toLocaleString('en-US', options)} ET</span>`;
            }


            function formatStaffInfo(staff) {
                const initials = staff.username.split(' ').map(n => n[0]).join('').toUpperCase();
                let avatarHtml = `<div class="staff-avatar">${initials}</div>`;

                if (staff.avatar_url) {
                    avatarHtml = `<div class="staff-avatar"><img src="${staff.avatar_url}" alt="${staff.username}"></div>`;
                }

                return `
                    <div class="staff-info">
                        ${avatarHtml}
                        <span class="staff-name">${staff.username}</span>
                    </div>
                `;
            }


            function formatRoleBadge(role) {
                let roleClass = 'role-trainee';

                const upperRole = (role || '').toUpperCase();

                if (upperRole === 'OWNER') roleClass = 'role-owner';
                else if (upperRole === 'DEVELOPER') roleClass = 'role-developer';
                else if (upperRole === 'SUPERVISOR') roleClass = 'role-supervisor';
                else if (upperRole === 'ADMIN') roleClass = 'role-admin';
                else if (upperRole === 'MODERATOR') roleClass = 'role-moderator';
                else if (upperRole === 'TRAINEE') roleClass = 'role-trainee';
                else if (upperRole === 'UNAUTHORIZED') roleClass = 'role-unauthorized';

                return `<span class="role-badge ${roleClass}">${role}</span>`;
            }


            function formatStatusBadge(isActive, role) {

                let roleClass = 'role-trainee';
                const upperRole = (role || '').toUpperCase();

                if (upperRole === 'OWNER') roleClass = 'role-owner';
                else if (upperRole === 'DEVELOPER') roleClass = 'role-developer';
                else if (upperRole === 'SUPERVISOR') roleClass = 'role-supervisor';
                else if (upperRole === 'ADMIN') roleClass = 'role-admin';
                else if (upperRole === 'MODERATOR') roleClass = 'role-moderator';
                else if (upperRole === 'TRAINEE') roleClass = 'role-trainee';
                else if (upperRole === 'UNAUTHORIZED') roleClass = 'role-unauthorized';


                let bgColor, textColor;

                switch(roleClass) {
                    case 'role-owner':
                        bgColor = 'rgba(155, 89, 182, 0.15)';
                        textColor = '#9b59b6';
                        break;
                    case 'role-developer':
                        bgColor = 'rgba(41, 128, 185, 0.15)';
                        textColor = '#2980b9';
                        break;
                    case 'role-supervisor':
                        bgColor = 'rgba(22, 160, 133, 0.15)';
                        textColor = '#16a085';
                        break;
                    case 'role-admin':
                        bgColor = 'rgba(231, 76, 60, 0.15)';
                        textColor = '#e74c3c';
                        break;
                    case 'role-moderator':
                        bgColor = 'rgba(243, 156, 18, 0.15)';
                        textColor = '#f39c12';
                        break;
                    case 'role-trainee':
                        bgColor = 'rgba(142, 68, 173, 0.15)';
                        textColor = '#8e44ad';
                        break;
                    case 'role-unauthorized':
                    default:
                        bgColor = 'rgba(149, 165, 166, 0.15)';
                        textColor = '#95a5a6';
                        break;
                }


                return isActive
                    ? `<span class="badge ms-2" style="background-color: ${bgColor}; color: ${textColor};">Active</span>`
                    : `<span class="badge ms-2" style="background-color: ${bgColor}; color: ${textColor};">Inactive</span>`;
            }

            function loadStaffMembers() {
                $('#staffCardsContainer').html(`
                    <div class="text-center py-5 w-100">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading staff members...</span>
                        </div>
                        <p class="mt-3 text-muted">Loading staff members...</p>
                    </div>
                `);

                fetch('/adminapi/staff/list.php/', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Discord-ID': window.AUTH_DATA.user_id || '',
                        'X-Discord-Username': window.AUTH_DATA.username || '',
                        'X-Discord-Role': window.AUTH_DATA.role || '',
                        'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.success && response.staff && response.staff.length > 0) {
                        allStaffMembers = response.staff;

                        $('#staffCardsContainer').empty();

                        response.staff.forEach(function(staff) {
                            const card = createStaffCard(staff);
                            $('#staffCardsContainer').append(card);
                        });





                        setupSearchAndFilter();
                    } else {
                        console.log('No staff members found or API returned error');

                        $('#staffCardsContainer').html(`
                            <div class="alert alert-warning text-center w-100">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                ${response.message || 'No staff members found.'}
                            </div>
                        `);
                    }
                })
                .catch(error => {
                    console.error('Error fetching staff members:', error);

                    $('#staffCardsContainer').html(`
                        <div class="alert alert-danger text-center w-100">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load staff members. Please try again.
                        </div>
                    `);
                });
            }

            /**
             * Create a staff card element
             * @param {Object} staff - Staff member data
             * @returns {string} HTML for the staff card
             */
            function createStaffCard(staff) {
                const avatarHtml = staff.avatar_url
                    ? `<img src="${staff.avatar_url}" alt="${staff.username}" class="img-fluid rounded-circle">`
                    : `<div class="d-flex align-items-center justify-content-center h-100 w-100 bg-primary text-white">
                        <span class="fs-3">${staff.username.charAt(0).toUpperCase()}</span>
                       </div>`;

                const roleBadge = formatRoleBadge(staff.role);

                const lastActive = formatTimestamp(staff.last_active);

                const isActive = staff.last_active && (new Date(staff.last_active) > new Date(Date.now() - 24 * 60 * 60 * 1000));
                const statusBadge = formatStatusBadge(isActive, staff.role);

                const discordId = `<span class="text-monospace">${staff.user_id}</span>`;

                return `
                    <div class="staff-card" data-role="${staff.role}" data-status="${isActive ? 'active' : 'inactive'}">
                        <div class="card-header">
                            <div class="staff-avatar">
                                ${avatarHtml}
                            </div>
                            <div class="staff-info">
                                <h5 class="staff-name">${staff.username}</h5>
                                <div>${roleBadge} ${statusBadge}</div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="text-muted">Last Active</div>
                                <div class="fw-bold">${lastActive}</div>
                            </div>
                            <div class="mb-3">
                                <div class="text-muted">Discord ID</div>
                                <div>${discordId}</div>
                            </div>
                            <div class="staff-actions">
                                <button class="btn btn-outline-primary view-activity" data-id="${staff.user_id}" title="View Activity">
                                    <i class="fas fa-chart-line me-1"></i> Activity
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            /**
             * Set up search and filter functionality
             */
            function setupSearchAndFilter() {
                $('#staffSearch').on('input', function() {
                    filterStaffCards();
                });

                $('#roleFilter').on('change', function() {
                    filterStaffCards();
                });

                $('#statusFilter').on('change', function() {
                    filterStaffCards();
                });
            }

            /**
             * Filter staff cards based on search and filter criteria
             */
            function filterStaffCards() {
                const searchTerm = $('#staffSearch').val().toLowerCase();
                const roleFilter = $('#roleFilter').val();
                const statusFilter = $('#statusFilter').val();

                $('.staff-card').each(function() {
                    const card = $(this);
                    const staffName = card.find('.staff-name').text().toLowerCase();
                    const staffRole = card.data('role');
                    const staffStatus = card.data('status');

                    const matchesSearch = !searchTerm || staffName.includes(searchTerm);
                    const matchesRole = !roleFilter || staffRole === roleFilter;
                    const matchesStatus = !statusFilter || staffStatus === statusFilter;

                    if (matchesSearch && matchesRole && matchesStatus) {
                        card.show();
                    } else {
                        card.hide();
                    }
                });

                if ($('.staff-card:visible').length === 0) {
                    if ($('#noResultsMessage').length === 0) {
                        $('#staffCardsContainer').append(`
                            <div id="noResultsMessage" class="alert alert-info text-center w-100 mt-3">
                                <i class="fas fa-info-circle me-2"></i>
                                No staff members match your search criteria.
                            </div>
                        `);
                    }
                } else {
                    $('#noResultsMessage').remove();
                }
            }

            let allStaffMembers = [];

            function loadAnnouncements() {
                $('#announcementsList').html(`
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `);

                fetch('/adminapi/announcements/list.php', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Discord-ID': window.AUTH_DATA.user_id || '',
                        'X-Discord-Username': window.AUTH_DATA.username || '',
                        'X-Discord-Role': window.AUTH_DATA.role || '',
                        'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.success && response.announcements && response.announcements.length > 0) {
                        let html = '';
                        response.announcements.forEach(announcement => {
                            const date = new Date(announcement.timestamp);
                            const formattedDate = date.toLocaleString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                                timeZone: 'America/New_York',
                                hour12: true
                            });

                            html += `
                                <div class="announcement-item mb-3 p-3 border rounded ${announcement.is_read ? 'bg-light-read' : 'bg-light-warning'}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">${announcement.title}</h5>
                                        <span class="badge bg-${announcement.is_global ? 'primary' : 'info'} rounded-pill">
                                            ${announcement.is_global ? 'Global' : 'Targeted'}
                                        </span>
                                    </div>
                                    <p class="mb-1" style="white-space: pre-line;">${announcement.message}</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Sent by ${announcement.sender_name} on ${formattedDate} ET</small>
                                        ${!announcement.is_read ? `
                                            <button class="btn btn-sm btn-outline-secondary mark-read" data-id="${announcement.id}">
                                                Mark as Read
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            `;
                        });
                        $('#announcementsList').html(html);
                    } else {
                        $('#announcementsList').html(`
                            <div class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                No announcements found.
                            </div>
                        `);
                    }
                })
                .catch(error => {
                    $('#announcementsList').html(`
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Failed to load announcements: ${error.message}
                        </div>
                    `);
                });
            }

            $(document).on('click', '.mark-read', function() {
                const announcementId = $(this).data('id');

                fetch('/adminapi/announcements/mark-read.php', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Discord-ID': window.AUTH_DATA.user_id || '',
                        'X-Discord-Username': window.AUTH_DATA.username || '',
                        'X-Discord-Role': window.AUTH_DATA.role || '',
                        'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        announcement_id: announcementId
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.success) {
                        loadAnnouncements();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to mark announcement as read',
                            confirmButtonColor: '#3085d6'
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to mark announcement as read: ' + error.message,
                        confirmButtonColor: '#3085d6'
                    });
                });
            });

            function populateRecipientsList(containerId) {
                const container = $(containerId);
                container.html(`
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `);

                if (allStaffMembers.length > 0) {
                    let html = '';
                    allStaffMembers.forEach(staff => {
                        html += `
                            <div class="form-check mb-2">
                                <input class="form-check-input recipient-checkbox" type="checkbox" value="${staff.user_id}" id="recipient-${staff.user_id}">
                                <label class="form-check-label" for="recipient-${staff.user_id}">
                                    ${staff.username} (${staff.role})
                                </label>
                            </div>
                        `;
                    });
                    container.html(html);
                } else {
                    container.html(`
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            No staff members found.
                        </div>
                    `);
                }
            }

            $(document).on('click', '.view-activity', function() {
                const staffId = $(this).data('id');
                const staffMember = allStaffMembers.find(s => s.user_id === staffId);

                if (!staffMember) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Staff member not found',
                        confirmButtonColor: '#3085d6'
                    });
                    return;
                }

                window.location.href = `/dashboard/audit-logs?staff=${staffId}`;
            });



            $('#sendAnnouncementBtn').click(function() {
                $('#announcementForm')[0].reset();
                $('#isGlobal').prop('checked', true);
                $('#recipientsContainer').addClass('d-none');

                populateRecipientsList('#recipientsList');

                const announcementModal = new bootstrap.Modal(document.getElementById('announcementModal'));
                announcementModal.show();
            });



            $('#isGlobal').change(function() {
                if ($(this).is(':checked')) {
                    $('#recipientsContainer').addClass('d-none');
                } else {
                    $('#recipientsContainer').removeClass('d-none');
                    populateRecipientsList('#recipientsList');
                }
            });

            $('#sendAnnouncementSubmitBtn').click(function() {
                const title = $('#announcementTitle').val();
                const message = $('#announcementMessage').val();
                const isGlobal = $('#isGlobal').is(':checked');

                if (!title || !message) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Please fill in all required fields',
                        confirmButtonColor: '#3085d6'
                    });
                    return;
                }

                let recipients = [];
                if (!isGlobal) {
                    $('#recipientsList .recipient-checkbox:checked').each(function() {
                        recipients.push($(this).val());
                    });

                    if (recipients.length === 0) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Please select at least one recipient',
                            confirmButtonColor: '#3085d6'
                        });
                        return;
                    }
                }

                $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> Sending...');

                fetch('/adminapi/announcements/send.php', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Discord-ID': window.AUTH_DATA.user_id || '',
                        'X-Discord-Username': window.AUTH_DATA.username || '',
                        'X-Discord-Role': window.AUTH_DATA.role || '',
                        'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        title: title,
                        message: message,
                        is_global: isGlobal,
                        recipients: recipients
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.success) {
                        bootstrap.Modal.getInstance(document.getElementById('announcementModal')).hide();

                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message,
                            confirmButtonColor: '#3085d6'
                        }).then(() => {
                            loadAnnouncements();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to send announcement',
                            confirmButtonColor: '#3085d6'
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to send announcement: ' + error.message,
                        confirmButtonColor: '#3085d6'
                    });
                })
                .finally(() => {
                    $('#sendAnnouncementSubmitBtn').prop('disabled', false).html('Send Announcement');
                });
            });




            $('#refreshStaffBtn').click(function() {
                loadStaffMembers();
            });

            $('#refreshAnnouncementsBtn').click(function() {
                loadAnnouncements();
            });

            loadStaffMembers();
            loadAnnouncements();
        });
    </script>
</body>
</html>
