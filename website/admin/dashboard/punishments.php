<?php
/**
 * Punishments Page
 * Allows staff to view and manage all punishments
 */


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 


require_auth(ROLE_TRAINEE); // TRAINEE_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Punishments | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <script>
        window.AUTH_DATA = {
            user_id: "<?php echo $_SESSION['discord_user_id'] ?? ''; ?>",
            username: "<?php echo $_SESSION['discord_username'] ?? ''; ?>",
            role: "<?php echo $_SESSION['discord_user_role'] ?? ''; ?>",
            avatar: "<?php echo $_SESSION['discord_avatar'] ?? ''; ?>"
        };
    </script>
    <link rel="stylesheet" href="/css/admin-common.css">
    <link rel="stylesheet" href="/css/mobile.css">
    <link rel="stylesheet" href="/css/mobile-fixes.css">
    <link rel="stylesheet" href="/css/punishment-details-dark-mode.css">
    <link rel="stylesheet" href="/css/punishment-details-fix.css?v=1">
    <link rel="stylesheet" href="/css/punishment-details-white-fix.css?v=1">
    <link rel="stylesheet" href="/css/punishment.css?v=1">
    <link rel="stylesheet" href="/css/punishment-dark-mode-fix.css?v=1">
    <style>
        .punishment-card {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .punishment-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .punishment-type-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .punishment-reason {
            background-color: var(--bg-dark);
            border-radius: var(--border-radius-sm);
        }

        .view-toggle-btn {
            border-radius: 50rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .view-toggle-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .empty-state h4 {
            margin-bottom: 1rem;
        }

        .empty-state p {
            color: var(--text-muted);
            max-width: 500px;
            margin: 0 auto;
        }

        .punishment-details .card {
            height: 100%;
        }

        /* Fix for white backgrounds in dark mode */
        body.dark-mode .card {
            background-color: var(--bg-dark);
            color: var(--text-light);
        }

        body.dark-mode .card-header.bg-light {
            background-color: var(--bg-darker) !important;
            color: var(--text-light);
        }

        body.dark-mode .card-body {
            background-color: var(--bg-dark);
            color: var(--text-light);
        }

        body.dark-mode .punishment-reason {
            background-color: var(--bg-darker) !important;
            color: var(--text-light);
        }

        body.dark-mode .modal-content {
            background-color: var(--bg-dark);
            color: var(--text-light);
        }

        body.dark-mode .modal-header:not(.bg-primary),
        body.dark-mode .modal-footer {
            background-color: var(--bg-darker);
            color: var(--text-light);
            border-color: var(--border-color);
        }

        body.dark-mode .form-control,
        body.dark-mode .form-select {
            background-color: var(--bg-darker);
            color: var(--text-light);
            border-color: var(--border-color);
        }

        /* Enhanced dark dropdown styling for consistency */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }

        body.dark-mode .form-text {
            color: var(--text-muted);
        }

        body.dark-mode .dropdown-menu {
            background-color: var(--bg-darker);
            color: var(--text-light);
            border-color: var(--border-color);
        }

        body.dark-mode .dropdown-item {
            color: var(--text-light);
        }

        body.dark-mode .dropdown-item:hover {
            background-color: var(--bg-dark);
        }

        /* Fix for the sidebar overlap */
        .admin-container {
            display: flex;
            min-height: 100vh;
            width: 100%;
            position: relative;
            overflow-x: hidden;
        }

        /* Removed custom sidebar positioning that was causing the issue */

        .admin-content {
            flex: 1;
            margin-left: 250px;
            width: calc(100% - 250px);
            min-height: 100vh;
        }

        /* Fix for dropdown menus appearing behind other elements */
        .dropdown-menu {
            z-index: 1050 !important;
        }

        /* Fix for modals */
        .modal {
            z-index: 1060 !important;
        }

        /* Fix modal backdrop */
        .modal-backdrop {
            z-index: 1050 !important;
        }

        /* Fix modal content */
        .modal-content {
            z-index: 1061 !important;
        }

        /* Fix for profile dropdown showing through the New Punishment button */
        .navbar-actions .dropdown-menu {
            z-index: 1070 !important;
        }

        /* Ensure buttons stay on top of dropdowns */
        .btn-primary, .btn-secondary, .btn-danger, .btn-success, .btn-warning {
            position: relative;
            z-index: 1040;
        }

        /* Fix for punishment templates dropdown */
        .punishment-reason-group {
            position: relative;
        }

        .templates-dropdown-wrapper {
            position: relative;
        }

        .dropdown-menu.punishment-templates {
            max-height: 200px;
            overflow-y: auto;
            position: absolute;
            z-index: 1070;
            bottom: 100%;
            top: auto;
            margin-bottom: 5px;
        }

        /* Specific fix for the New Punishment button */
        .new-punishment-btn {
            position: relative;
            z-index: 1045 !important;
        }

        /* Fix for the profile dropdown overlapping with the New Punishment button */
        .navbar-actions .dropdown {
            position: relative;
        }

        .navbar-actions .dropdown-menu {
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 0.5rem;
            width: 200px;
            box-shadow: var(--shadow-lg);
            z-index: 1090 !important; /* Higher than both buttons and other dropdowns */
        }

        /* Fix for modal buttons */
        .modal-footer {
            position: relative;
            z-index: 1065 !important;
        }

        .modal-footer .btn {
            position: relative;
            z-index: 1066 !important;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Include sidebar -->
        <?php include_once __DIR__ . '/../includes/sidebar.php'; ?>

        <div class="admin-content">
            <!-- Include navbar -->
            <?php include_once __DIR__ . '/../includes/navbar.php'; ?>

            <div class="content-wrapper">
                <div class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1 class="m-0">Punishments</h1>
                            </div>
                            <div class="col-sm-6">
                                <!-- Breadcrumb removed -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content">
                    <div class="container-fluid">
                        <!-- Action Buttons Section (Empty) -->
                        <div class="d-flex justify-content-end mb-3">
                        </div>

                        <!-- Filters -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-filter me-2"></i>Filters
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label for="filterType" class="form-label">Punishment Type</label>
                                        <select id="filterType" class="form-select">
                                            <option value="all" selected>All Types</option>
                                            <option value="ban">Bans</option>
                                            <option value="mute">Mutes</option>
                                            <option value="warning">Warnings</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filterStatus" class="form-label">Status</label>
                                        <select id="filterStatus" class="form-select">
                                            <option value="all" selected>All Status</option>
                                            <option value="active">Active</option>
                                            <option value="expired">Expired</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filterPlayer" class="form-label">Player</label>
                                        <input type="text" id="filterPlayer" class="form-control" placeholder="Enter player name">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filterStaff" class="form-label">Staff Member</label>
                                        <input type="text" id="filterStaff" class="form-control" placeholder="Enter staff name">
                                    </div>
                                    <div class="col-12 text-end">
                                        <button id="resetFilters" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-undo me-2"></i>Reset
                                        </button>
                                        <button id="applyFilters" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>Apply Filters
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Punishments List -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-gavel me-2"></i>Punishment History
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="punishmentsContent">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading punishments...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Punishment Modal -->
            <div class="modal fade" id="addPunishmentModal" tabindex="-1" aria-labelledby="addPunishmentModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content punishment-modal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addPunishmentModalLabel">Add Punishment</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="punishmentForm">
                                <input type="hidden" id="punishmentType" name="type" value="ban">
                                <input type="hidden" id="punishmentDurationValue" name="duration" value="permanent">
                                <input type="hidden" id="punishmentDurationUnit" name="duration_unit" value="permanent">
                                <input type="hidden" id="punishmentPublic" name="public" value="1">

                                <div class="mb-3">
                                    <label for="playerName" class="form-label">Player Name</label>
                                    <input type="text" class="form-control" id="playerName" required>
                                    <div class="form-text">Enter the exact player name</div>
                                </div>

                                <div class="mb-4">
                                    <label for="punishmentTypeSelect" class="form-label">Violation Type</label>
                                    <select class="form-select" id="punishmentTypeSelect">
                                        <option value="">Select a violation type...</option>
                                    </select>
                                    <div class="form-text">Select a predefined violation type</div>
                                </div>

                                <!-- Offense progression information will be displayed here -->
                                <div id="offenseProgressionInfo" class="mb-4"></div>

                                <div class="mb-4">
                                    <label for="punishmentEvidence" class="form-label">Evidence</label>
                                    <textarea class="form-control" id="punishmentEvidence" name="evidence" rows="3" placeholder="Provide evidence of the violation (screenshots, videos, etc.)"></textarea>
                                    <div class="form-text">Include links to screenshots, videos, report ID, or other evidence</div>
                                </div>

                                <div class="mb-4">
                                    <label for="punishmentNotes" class="form-label">Staff Notes</label>
                                    <textarea class="form-control" id="punishmentNotes" name="notes" rows="3" placeholder="Add any additional notes for staff reference (not visible to the player)"></textarea>
                                    <div class="form-text">These notes will only be visible to staff members</div>
                                </div>

                                <div class="confirmation-section">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="punishmentConfirm" name="confirm" required>
                                        <label class="form-check-label" for="punishmentConfirm">
                                            I confirm this punishment is appropriate and follows server guidelines
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="submitPunishment">Apply Punishment</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Punishment Details Modal -->
            <div class="modal fade" id="punishmentDetailsModal" tabindex="-1" aria-labelledby="punishmentDetailsModalLabel" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content punishment-details-modal">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="punishmentDetailsModalLabel">Punishment Details</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" id="closeDetailsModalBtn"></button>
                        </div>
                        <div class="modal-body" id="punishmentDetailsContent">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading punishment details...</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeDetailsModalFooterBtn">Close</button>
                            <button type="button" class="btn btn-danger" id="removePunishmentBtn" style="display: none;">
                                <i class="fas fa-times me-2" aria-hidden="true"></i>Remove Punishment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/js/admin-common.min.js?v=3"></script>
    <script src="/js/toggleSidebar-fix.js"></script>
    <script src="/js/punishment-types.min.js?v=1"></script>
    <script src="/js/punishments.min.js?v=1"></script>
    <script>

        document.addEventListener('DOMContentLoaded', function() {

            const addPunishmentModal = document.getElementById('addPunishmentModal');
            if (addPunishmentModal) {

                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Escape' && document.body.classList.contains('modal-open')) {
                        try {
                            const modal = bootstrap.Modal.getInstance(addPunishmentModal);
                            if (modal) {
                                modal.hide();
                            }


                            const backdrop = document.querySelector('.modal-backdrop');
                            if (backdrop) {
                                backdrop.remove();
                            }
                            document.body.classList.remove('modal-open');
                            document.body.style.overflow = '';
                            document.body.style.paddingRight = '';
                        } catch (error) {
                            console.error('Error closing modal with escape key:', error);
                        }
                    }
                });


                document.addEventListener('click', function(event) {
                    if (document.body.classList.contains('modal-open') &&
                        !addPunishmentModal.contains(event.target) &&
                        !event.target.closest('.modal-content')) {
                        try {
                            const modal = bootstrap.Modal.getInstance(addPunishmentModal);
                            if (modal) {
                                modal.hide();
                            }
                        } catch (error) {
                            console.error('Error closing modal by clicking outside:', error);
                        }
                    }
                });
            }


            const submitBtn = document.getElementById('submitPunishment');
            if (submitBtn) {
                submitBtn.addEventListener('click', function() {
                    issuePunishmentWithType();
                });
            }


            const punishmentDetailsModal = document.getElementById('punishmentDetailsModal');
            if (punishmentDetailsModal) {

                let previouslyFocusedElement = null;


                punishmentDetailsModal.addEventListener('show.bs.modal', function() {

                    previouslyFocusedElement = document.activeElement;


                    punishmentDetailsModal.removeAttribute('aria-hidden');
                });


                punishmentDetailsModal.addEventListener('shown.bs.modal', function() {

                    punishmentDetailsModal.removeAttribute('aria-hidden');


                    const firstFocusableElement = punishmentDetailsModal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                    if (firstFocusableElement) {
                        firstFocusableElement.focus();
                    }
                });


                const closeBtn = document.getElementById('closeDetailsModalBtn');
                const closeFooterBtn = document.getElementById('closeDetailsModalFooterBtn');
                const removePunishmentBtn = document.getElementById('removePunishmentBtn');


                const handleModalClose = function() {

                    if (document.activeElement) {
                        document.activeElement.blur();
                    }
                };

                if (closeBtn) {
                    closeBtn.addEventListener('click', handleModalClose);
                }

                if (closeFooterBtn) {
                    closeFooterBtn.addEventListener('click', handleModalClose);
                }

                if (removePunishmentBtn) {
                    removePunishmentBtn.addEventListener('click', handleModalClose);
                }


                punishmentDetailsModal.addEventListener('hidden.bs.modal', function() {

                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }


                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';


                    if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {

                        setTimeout(() => {
                            previouslyFocusedElement.focus();
                        }, 10);
                    }
                });


                punishmentDetailsModal.removeAttribute('aria-hidden');
            }
        });
    </script>
</body>
</html>
