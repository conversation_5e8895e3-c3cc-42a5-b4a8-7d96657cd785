<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 


require_auth(ROLE_TRAINEE); // Or ROLE_MODERATOR, ROLE_ADMIN
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Help Center | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>"
};
</script>
    <link href="/css/admin-common.css" rel="stylesheet">
    <link href="/css/modern-theme.css" rel="stylesheet">
    <link href="/css/mobile.css" rel="stylesheet">
    <style>
        /* Hero section */
        .help-hero {
            background: linear-gradient(135deg, var(--accent-color) 0%, #2563eb 100%);
            border-radius: var(--border-radius-lg);
            padding: 3rem 2rem;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .help-hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5OC41IDEwMEMyOTguNSAxNTUuMjI4IDI1NC4yMjggMTk5LjUgMTk5IDE5OS41QzE0My43NzIgMTk5LjUgOTkuNSAxNTUuMjI4IDk5LjUgMTAwQzk5LjUgNDQuNzcxNSAxNDMuNzcyIDAuNSAxOTkgMC41QzI1NC4yMjggMC41IDI5OC41IDQ0Ljc3MTUgMjk4LjUgMTAwWiIgc3Ryb2tlPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiLz48cGF0aCBkPSJNMTk5IDI5OC41QzI1NC4yMjggMjk4LjUgMjk4LjUgMjU0LjIyOCAyOTguNSAxOTlDMjk4LjUgMTQzLjc3MiAyNTQuMjI4IDk5LjUgMTk5IDk5LjVDMTQzLjc3MiA5OS41IDk5LjUgMTQzLjc3MiA5OS41IDE5OUM5OS41IDI1NC4yMjggMTQzLjc3MiAyOTguNSAxOTkgMjk4LjVaIiBzdHJva2U9InJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSIvPjxwYXRoIGQ9Ik0xMDAgMTk5LjVDMTU1LjIyOCAxOTkuNSAxOTkuNSAxNTUuMjI4IDE5OS41IDEwMEMxOTkuNSA0NC43NzE1IDE1NS4yMjggMC41IDEwMCAwLjVDNDQuNzcxNSAwLjUgMC41IDQ0Ljc3MTUgMC41IDEwMEMwLjUgMTU1LjIyOCA0NC43NzE1IDE5OS41IDEwMCAxOTkuNVoiIHN0cm9rZT0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIi8+PHBhdGggZD0iTTEwMCAyOTguNUMxNTUuMjI4IDI5OC41IDE5OS41IDI1NC4yMjggMTk5LjUgMTk5QzE5OS41IDE0My43NzIgMTU1LjIyOCA5OS41IDEwMCA5OS41QzQ0Ljc3MTUgOTkuNSAwLjUgMTQzLjc3MiAwLjUgMTk5QzAuNSAyNTQuMjI4IDQ0Ljc3MTUgMjk4LjUgMTAwIDI5OC41WiIgc3Ryb2tlPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiLz48L3N2Zz4=') no-repeat center center;
            opacity: 0.2;
            z-index: 0;
        }

        .help-hero-content {
            position: relative;
            z-index: 1;
        }

        .help-hero h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .help-hero p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.125rem;
            max-width: 600px;
            margin-bottom: 1.5rem;
        }

        /* Search bar */
        .search-help {
            position: relative;
            margin-bottom: 2.5rem;
            max-width: 700px;
        }

        .search-help input {
            padding: 1.25rem 1.25rem 1.25rem 3.5rem;
            height: 60px;
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            color: var(--text-light);
            font-size: 1.125rem;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .search-help input:focus {
            box-shadow: 0 0 0 4px rgba(58, 134, 255, 0.15);
            border-color: var(--accent-color);
        }

        .search-help i {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--accent-color);
            font-size: 1.5rem;
        }

        /* Help sections */
        .help-section {
            margin-bottom: 4rem;
        }

        .help-section-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .help-section-title i {
            color: var(--accent-color);
        }

        .help-section-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 800px;
        }

        /* Category cards */
        .help-category {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 2rem 1.5rem;
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            height: 100%;
            border: 1px solid transparent;
        }

        .help-category:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            border-color: rgba(58, 134, 255, 0.2);
        }

        .help-category-icon {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(58, 134, 255, 0.2) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.25rem;
            transition: all 0.3s ease;
        }

        .help-category:hover .help-category-icon {
            transform: scale(1.1);
            background: linear-gradient(135deg, rgba(58, 134, 255, 0.2) 0%, rgba(58, 134, 255, 0.3) 100%);
        }

        .help-category-icon i {
            font-size: 1.75rem;
            color: var(--accent-color);
        }

        .help-category-title {
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
            color: var(--text-light);
        }

        .help-category-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Guide cards */
        .help-card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            height: 100%;
        }

        .help-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            border-color: rgba(58, 134, 255, 0.2);
        }

        .help-card-title {
            font-size: 1.35rem;
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            color: var(--text-light);
        }

        .help-card-title i {
            margin-right: 0.75rem;
            color: var(--accent-color);
            font-size: 1.75rem;
        }

        .help-card-content {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.6;
        }

        .help-card-content p {
            margin-bottom: 1rem;
        }

        .help-card-content ol,
        .help-card-content ul {
            padding-left: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .help-card-content li {
            margin-bottom: 0.5rem;
        }

        .help-card-content img {
            max-width: 100%;
            border-radius: var(--border-radius-md);
            margin: 1rem 0;
            border: 1px solid var(--border-color);
        }

        .help-card .btn {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: var(--border-radius-md);
            transition: all 0.3s ease;
        }

        .help-card .btn:hover {
            transform: translateY(-2px);
        }

        /* FAQ accordion */
        .faq-accordion {
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
        }

        .faq-item {
            border-bottom: 1px solid var(--border-color);
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-question {
            font-weight: 600;
            padding: 1.25rem;
            color: var(--text-light);
            background-color: var(--bg-card);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .faq-question:hover {
            background-color: rgba(58, 134, 255, 0.05);
        }

        .faq-question i {
            color: var(--accent-color);
            transition: transform 0.3s ease;
        }

        .faq-question.active i {
            transform: rotate(180deg);
        }

        .faq-answer {
            color: var(--text-secondary);
            padding: 0 1.25rem 1.25rem 1.25rem;
            line-height: 1.6;
            display: none;
        }

        .faq-answer.show {
            display: block;
        }

        /* Contact options */
        .contact-option {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .contact-option:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            border-color: rgba(58, 134, 255, 0.2);
        }

        .contact-icon {
            width: 60px;
            height: 60px;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(58, 134, 255, 0.2) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1.5rem;
            flex-shrink: 0;
        }

        .contact-icon i {
            font-size: 1.5rem;
            color: var(--accent-color);
        }

        .contact-details {
            flex-grow: 1;
        }

        .contact-title {
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--text-light);
        }

        .contact-description {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 0;
            line-height: 1.5;
        }

        /* Video tutorials */
        .video-tutorial {
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            height: 100%;
        }

        .video-tutorial:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }

        .video-thumbnail {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background-color: #000;
            overflow: hidden;
        }

        .video-thumbnail img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .video-tutorial:hover .video-thumbnail img {
            transform: scale(1.05);
            opacity: 0.8;
        }

        .video-play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background-color: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .video-play-button i {
            color: white;
            font-size: 1.5rem;
            margin-left: 4px;
        }

        .video-tutorial:hover .video-play-button {
            transform: translate(-50%, -50%) scale(1.1);
            background-color: var(--accent-hover);
        }

        .video-info {
            padding: 1.25rem;
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-top: none;
        }

        .video-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--text-light);
        }

        .video-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        /* Step-by-step guide */
        .step-guide {
            counter-reset: step-counter;
        }

        .step-item {
            display: flex;
            margin-bottom: 2rem;
            position: relative;
        }

        .step-number {
            counter-increment: step-counter;
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--accent-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 1.25rem;
            position: relative;
            z-index: 1;
        }

        .step-number::before {
            content: counter(step-counter);
        }

        .step-content {
            flex-grow: 1;
            padding-bottom: 1rem;
        }

        .step-title {
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 0.75rem;
            color: var(--text-light);
        }

        .step-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .step-image {
            border-radius: var(--border-radius-md);
            overflow: hidden;
            border: 1px solid var(--border-color);
            margin-top: 1rem;
        }

        .step-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        /* Timeline connector */
        .step-item:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 40px;
            left: 20px;
            width: 2px;
            height: calc(100% - 20px);
            background-color: rgba(58, 134, 255, 0.2);
            z-index: 0;
        }

        /* Mobile responsiveness */
        @media (max-width: 991.98px) {
            .help-hero {
                padding: 2rem 1.5rem;
            }

            .help-hero h1 {
                font-size: 2rem;
            }

            .help-section-title {
                font-size: 1.5rem;
            }

            .help-card {
                padding: 1.5rem;
            }
        }

        @media (max-width: 767.98px) {
            .help-hero h1 {
                font-size: 1.75rem;
            }

            .help-hero p {
                font-size: 1rem;
            }

            .search-help input {
                height: 50px;
                font-size: 1rem;
                padding: 1rem 1rem 1rem 3rem;
            }

            .help-category {
                padding: 1.5rem 1rem;
            }

            .help-category-icon {
                width: 60px;
                height: 60px;
            }

            .help-card-title {
                font-size: 1.25rem;
            }

            .contact-option {
                padding: 1.25rem;
            }

            .contact-icon {
                width: 50px;
                height: 50px;
                margin-right: 1rem;
            }

            .contact-title {
                font-size: 1.1rem;
            }
        }

        /* Dark mode adjustments */
        body.dark-mode .help-hero {
            background: linear-gradient(135deg, #3a86ff 0%, #1e40af 100%);
        }

        body.dark-mode .faq-question:hover {
            background-color: rgba(58, 134, 255, 0.1);
        }

        body.dark-mode .video-info {
            background-color: var(--secondary-color);
            border-color: var(--border-color-dark);
        }

        body.dark-mode .step-image {
            border-color: var(--border-color-dark);
        }
    </style>
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Hero Section -->
            <div class="help-hero">
                <div class="help-hero-content">
                    <h1>Welcome to the Help Center</h1>
                    <p>Find comprehensive guides, tutorials, and answers to help you navigate the MassacreMC Admin Panel effectively.</p>

                    <!-- Search Bar -->
                    <div class="search-help">
                        <i class="fas fa-search"></i>
                        <input type="text" class="form-control" placeholder="Search for guides, tutorials, or questions...">
                    </div>
                </div>
            </div>

            <!-- Help Categories -->
            <div class="help-section">
                <div class="help-section-title">
                    <i class="fas fa-th-large"></i> Browse Help Categories
                </div>
                <div class="help-section-description">
                    Select a category to find specific guides and information related to your role.
                </div>
                <div class="row g-4">
                    <div class="col-md-4 col-sm-6">
                        <a href="#getting-started" class="text-decoration-none">
                            <div class="help-category">
                                <div class="help-category-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="help-category-title">Getting Started</div>
                                <div class="help-category-description">Essential information for new staff members and basic admin panel navigation</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <a href="#managing-reports" class="text-decoration-none">
                            <div class="help-category">
                                <div class="help-category-icon">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <div class="help-category-title">Managing Reports</div>
                                <div class="help-category-description">Learn how to efficiently review and process player reports</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <a href="#handling-appeals" class="text-decoration-none">
                            <div class="help-category">
                                <div class="help-category-icon">
                                    <i class="fas fa-gavel"></i>
                                </div>
                                <div class="help-category-title">Handling Appeals</div>
                                <div class="help-category-description">Guidelines for reviewing and responding to punishment appeals</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <a href="#player-management" class="text-decoration-none">
                            <div class="help-category">
                                <div class="help-category-icon">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="help-category-title">Player Management</div>
                                <div class="help-category-description">How to search, view, and manage player information</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <a href="#punishment-system" class="text-decoration-none">
                            <div class="help-category">
                                <div class="help-category-icon">
                                    <i class="fas fa-ban"></i>
                                </div>
                                <div class="help-category-title">Punishment System</div>
                                <div class="help-category-description">Understanding and using the punishment workflow</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <a href="#my-profile" class="text-decoration-none">
                            <div class="help-category">
                                <div class="help-category-icon">
                                    <i class="fas fa-user-cog"></i>
                                </div>
                                <div class="help-category-title">My Profile</div>
                                <div class="help-category-description">How to manage your staff account and preferences</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Getting Started Section -->
            <div class="help-section" id="getting-started">
                <div class="help-section-title">
                    <i class="fas fa-rocket"></i> Getting Started
                </div>
                <div class="help-section-description">
                    Essential information to help you get started with the admin panel.
                </div>

                <!-- Step-by-step Guide -->
                <div class="help-card">
                    <div class="help-card-title">
                        <i class="fas fa-map-signs"></i> Admin Panel Orientation
                    </div>
                    <div class="help-card-content">
                        <p>Welcome to the MassacreMC Admin Panel! This guide will help you understand the basic layout and navigation of the panel.</p>

                        <div class="step-guide">
                            <div class="step-item">
                                <div class="step-number"></div>
                                <div class="step-content">
                                    <div class="step-title">Dashboard Overview</div>
                                    <div class="step-description">
                                        The dashboard is your home page and provides a quick overview of important statistics and recent activity. You'll see pending reports, recent punishments, and server status at a glance.
                                    </div>
                                </div>
                            </div>

                            <div class="step-item">
                                <div class="step-number"></div>
                                <div class="step-content">
                                    <div class="step-title">Sidebar Navigation</div>
                                    <div class="step-description">
                                        The sidebar on the left contains links to all major sections of the admin panel. You can access Reports, Appeals, Player Search, Punishments, and more from here.
                                    </div>
                                </div>
                            </div>

                            <div class="step-item">
                                <div class="step-number"></div>
                                <div class="step-content">
                                    <div class="step-title">User Profile</div>
                                    <div class="step-description">
                                        Your profile information is displayed in the top navbar. You can see your username, role, and access the settings menu from here.
                                    </div>
                                </div>
                            </div>

                            <div class="step-item">
                                <div class="step-number"></div>
                                <div class="step-content">
                                    <div class="step-title">Dark Mode Toggle</div>
                                    <div class="step-description">
                                        You can switch between light and dark mode using the toggle in the navbar. Choose the theme that works best for you!
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Managing Reports Section -->
            <div class="help-section" id="managing-reports">
                <div class="help-section-title">
                    <i class="fas fa-flag"></i> Managing Reports
                </div>
                <div class="help-section-description">
                    Learn how to efficiently process player reports in the admin panel.
                </div>

                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-clipboard-check"></i> Report Processing Guide
                            </div>
                            <div class="help-card-content">
                                <p>Follow these steps to properly review and process player reports:</p>
                                <ol>
                                    <li><strong>Access the Reports Page</strong> - Navigate to the Reports section in the sidebar</li>
                                    <li><strong>Review Report Details</strong> - Carefully read the report description and examine any evidence provided</li>
                                    <li><strong>Check Player History</strong> - Review the reported player's history for previous violations</li>
                                    <li><strong>Make a Decision</strong> - Based on server rules and evidence, decide whether to accept or reject the report</li>
                                    <li><strong>Provide Feedback</strong> - Always include clear reasoning for your decision</li>
                                    <li><strong>Take Action</strong> - If accepting the report, issue appropriate punishment</li>
                                </ol>
                                <p>Remember that consistency and fairness are key when processing reports.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-exclamation-triangle"></i> Common Report Types
                            </div>
                            <div class="help-card-content">
                                <p>Here are the most common types of reports you'll encounter and how to handle them:</p>

                                <h6>Chat Violations</h6>
                                <p>Reports about inappropriate chat messages, harassment, or spam. Always check chat logs for context before making a decision.</p>

                                <h6>Hacking/Cheating</h6>
                                <p>Reports about players using unfair advantages. Look for clear evidence in screenshots or video recordings. When in doubt, consult with senior staff.</p>

                                <h6>Griefing</h6>
                                <p>Reports about destruction of property. Verify ownership claims and check block logs when available.</p>

                                <h6>Scamming</h6>
                                <p>Reports about trade scams or deception. These can be complex and may require additional investigation.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Handling Appeals Section -->
            <div class="help-section" id="handling-appeals">
                <div class="help-section-title">
                    <i class="fas fa-gavel"></i> Handling Appeals
                </div>
                <div class="help-section-description">
                    Guidelines for reviewing and responding to punishment appeals effectively.
                </div>

                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-balance-scale"></i> Appeal Review Process
                            </div>
                            <div class="help-card-content">
                                <p>Follow this process when reviewing player punishment appeals:</p>
                                <ol>
                                    <li><strong>Review Original Punishment</strong> - Examine the details of the original punishment, including reason, evidence, and staff notes</li>
                                    <li><strong>Read Appeal Carefully</strong> - Consider the player's explanation and any new evidence they've provided</li>
                                    <li><strong>Check Player History</strong> - Review the player's full punishment history to establish patterns of behavior</li>
                                    <li><strong>Consult Guidelines</strong> - Refer to the server's appeal guidelines for specific violation types</li>
                                    <li><strong>Make a Decision</strong> - Decide whether to accept, modify, or reject the appeal based on all available information</li>
                                    <li><strong>Provide Clear Reasoning</strong> - Explain your decision thoroughly, referencing specific points from their appeal</li>
                                </ol>
                                <p>Remember that appeals require careful consideration of both the rules and the specific circumstances of each case.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-lightbulb"></i> Appeal Decision Guidelines
                            </div>
                            <div class="help-card-content">
                                <p>Consider these factors when making appeal decisions:</p>

                                <h6>Admission of Wrongdoing</h6>
                                <p>Players who acknowledge their mistake and show genuine remorse may deserve more leniency, especially for first offenses.</p>

                                <h6>New Evidence</h6>
                                <p>If the player provides new evidence that contradicts the original punishment basis, carefully verify its authenticity.</p>

                                <h6>Previous Behavior</h6>
                                <p>Consider the player's history - a player with a clean record before the incident may deserve more consideration than a repeat offender.</p>

                                <h6>Time Served</h6>
                                <p>For longer punishments, consider if the player has already served a significant portion and shown understanding of their violation.</p>

                                <h6>Consistency</h6>
                                <p>Ensure your decisions align with how similar appeals have been handled in the past to maintain fairness.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Player Management Section -->
            <div class="help-section" id="player-management">
                <div class="help-section-title">
                    <i class="fas fa-user-shield"></i> Player Management
                </div>
                <div class="help-section-description">
                    Learn how to effectively search for, view, and manage player information.
                </div>

                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-search"></i> Finding Player Information
                            </div>
                            <div class="help-card-content">
                                <p>The Player Search feature allows you to quickly find information about any player:</p>
                                <ol>
                                    <li><strong>Access Player Search</strong> - Click on "Player Search" in the sidebar navigation</li>
                                    <li><strong>Enter Player Details</strong> - Type the player's username, UUID, or IP address in the search box</li>
                                    <li><strong>Review Results</strong> - Click on a player's name in the search results to view their full profile</li>
                                    <li><strong>Examine Profile Tabs</strong> - Navigate between tabs to see different aspects of the player's information:
                                        <ul>
                                            <li>Overview - Basic player information and stats</li>
                                            <li>Punishments - History of warnings, mutes, kicks, and bans</li>
                                            <li>Reports - Reports filed by or against this player</li>
                                            <li>Notes - Staff notes about the player</li>
                                            <li>Alts - Potential alternate accounts</li>
                                        </ul>
                                    </li>
                                </ol>
                                <p>You can use advanced search filters to narrow down results by date, server, or other criteria.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-sticky-note"></i> Managing Player Notes
                            </div>
                            <div class="help-card-content">
                                <p>Staff notes are an important tool for tracking player behavior and sharing information with other staff members:</p>

                                <h6>Adding Notes</h6>
                                <p>To add a note, navigate to a player's profile, select the Notes tab, and click "Add Note." Include relevant details and select an appropriate category.</p>

                                <h6>Effective Note Writing</h6>
                                <p>Write clear, factual notes that other staff can easily understand. Include dates, specific behaviors observed, and any actions taken.</p>

                                <h6>Note Categories</h6>
                                <p>Use the appropriate category for your note (Warning, Behavior, Information, etc.) to help other staff quickly understand its nature.</p>

                                <h6>Editing and Deleting</h6>
                                <p>You can edit your own notes if needed. Note that all changes are tracked in the audit log for accountability.</p>

                                <h6>Confidentiality</h6>
                                <p>Remember that notes are visible to all staff members. Maintain professionalism and only include relevant information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Punishment System Section -->
            <div class="help-section" id="punishment-system">
                <div class="help-section-title">
                    <i class="fas fa-ban"></i> Punishment System
                </div>
                <div class="help-section-description">
                    Learn how to effectively use the punishment system to maintain server rules and player conduct.
                </div>

                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-exclamation-circle"></i> Punishment Types
                            </div>
                            <div class="help-card-content">
                                <p>The admin panel offers several types of punishments that can be applied based on the severity of rule violations:</p>

                                <h6>Warning</h6>
                                <p>A formal notice to a player that their behavior violates server rules. Warnings don't restrict gameplay but serve as official documentation of misconduct.</p>

                                <h6>Mute</h6>
                                <p>Prevents a player from using chat functions for a specified period. Appropriate for chat violations, spam, or harassment.</p>

                                <h6>Kick</h6>
                                <p>Temporarily removes a player from the server. They can rejoin immediately but will see the reason for their kick. Useful for immediate intervention.</p>

                                <h6>Temporary Ban</h6>
                                <p>Prevents a player from joining the server for a specified duration. Used for more serious or repeated violations.</p>

                                <h6>Permanent Ban</h6>
                                <p>Indefinitely prevents a player from joining the server. Reserved for the most severe violations or persistent rule-breakers.</p>

                                <p>Always select the appropriate punishment type based on the nature and severity of the violation, following server guidelines.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-gavel"></i> Issuing Punishments
                            </div>
                            <div class="help-card-content">
                                <p>Follow these steps to properly issue a punishment:</p>
                                <ol>
                                    <li><strong>Find the Player</strong> - Use Player Search to locate the player's profile</li>
                                    <li><strong>Access Punishment Interface</strong> - Click the "Punish" button on their profile</li>
                                    <li><strong>Select Punishment Type</strong> - Choose the appropriate type based on the violation</li>
                                    <li><strong>Set Duration</strong> - For temporary punishments, specify an appropriate length</li>
                                    <li><strong>Provide Clear Reason</strong> - Write a specific, factual description of the violation</li>
                                    <li><strong>Add Evidence</strong> - Include screenshots, chat logs, or other relevant evidence</li>
                                    <li><strong>Review and Submit</strong> - Double-check all details before finalizing</li>
                                </ol>

                                <h6>Writing Effective Punishment Reasons</h6>
                                <p>Be specific about the rule violation, avoid personal opinions, and use clear language that the player can understand. Good example: "Using hate speech in global chat on 2023-05-15 at 3:45 PM EST."</p>

                                <h6>Progressive Discipline</h6>
                                <p>For repeat offenders, follow a progressive approach: warnings for first offenses, escalating to mutes, temporary bans, and finally permanent bans for continued violations.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Profile Section -->
            <div class="help-section" id="my-profile">
                <div class="help-section-title">
                    <i class="fas fa-user-cog"></i> My Profile
                </div>
                <div class="help-section-description">
                    Learn how to manage your staff account settings and preferences.
                </div>

                <div class="row g-4">
                    <div class="col-lg-12">
                        <div class="help-card">
                            <div class="help-card-title">
                                <i class="fas fa-id-card"></i> Account Management
                            </div>
                            <div class="help-card-content">
                                <p>Your staff account is linked to your Discord identity. Here's how to manage your account settings:</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Accessing Your Profile</h6>
                                        <p>Click on your username or profile picture in the top-right corner of the navbar to access your profile settings.</p>

                                        <h6>Viewing Your Information</h6>
                                        <p>On your profile page, you can view your:</p>
                                        <ul>
                                            <li>Username and Discord ID</li>
                                            <li>Staff role and permissions</li>
                                            <li>Account creation date</li>
                                            <li>Last login information</li>
                                        </ul>
                                    </div>

                                    <div class="col-md-6">
                                        <h6>Activity History</h6>
                                        <p>You can view a log of your recent actions in the admin panel, including reports processed, punishments issued, and appeals handled.</p>

                                        <h6>Theme Preferences</h6>
                                        <p>Toggle between light and dark mode using the theme switch in the navbar. Your preference will be saved for future sessions.</p>

                                        <h6>Security Best Practices</h6>
                                        <p>Always log out when using shared computers, and never share your login credentials with anyone. Your actions in the admin panel are tracked and attributed to your account.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Frequently Asked Questions -->
            <div class="help-section" id="faq">
                <div class="help-section-title">
                    <i class="fas fa-question-circle"></i> Frequently Asked Questions
                </div>
                <div class="help-section-description">
                    Quick answers to common questions about using the admin panel.
                </div>

                <div class="faq-accordion">
                    <div class="faq-item">
                        <div class="faq-question">
                            How do I reset my password? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            Password resets are handled through Discord authentication. If you need to reset your access, please contact an administrator to reset your Discord connection. This ensures that only authorized staff members can access the admin panel.
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            What should I do if I'm unsure about a report? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            If you're uncertain about how to handle a report, it's best to consult with a senior staff member or administrator. You can also check the server rules for guidance on specific violations. It's better to ask for help than to make an incorrect decision.
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            How long should punishments last for different violations? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            Punishment durations should follow the server's punishment guidelines. Generally, first offenses receive shorter punishments, while repeat offenses receive progressively longer ones. Severe violations like hacking may warrant immediate permanent bans. Always check the punishment guidelines document for specific recommendations.
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            Can I edit a punishment after it's been issued? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            Yes, you can modify a punishment by going to the player's profile, finding the punishment in their history, and clicking the edit button. Note that all changes are logged in the audit system, so there is full transparency about who made changes and when.
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            What information should I include in player notes? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            Player notes should be factual, concise, and relevant to staff operations. Include dates, specific behaviors, and any actions taken. Avoid personal opinions or unnecessary details. Remember that notes are visible to all staff members and should maintain a professional tone.
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            How do I view a player's punishment history? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            To view a player's punishment history, search for the player using the Player Search function, then navigate to their profile. The punishment history is displayed in a tab on their profile page, showing all past warnings, mutes, kicks, and bans.
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            What's the difference between a warning and a mute? <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            A warning is a formal notice to a player that their behavior violates server rules, but it doesn't restrict their gameplay. A mute prevents a player from using chat functions for a specified period. Warnings are typically used for first-time minor offenses, while mutes are used for chat-related violations.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Support -->
            <div class="help-section">
                <div class="help-section-title">
                    <i class="fas fa-life-ring"></i> Need More Help?
                </div>
                <div class="help-section-description">
                    If you couldn't find what you're looking for, reach out through one of these channels.
                </div>
                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="contact-option">
                            <div class="contact-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="contact-details">
                                <div class="contact-title">Discord Support</div>
                                <div class="contact-description">Join our staff Discord channel for real-time assistance from other staff members and administrators. This is the fastest way to get help with urgent issues.</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="contact-option">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <div class="contact-title">Email Support</div>
                                <div class="contact-description">Contact our admin <NAME_EMAIL> for technical issues, account problems, or other questions that require detailed assistance.</div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/admin-common.min.js?v=3"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {

            const searchInput = document.querySelector('.search-help input');

            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();

                    if (searchTerm.length > 1) {

                        searchInContent(searchTerm);
                    } else {

                        resetSearch();
                    }
                });
            }


            document.querySelectorAll('.help-category').forEach(category => {
                category.addEventListener('click', function(e) {

                    const link = this.closest('a');
                    if (link && link.getAttribute('href')) {

                    }
                });
            });


            document.querySelectorAll('.help-card .btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    const targetId = this.getAttribute('href');
                    if (targetId && targetId.startsWith('#')) {
                        e.preventDefault();

                        const targetElement = document.querySelector(targetId);
                        if (targetElement) {
                            targetElement.scrollIntoView({ behavior: 'smooth' });
                        }
                    }
                });
            });


            document.querySelectorAll('.faq-question').forEach(question => {
                question.addEventListener('click', function() {

                    this.classList.toggle('active');


                    const answer = this.nextElementSibling;


                    if (answer.classList.contains('show')) {
                        answer.classList.remove('show');
                        answer.style.maxHeight = null;
                    } else {
                        answer.classList.add('show');
                        answer.style.maxHeight = answer.scrollHeight + 'px';
                    }
                });
            });


            document.querySelectorAll('.video-tutorial').forEach(video => {
                video.addEventListener('click', function() {




                });
            });


            const firstFaqQuestion = document.querySelector('.faq-question');
            const firstFaqAnswer = document.querySelector('.faq-answer');

            if (firstFaqQuestion && firstFaqAnswer) {
                firstFaqQuestion.classList.add('active');
                firstFaqAnswer.classList.add('show');
                firstFaqAnswer.style.maxHeight = firstFaqAnswer.scrollHeight + 'px';
            }


            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    const targetId = this.getAttribute('href');
                    if (targetId !== '#') {
                        e.preventDefault();
                        const targetElement = document.querySelector(targetId);
                        if (targetElement) {

                            const headerOffset = 80;
                            const elementPosition = targetElement.getBoundingClientRect().top;
                            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                            window.scrollTo({
                                top: offsetPosition,
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        });


        function searchInContent(searchTerm) {

            const searchableElements = [
                { selector: '.help-category-title', parent: '.help-category', highlight: 'color' },
                { selector: '.help-category-description', parent: '.help-category', highlight: 'none' },
                { selector: '.help-card-title', parent: '.help-card', highlight: 'color' },
                { selector: '.help-card-content p', parent: '.help-card', highlight: 'none' },
                { selector: '.help-card-content li', parent: '.help-card', highlight: 'none' },
                { selector: '.step-title', parent: '.step-item', highlight: 'color' },
                { selector: '.step-description', parent: '.step-item', highlight: 'none' },
                { selector: '.faq-question', parent: '.faq-item', highlight: 'color' },
                { selector: '.faq-answer', parent: '.faq-item', highlight: 'weight' },
                { selector: '.video-title', parent: '.video-tutorial', highlight: 'color' },
                { selector: '.video-description', parent: '.video-tutorial', highlight: 'none' },
                { selector: '.contact-title', parent: '.contact-option', highlight: 'color' },
                { selector: '.contact-description', parent: '.contact-option', highlight: 'none' }
            ];


            const matchedContainers = new Set();


            searchableElements.forEach(item => {
                document.querySelectorAll(item.selector).forEach(element => {
                    const text = element.textContent.toLowerCase();
                    const container = element.closest(item.parent);

                    if (text.includes(searchTerm)) {

                        if (container) matchedContainers.add(container);


                        if (item.highlight === 'color') {
                            element.style.color = 'var(--accent-color)';
                        } else if (item.highlight === 'weight') {
                            element.style.fontWeight = '500';
                        }
                    } else {

                        if (item.highlight === 'color') {
                            element.style.color = '';
                        } else if (item.highlight === 'weight') {
                            element.style.fontWeight = '';
                        }
                    }
                });
            });


            document.querySelectorAll('.help-category, .help-card, .faq-item, .step-item, .video-tutorial, .contact-option').forEach(container => {
                if (matchedContainers.has(container)) {
                    container.style.display = '';


                    const parentRow = container.closest('.row');
                    const parentSection = container.closest('.help-section');
                    if (parentRow) parentRow.style.display = '';
                    if (parentSection) parentSection.style.display = '';


                    if (container.classList.contains('faq-item')) {
                        const question = container.querySelector('.faq-question');
                        const answer = container.querySelector('.faq-answer');
                        if (question && answer) {
                            question.classList.add('active');
                            answer.classList.add('show');
                            answer.style.maxHeight = answer.scrollHeight + 'px';
                        }
                    }
                } else {
                    container.style.display = 'none';
                }
            });


            document.querySelectorAll('.help-section').forEach(section => {
                const visibleItems = section.querySelectorAll('.help-category:not([style*="display: none"]), .help-card:not([style*="display: none"]), .faq-item:not([style*="display: none"]), .video-tutorial:not([style*="display: none"]), .contact-option:not([style*="display: none"])');
                if (visibleItems.length === 0) {
                    section.style.display = 'none';
                } else {
                    section.style.display = '';
                }
            });


            document.querySelectorAll('.faq-answer').forEach(answer => {
                if (answer.textContent.toLowerCase().includes(searchTerm) && answer.style.display !== 'none') {
                    const question = answer.previousElementSibling;
                    if (question) {
                        question.classList.add('active');
                        answer.classList.add('show');
                        answer.style.maxHeight = answer.scrollHeight + 'px';
                    }
                }
            });
        }


        function resetSearch() {

            document.querySelectorAll('.help-category-title, .help-card-title, .step-title, .faq-question, .video-title, .contact-title').forEach(el => {
                el.style.color = '';
            });

            document.querySelectorAll('.faq-answer, .step-description').forEach(el => {
                el.style.fontWeight = '';
            });


            document.querySelectorAll('.help-category, .help-card, .faq-item, .step-item, .video-tutorial, .contact-option, .help-section, .row').forEach(container => {
                container.style.display = '';
            });


            document.querySelectorAll('.faq-question').forEach((question, index) => {
                const answer = question.nextElementSibling;
                if (index === 0) {
                    question.classList.add('active');
                    answer.classList.add('show');
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                } else {
                    question.classList.remove('active');
                    answer.classList.remove('show');
                    answer.style.maxHeight = null;
                }
            });
        }
    </script>
</body>
</html>
