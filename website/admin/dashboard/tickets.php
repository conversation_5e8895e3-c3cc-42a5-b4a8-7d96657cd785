<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 

// Require ADMIN role or higher
require_auth(ROLE_ADMIN);
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Support Tickets | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    hasRequiredRole: <?php echo has_role(ROLE_ADMIN) ? 'true' : 'false'; ?>,
    userId: '<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>',
    username: '<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>',
    role: '<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>',
    avatar: '<?php echo htmlspecialchars($_SESSION['discord_avatar'] ?? ''); ?>'
};
    </script>
    <link href="../css/admin-common.css" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
    <style>
        .ticket-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .ticket-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .ticket-card {
            border-left-color: #007bff;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .ticket-meta {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .conversation-preview {
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }
        .conversation-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(transparent, white);
        }

        /* Privacy badges styling */
        .badge.bg-info {
            background-color: #0dcaf0 !important;
        }

        .badge.bg-secondary {
            background-color: #6c757d !important;
        }

        /* Dark mode adjustments */
        .dark-mode .conversation-preview::after {
            background: linear-gradient(transparent, #2d3748);
        }

        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }
    </style>
</head>
<body>
    <div class="admin-wrapper">
        <?php include '../includes/navbar.php'; ?>
        <?php include '../includes/sidebar.php'; ?>

        <div id="content" class="main-content">
            <div class="page-container">
                <!-- Content Header -->
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1>Support Tickets</h1>
                            <p class="text-muted">Manage customer support tickets and conversations</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="refreshTickets()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Open Tickets</h6>
                                        <h3 class="mb-0" id="openTicketsCount">-</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-ticket-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Pending Response</h6>
                                        <h3 class="mb-0" id="pendingResponseCount">-</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Resolved Today</h6>
                                        <h3 class="mb-0" id="resolvedTodayCount">-</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Avg Response Time</h6>
                                        <h3 class="mb-0" id="avgResponseTime">-</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-stopwatch fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Status</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Statuses</option>
                                    <option value="open">Open</option>
                                    <option value="pending">Pending Response</option>
                                    <option value="resolved">Resolved</option>
                                    <option value="closed">Closed</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Priority</label>
                                <select class="form-select" id="priorityFilter">
                                    <option value="">All Priorities</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Category</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">All Categories</option>
                                    <option value="technical">Technical Support</option>
                                    <option value="billing">Billing</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="bug">Bug Report</option>
                                    <option value="feature">Feature Request</option>
                                    <option value="privacy">GDPR Request</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" id="searchFilter" placeholder="Search tickets...">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tickets List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Tickets</h5>
                    </div>
                    <div class="card-body">
                        <div id="ticketsContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading tickets...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading tickets...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ticket Detail Modal -->
    <div class="modal fade" id="ticketModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ticket Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="ticketModalBody">
                    <!-- Ticket details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Admin Scripts -->
    <script src="../js/admin-common.min.js"></script>
    <script src="../js/tickets.min.js"></script>
</body>
</html>
