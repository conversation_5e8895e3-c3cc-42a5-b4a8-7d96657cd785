<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 
require_once __DIR__ . '/../includes/security.php';


require_auth(ROLE_TRAINEE); // TRAINEE_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>My Profile | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <?php include '../includes/auth-data.php'; ?>
    <link href="/css/admin-common.css" rel="stylesheet">
    <link href="/css/modern-theme.css" rel="stylesheet">
    <link href="/css/mobile.css" rel="stylesheet">
    <link href="/css/staff-profile.css" rel="stylesheet">
    <style>
        .profile-header {
            background-color: var(--primary-color);
            color: var(--text-light);
            padding: 2rem;
            border-radius: var(--border-radius-md);
            margin-bottom: 2rem;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: var(--accent-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            margin-right: 1.5rem;
        }

        .profile-info h2 {
            margin-bottom: 0.5rem;
            color: var(--text-light);
        }

        .profile-info .badge {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }

        .profile-section {
            margin-bottom: 2rem;
        }

        .profile-section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .profile-section-title i {
            margin-right: 0.75rem;
            color: var(--accent-color);
        }

        .info-card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius-md);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
        }

        .info-item {
            margin-bottom: 1rem;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .info-value {
            font-size: 1rem;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.25rem;
            padding-bottom: 1.25rem;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(58, 134, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: var(--accent-color);
            margin-right: 1rem;
            flex-shrink: 0;
        }

        /* Activity icon colors for different types */
        .activity-icon .fa-bullhorn {
            color: #3a86ff; /* Accent color for announcements */
        }

        .activity-content {
            flex-grow: 1;
        }

        .activity-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.875rem;
            color: var(--text-muted);
        }






    </style>
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>



    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Content Header -->
            <div class="content-header">
                <h1>My Profile</h1>
                <p class="text-muted">View and manage your staff profile information</p>
            </div>

            <!-- Profile Header -->
            <div class="profile-header d-flex align-items-center">
                <?php if (!empty($_SESSION['discord_avatar'])): ?>
                    <img src="<?php echo htmlspecialchars($_SESSION['discord_avatar']); ?>" class="profile-avatar" alt="Profile Avatar">
                <?php else: ?>
                    <div class="profile-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>

                <div class="profile-info">
                    <h2><?php echo htmlspecialchars($_SESSION['discord_username'] ?? 'Staff Member'); ?></h2>
                    <div class="d-flex align-items-center">
                        <?php
                        $roleClass = 'role-trainee';
                        $role = $_SESSION['discord_user_role'] ?? 'TRAINEE';

                        $upperRole = strtoupper($role);

                        if ($upperRole === 'OWNER') $roleClass = 'role-owner';
                        else if ($upperRole === 'DEVELOPER') $roleClass = 'role-developer';
                        else if ($upperRole === 'SUPERVISOR') $roleClass = 'role-supervisor';
                        else if ($upperRole === 'ADMIN') $roleClass = 'role-admin';
                        else if ($upperRole === 'MODERATOR') $roleClass = 'role-moderator';
                        else if ($upperRole === 'TRAINEE') $roleClass = 'role-trainee';
                        else if ($upperRole === 'UNAUTHORIZED') $roleClass = 'role-unauthorized';
                        ?>
                        <span class="role-badge <?php echo $roleClass; ?> me-2">
                            <?php echo htmlspecialchars($role); ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4">
                    <!-- Account Information -->
                    <div class="profile-section">
                        <div class="profile-section-title">
                            <i class="fas fa-user-circle"></i> Account Information
                        </div>
                        <div class="info-card">
                            <div class="info-item">
                                <div class="info-label">Username</div>
                                <div class="info-value"><?php echo htmlspecialchars($_SESSION['discord_username'] ?? 'Unknown'); ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Discord ID</div>
                                <div class="info-value"><?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? 'Unknown'); ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Role</div>
                                <div class="info-value"><?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? 'Staff'); ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Discord Email</div>
                                <div class="info-value"><?php echo htmlspecialchars($_SESSION['discord_email'] ?? 'Not provided'); ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Account Linked</div>
                                <div class="info-value">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i> Discord Connected
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8">


                    <!-- Recent Activity -->
                    <div class="profile-section">
                        <div class="profile-section-title">
                            <i class="fas fa-history"></i> Recent Activity
                        </div>
                        <div class="info-card">
                            <!-- Activity items will be loaded dynamically -->
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Loading activity data...</div>
                                    <div class="activity-time">Please wait</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Staff Performance -->
                    <div class="profile-section">
                        <div class="profile-section-title">
                            <i class="fas fa-chart-line"></i> Staff Performance
                        </div>
                        <div class="info-card">
                            <div class="row">
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="text-center">
                                        <h3 class="mb-1"><i class="fas fa-spinner fa-spin"></i></h3>
                                        <p class="text-muted mb-0">Reports Handled</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="text-center">
                                        <h3 class="mb-1"><i class="fas fa-spinner fa-spin"></i></h3>
                                        <p class="text-muted mb-0">Player Notes</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="text-center">
                                        <h3 class="mb-1"><i class="fas fa-spinner fa-spin"></i></h3>
                                        <p class="text-muted mb-0">Punishments Issued</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="mb-1"><i class="fas fa-spinner fa-spin"></i></h3>
                                        <p class="text-muted mb-0">Announcements Sent</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                </div>
            </div>
        </div>
    </div>

    <!-- Include common footer with scripts -->
    <?php include '../includes/footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
        // Always force dark mode - no toggle functionality
        document.documentElement.classList.add('dark-mode');
        document.body.classList.add('dark-mode');
        localStorage.setItem('darkMode', 'enabled');
        document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';
            fetchRecentActivity();
            fetchStaffPerformance();
        });


        function fetchRecentActivity() {

            const activityContainer = document.querySelector('.activity-item').parentNode;
            activityContainer.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Loading activity data...</div>
                        <div class="activity-time">Please wait</div>
                    </div>
                </div>
            `;


            $.ajax({
                url: '/adminapi/staff/activity',
                method: 'GET',
                dataType: 'json',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success && response.activities && response.activities.length > 0) {
                        displayActivities(response.activities);
                    } else {

                        displaySampleActivities();
                    }
                },
                error: function() {

                    displaySampleActivities();
                }
            });
        }


        function displayActivities(activities) {
            const activityContainer = document.querySelector('.activity-item').parentNode;
            activityContainer.innerHTML = '';


            const recentActivities = activities.slice(0, 5);

            if (recentActivities.length === 0) {
                activityContainer.innerHTML = `
                    <div class="activity-item">
                        <div class="activity-content text-center py-4">
                            <div class="text-muted">No recent activity found</div>
                        </div>
                    </div>
                `;
                return;
            }

            recentActivities.forEach(activity => {

                let timeFormatted;
                if (activity.formatted_date) {
                    timeFormatted = activity.formatted_date;
                } else {
                    const activityTime = new Date(activity.timestamp);
                    timeFormatted = formatTimeAgo(activityTime);
                }

                let icon = 'history';


                switch (activity.type) {
                    case 'login':
                        icon = 'sign-in-alt';
                        break;
                    case 'report':
                        icon = 'flag';
                        break;
                    case 'note':
                        icon = 'user-edit';
                        break;
                    case 'punishment':
                        icon = 'gavel';
                        break;
                    case 'appeal':
                        icon = 'balance-scale';
                        break;
                    case 'settings':
                        icon = 'cog';
                        break;
                    case 'announcement':
                        icon = 'bullhorn';
                        break;
                    default:
                        icon = 'history';
                }

                const activityHTML = `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.description}</div>
                            <div class="activity-time">${timeFormatted}</div>
                        </div>
                    </div>
                `;

                activityContainer.innerHTML += activityHTML;
            });


        }


        function displaySampleActivities() {
            const activityContainer = document.querySelector('.activity-item').parentNode;
            activityContainer.innerHTML = `
                <div class="activity-item">
                    <div class="activity-content text-center py-4">
                        <div class="text-muted">No recent activity found</div>
                    </div>
                </div>
            `;
        }


        function fetchStaffPerformance() {

            document.querySelector('.col-md-3:nth-child(1) h3').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            document.querySelector('.col-md-3:nth-child(2) h3').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            document.querySelector('.col-md-3:nth-child(3) h3').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            document.querySelector('.col-md-3:nth-child(4) h3').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';


            $.ajax({
                url: '/adminapi/staff/performance',
                method: 'GET',
                dataType: 'json',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success && response.performance) {
                        displayPerformance(response.performance);
                    } else {

                        displaySamplePerformance();
                    }
                },
                error: function() {

                    displaySamplePerformance();
                }
            });
        }


        function displayPerformance(performance) {

            document.querySelector('.col-md-3:nth-child(1) h3').textContent = performance.reportsHandled || 0;
            document.querySelector('.col-md-3:nth-child(2) h3').textContent = performance.playerNotes || 0;
            document.querySelector('.col-md-3:nth-child(3) h3').textContent = performance.punishmentsIssued || 0;
            document.querySelector('.col-md-3:nth-child(4) h3').textContent = performance.announcementsSent || 0;


            if (performance.reportAcceptRate !== undefined) {
                const acceptRate = Math.round(performance.reportAcceptRate * 100);
                const reportsElement = document.querySelector('.col-md-3:nth-child(1)');


                if (reportsElement) {
                    reportsElement.setAttribute('data-bs-toggle', 'tooltip');
                    reportsElement.setAttribute('data-bs-placement', 'top');
                    reportsElement.setAttribute('title', `Accept rate: ${acceptRate}%`);


                    new bootstrap.Tooltip(reportsElement);
                }
            }


            if (performance.timePeriod) {

                const performanceSections = document.querySelectorAll('.profile-section-title');
                let performanceSection = null;


                for (let i = 0; i < performanceSections.length; i++) {
                    if (performanceSections[i].textContent.includes('Staff Performance')) {
                        performanceSection = performanceSections[i];
                        break;
                    }
                }

                if (performanceSection) {

                    if (!performanceSection.querySelector('.time-period-label')) {
                        performanceSection.innerHTML += ` <span class="small text-muted time-period-label">(${performance.timePeriod})</span>`;
                    }
                }
            }
        }


        function displaySamplePerformance() {
            const performance = {
                reportsHandled: 0,
                playerNotes: 0,
                punishmentsIssued: 0,
                announcementsSent: 0,
                timePeriod: 'Last 30 days'
            };

            displayPerformance(performance);
        }


        function formatTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffSec = Math.floor(diffMs / 1000);
            const diffMin = Math.floor(diffSec / 60);
            const diffHour = Math.floor(diffMin / 60);
            const diffDay = Math.floor(diffHour / 24);

            if (diffDay > 0) {
                return diffDay === 1 ? 'Yesterday' : `${diffDay} days ago`;
            } else if (diffHour > 0) {
                return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
            } else if (diffMin > 0) {
                return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
            } else {
                return 'Just now';
            }
        }
    </script>
</body>
</html>
