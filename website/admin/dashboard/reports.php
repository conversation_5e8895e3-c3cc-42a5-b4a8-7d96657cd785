<?php

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 

require_auth(ROLE_TRAINEE); // TRAINEE_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Reports Dashboard | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>"
};
</script>
    <link href="/css/admin-common.css" rel="stylesheet">
    <link href="/css/reports-cards.css" rel="stylesheet">
    <style>


        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }
    </style>
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">Player Reports</h1>
                <div>
                    <span class="badge bg-warning me-2">Pending: <span id="pendingCount">0</span></span>
                    <span class="badge bg-secondary">Total: <span id="totalCount">0</span></span>
                </div>
            </div>



            <!-- Filter Controls -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="statusFilter" class="form-label">Status</label>
                                <select id="statusFilter" class="form-select">
                                    <option value="all">All Statuses</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Accepted">Accepted</option>
                                    <option value="Denied">Denied</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="ruleFilter" class="form-label">Rule Broken</label>
                                <select id="ruleFilter" class="form-select">
                                    <option value="all">All Rules</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="priorityFilter" class="form-label">Priority</label>
                                <select id="priorityFilter" class="form-select">
                                    <option value="all">All Priorities</option>
                                    <option value="1">High</option>
                                    <option value="2">Urgent</option>
                                    <option value="3">Medium</option>
                                    <option value="4">Low</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchFilter" class="form-label">Search</label>
                                <input type="text" id="searchFilter" class="form-control" placeholder="Search player names or report ID...">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button id="resetFilters" class="btn btn-secondary w-100">Reset Filters</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Container -->
            <div id="reportsContainer" class="row g-3">
                <!-- Reports will be loaded here -->
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading reports...</p>
                </div>
            </div>

            <!-- Pagination Controls -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="reports-showing">
                    Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="showingTotal">0</span> reports
                </div>
                <div class="btn-group" id="paginationControls">
                    <button class="btn btn-outline-primary" id="prevPage" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <button class="btn btn-outline-primary" id="nextPage" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Modal -->
    <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="reportModalLabel">Report Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="reportModalContent">
                    <!-- Report content will be loaded here -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" id="reportModalFooter">
                    <!-- Action buttons will be added here -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Admin Scripts -->
    <script src="../js/admin-common.min.js"></script>
    <script src="../js/reports.min.js"></script>

</body>
</html>
