<?php
/**
 * Form Helpers
 * Provides functions to help with form creation and validation
 */


require_once __DIR__ . '/security.php';

/**
 * Start a form with CSRF protection
 *
 * @param string $action Form action URL
 * @param string $method Form method (POST, GET, etc.)
 * @param array $attributes Additional form attributes
 * @return string Form opening HTML
 */
function form_open($action = '', $method = 'POST', $attributes = []) {

    $attr = [
        'action' => $action,
        'method' => $method,
        'class' => 'needs-validation',
        'novalidate' => 'novalidate'
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $value) {
        if ($value === true) {
            $attr_str .= " $key";
        } elseif ($value !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    

    $form = "<form$attr_str>";
    

    if (strtoupper($method) === 'POST') {
        $form .= csrf_token_input();
    }
    
    return $form;
}

/**
 * Close a form
 *
 * @return string Form closing HTML
 */
function form_close() {
    return '</form>';
}

/**
 * Create a form input field
 *
 * @param string $name Input name
 * @param string $value Input value
 * @param array $attributes Additional input attributes
 * @return string Input HTML
 */
function form_input($name, $value = '', $attributes = []) {

    $attr = [
        'type' => 'text',
        'name' => $name,
        'id' => $name,
        'value' => $value,
        'class' => 'form-control'
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<input$attr_str>";
}

/**
 * Create a form textarea
 *
 * @param string $name Textarea name
 * @param string $value Textarea value
 * @param array $attributes Additional textarea attributes
 * @return string Textarea HTML
 */
function form_textarea($name, $value = '', $attributes = []) {

    $attr = [
        'name' => $name,
        'id' => $name,
        'class' => 'form-control',
        'rows' => 5
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false && $key !== 'value') {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<textarea$attr_str>" . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . "</textarea>";
}

/**
 * Create a form select dropdown
 *
 * @param string $name Select name
 * @param array $options Select options
 * @param string $selected Selected option
 * @param array $attributes Additional select attributes
 * @return string Select HTML
 */
function form_select($name, $options = [], $selected = '', $attributes = []) {

    $attr = [
        'name' => $name,
        'id' => $name,
        'class' => 'form-select'
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    

    $select = "<select$attr_str>";
    

    foreach ($options as $value => $label) {
        $selected_attr = ($value == $selected) ? ' selected="selected"' : '';
        $select .= "<option value=\"" . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . "\"$selected_attr>" . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . "</option>";
    }
    

    $select .= "</select>";
    
    return $select;
}

/**
 * Create a form checkbox
 *
 * @param string $name Checkbox name
 * @param string $value Checkbox value
 * @param bool $checked Whether checkbox is checked
 * @param array $attributes Additional checkbox attributes
 * @return string Checkbox HTML
 */
function form_checkbox($name, $value = '1', $checked = false, $attributes = []) {

    $attr = [
        'type' => 'checkbox',
        'name' => $name,
        'id' => $name,
        'value' => $value,
        'class' => 'form-check-input'
    ];
    

    if ($checked) {
        $attr['checked'] = 'checked';
    }
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<input$attr_str>";
}

/**
 * Create a form radio button
 *
 * @param string $name Radio name
 * @param string $value Radio value
 * @param bool $checked Whether radio is checked
 * @param array $attributes Additional radio attributes
 * @return string Radio HTML
 */
function form_radio($name, $value, $checked = false, $attributes = []) {

    $attr = [
        'type' => 'radio',
        'name' => $name,
        'id' => $name . '_' . $value,
        'value' => $value,
        'class' => 'form-check-input'
    ];
    

    if ($checked) {
        $attr['checked'] = 'checked';
    }
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<input$attr_str>";
}

/**
 * Create a form submit button
 *
 * @param string $value Button text
 * @param array $attributes Additional button attributes
 * @return string Button HTML
 */
function form_submit($value = 'Submit', $attributes = []) {

    $attr = [
        'type' => 'submit',
        'value' => $value,
        'class' => 'btn btn-primary'
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<input$attr_str>";
}

/**
 * Create a form button
 *
 * @param string $content Button content
 * @param array $attributes Additional button attributes
 * @return string Button HTML
 */
function form_button($content = 'Button', $attributes = []) {

    $attr = [
        'type' => 'button',
        'class' => 'btn btn-secondary'
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<button$attr_str>" . $content . "</button>";
}

/**
 * Create a form label
 *
 * @param string $for Input ID
 * @param string $text Label text
 * @param array $attributes Additional label attributes
 * @return string Label HTML
 */
function form_label($for, $text, $attributes = []) {

    $attr = [
        'for' => $for,
        'class' => 'form-label'
    ];
    

    $attr = array_merge($attr, $attributes);
    

    $attr_str = '';
    foreach ($attr as $key => $val) {
        if ($val === true) {
            $attr_str .= " $key";
        } elseif ($val !== false) {
            $attr_str .= " $key=\"" . htmlspecialchars($val, ENT_QUOTES, 'UTF-8') . "\"";
        }
    }
    
    return "<label$attr_str>" . htmlspecialchars($text, ENT_QUOTES, 'UTF-8') . "</label>";
}
