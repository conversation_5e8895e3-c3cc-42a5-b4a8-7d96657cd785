<?php

require_once __DIR__ . '/version.php';


$releaseInfo = [
    'releaseName' => get_release_name(),
    'environment' => get_app_environment()
];
?>

<!-- Common JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Common Admin Scripts -->
<script src="/js/admin-common.min.js?v=1"></script>
<script src="/js/sidebar-fix.js?v=1"></script>
<script src="/js/sidebar-notifications.js?v=1"></script>

<!-- Sentry Release Health Configuration -->
<script>

    window.RELEASE_INFO = <?php echo json_encode($releaseInfo); ?>;
</script>
<script src="/js/sentry-config.min.js?v=1"></script>
<script defer src="https://cloud.umami.is/script.js" data-website-id="d7d1f96a-2af6-49a7-af19-3168f54f4944"></script>

<!-- Force Dark Mode Only -->
<script>
    $(document).ready(function() {
        // Always force dark mode - remove any toggle functionality
        document.documentElement.classList.add('dark-mode');
        document.body.classList.add('dark-mode');
        localStorage.setItem('darkMode', 'enabled');
        document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';

        // Hide any dark mode toggle elements
        const darkModeMenuItem = document.querySelector('.dropdown-item[href="#"][data-action="toggle-dark-mode"]');
        if (darkModeMenuItem) {
            darkModeMenuItem.style.display = 'none';
        }

        const darkModeToggle = document.querySelector('.dark-mode-toggle');
        if (darkModeToggle) {
            darkModeToggle.style.display = 'none';
        }
    });
</script>
