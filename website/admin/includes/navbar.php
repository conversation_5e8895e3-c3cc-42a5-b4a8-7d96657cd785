<nav class="navbar">
    <div class="navbar-brand">
        <button id="sidebarToggle" class="btn btn-icon d-lg-none me-2" type="button" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        <a href="/dashboard/dashboard">
            <i class="logo/logo.png" style="color: var(--accent-color);"></i>
            MassacreMC Staff
        </a>
    </div>

    <div class="navbar-content d-none d-lg-flex">
        <!-- Search bar removed -->
    </div>

    <div class="navbar-actions">
        <div class="d-flex align-items-center">
            <!-- User Info (No Dropdown) -->
            <div class="user-info d-flex align-items-center">
                <?php if (!empty($_SESSION['discord_avatar'])): ?>
                    <img src="<?php echo htmlspecialchars($_SESSION['discord_avatar']); ?>" class="avatar avatar-sm me-2" alt="Profile">
                <?php else: ?>
                    <div class="avatar avatar-sm me-2 d-flex align-items-center justify-content-center bg-primary text-white">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>
                <div class="d-none d-md-block">
                    <div class="fw-semibold text-primary"><?php echo htmlspecialchars($_SESSION['discord_username'] ?? 'User'); ?></div>
                    <?php

                    $role = strtoupper($_SESSION['discord_user_role'] ?? 'STAFF');
                    $roleColorClass = '';

                    if ($role === 'OWNER') {
                        $roleColorClass = 'style="color: #9b59b6;"'; // Purple
                    } elseif ($role === 'DEVELOPER') {
                        $roleColorClass = 'style="color: #2980b9;"'; // Blue
                    } elseif ($role === 'SUPERVISOR') {
                        $roleColorClass = 'style="color: #16a085;"'; // Teal
                    } elseif ($role === 'ADMIN') {
                        $roleColorClass = 'style="color: #e74c3c;"'; // Red
                    } elseif ($role === 'MODERATOR') {
                        $roleColorClass = 'style="color: #f39c12;"'; // Orange
                    } elseif ($role === 'TRAINEE') {
                        $roleColorClass = 'style="color: #8e44ad;"'; // Purple
                    } else {
                        $roleColorClass = 'style="color: #95a5a6;"'; // Gray
                    }
                    ?>
                    <div class="small" <?php echo $roleColorClass; ?>><?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? 'Staff'); ?></div>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Mobile Search Overlay removed -->

<!-- Sidebar Backdrop for Mobile -->
<div class="sidebar-backdrop" onclick="toggleSidebar()"></div>