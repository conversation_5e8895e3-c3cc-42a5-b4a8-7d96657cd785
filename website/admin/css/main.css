/* Main CSS file for Admin Panel */

/* Import CSS Variables */
@import url('variables.css');

/* Import Component Styles */
@import url('playerinfo.css');
@import url('notes.css');
@import url('punishments.css');
@import url('player-stats.css');

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--bg-body);
    color: var(--text-dark);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

/* Layout */
.container-fluid {
    padding: 1.5rem;
}

.row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Sidebar */
#sidebar {
    position: fixed;
    width: 250px;
    height: 100%;
    background: var(--bg-card);
    border-right: 1px solid var(--border-color);
    transition: all 0.3s;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    padding: 1.5rem;
    background: var(--primary-color);
    color: var(--text-light);
}

#sidebar ul.components {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

#sidebar ul li a {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    display: block;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s;
}

#sidebar ul li a:hover {
    background: var(--bg-hover);
    color: var(--primary-color);
}

#sidebar ul li.active > a {
    color: var(--primary-color);
    background: var(--bg-light);
    border-left: 4px solid var(--primary-color);
}

#sidebar ul li a i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Content */
#content {
    width: calc(100% - 250px);
    min-height: 100vh;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    right: 0;
}

#content.active {
    width: 100%;
}

/* Navbar */
.navbar {
    padding: 1rem 1.5rem;
    background: var(--bg-card);
    border: none;
    border-radius: 0;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
}

.navbar-btn {
    box-shadow: none;
    outline: none !important;
    border: none;
}

/* Search Bar */
.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input {
    border-radius: var(--border-radius-md);
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 1px solid var(--border-color);
    width: 100%;
    transition: all 0.3s;
    box-shadow: var(--shadow-sm);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.search-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s;
}

.search-button:hover {
    color: var(--primary-color);
}

/* Search History */
.search-history {
    margin-bottom: 1.5rem;
}

.history-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.history-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    color: var(--text-dark);
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--border-color);
}

.history-tag:hover {
    background-color: var(--bg-hover);
    transform: translateY(-2px);
}

.history-tag i {
    margin-right: 0.5rem;
}

.history-tag.player i {
    color: var(--primary-color);
}

.history-tag.faction i {
    color: var(--danger-color);
}

/* Responsive */
@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        width: 100%;
    }
    #content.active {
        width: calc(100% - 250px);
    }
    #sidebarCollapse span {
        display: none;
    }
}
