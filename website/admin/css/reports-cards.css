/* Card-based Reports Layout */

/* Report Card */
.report-card {
    transition: all 0.2s ease;
    height: 100%;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.report-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.report-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: none;
}

.report-card .card-body {
    padding: 1rem;
}

.report-card .card-footer {
    background-color: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
}

/* Status Badges */
.report-card .status-badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
    font-weight: 600;
    border-radius: 50rem;
}

.report-card .status-badge.pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
}

.report-card .status-badge.accepted {
    background-color: rgba(40, 167, 69, 0.2);
    color: #155724;
}

.report-card .status-badge.denied {
    background-color: rgba(220, 53, 69, 0.2);
    color: #721c24;
}

/* Player Info */
.report-card .player-name {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: var(--primary-color);
}

.report-card .rule-violation {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.report-card .report-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.report-card .evidence-preview {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 0.75rem;
    max-height: 3.6em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Card Actions */
.report-card .card-actions {
    display: flex;
    gap: 0.5rem;
}

.report-card .btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
}

/* Modal Styling */
#reportModal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

.swal2-container {
    z-index: 1060 !important;
}

.report-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.report-modal-header .report-id {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.report-modal-header .status-badge {
    font-size: 0.9rem;
    padding: 0.4em 0.8em;
}

.report-modal-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.report-modal-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.report-modal-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.report-modal-section-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.player-info-box {
    background-color: var(--bg-light);
    border-radius: 0.5rem;
    padding: 1rem;
}

.player-info-box .player-name {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--danger-color);
}

.player-info-box .rule-violation {
    display: inline-block;
    padding: 0.35em 0.65em;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
    font-size: 0.9rem;
}

.timeline-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.timeline-item {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.4rem;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.timeline-item .timeline-label {
    font-weight: 600;
    margin-right: 0.5rem;
}

.evidence-box {
    background-color: var(--bg-light);
    border-radius: 0.5rem;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Pagination */
.reports-showing {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Filter Controls */
.filter-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--bg-light);
    border-radius: 50rem;
    padding: 0.35em 0.75em;
    font-size: 0.85rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.filter-badge .filter-remove {
    margin-left: 0.5rem;
    cursor: pointer;
    opacity: 0.7;
}

.filter-badge .filter-remove:hover {
    opacity: 1;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state-text {
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto;
}

/* Dark Mode Styles */
body.dark-mode .report-card {
    background-color: var(--secondary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

body.dark-mode .report-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

body.dark-mode .report-card .card-header {
    background-color: rgba(0, 0, 0, 0.2);
}

body.dark-mode .report-card .card-footer {
    border-top-color: var(--border-color-dark);
}

body.dark-mode .report-card .player-name {
    color: var(--accent-color);
}

body.dark-mode .report-card .rule-violation,
body.dark-mode .report-card .evidence-preview {
    color: var(--text-light);
}

body.dark-mode .report-card .report-date {
    color: var(--text-muted);
}

body.dark-mode .status-badge.pending {
    background-color: rgba(255, 193, 7, 0.3);
    color: #ffc107;
}

body.dark-mode .status-badge.accepted {
    background-color: rgba(40, 167, 69, 0.3);
    color: #28a745;
}

body.dark-mode .status-badge.denied {
    background-color: rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

body.dark-mode .player-info-box {
    background-color: rgba(0, 0, 0, 0.2);
}

body.dark-mode .evidence-box {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color-dark);
}

body.dark-mode .filter-badge {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

body.dark-mode .empty-state-icon {
    color: var(--text-muted);
}

body.dark-mode .empty-state-text {
    color: var(--text-muted);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .report-card .card-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-card .btn-action {
        width: 100%;
        z-index: 1020 !important; /* Lower than navbar to prevent overlap */
        position: relative;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    /* Fix button overlap issues on mobile */
    .report-card .card-footer {
        z-index: 1020 !important;
        position: relative;
    }

    .report-card .card-actions .btn {
        z-index: 1020 !important;
        position: relative;
    }

    /* Ensure view details buttons don't overlap with navbar */
    .view-report {
        z-index: 1020 !important;
        position: relative;
    }
}
