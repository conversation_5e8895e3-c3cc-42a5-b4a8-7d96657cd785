/* Custom styles for the Reports page */

.report-details {
    position: fixed;
    top: 0;
    right: -450px; /* Start off-screen */
    width: 450px;
    height: 100%;
    background-color: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1040;
    transition: right 0.3s ease, background-color 0.3s ease, color 0.3s ease;
    overflow-y: auto;
    padding: 0;
    display: none; /* Hidden by default */
}

/* Dark mode for report details */
body.dark-mode .report-details {
    background-color: var(--secondary-color);
    color: var(--text-light);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.5);
}

.report-details.active {
    right: 0; /* Slide in when active */
    display: block;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
}

.report-header h4 {
    margin: 0;
    color: white;
}

.report-header .btn-close {
    color: white;
    opacity: 0.8;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

.report-header .btn-close:hover {
    opacity: 1;
}

/* Dark mode for report header */
body.dark-mode .report-header {
    background-color: var(--primary-color);
}

body.dark-mode .report-header h4 {
    color: white;
}

body.dark-mode .report-header .btn-close {
    color: white;
}

.report-content {
    padding: 20px;
}

.report-bg {
    background-color: white;
}

.report-modal {
    background-color: white;
    position: relative;
    width: 100%;
    height: 100%;
}

body.dark-mode .report-bg {
    background-color: var(--secondary-color);
}

/* Add styles for report detail sections */
.report-details-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.report-details-section:last-child {
    border-bottom: none;
}

/* Dark mode for report detail sections */
body.dark-mode .report-details-section {
    border-bottom: 1px solid var(--border-color-dark);
}

body.dark-mode .report-details-section:last-child {
    border-bottom: none;
}

body.dark-mode .report-details-section h5,
body.dark-mode .report-details-section h6 {
    color: var(--text-light);
}

/* Report sections */
.report-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    color: var(--text-dark);
}

.report-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.report-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

.report-section-title i {
    margin-right: 10px;
    color: var(--primary-color);
}

/* Status indicator */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    margin-bottom: 20px;
}

.status-indicator.pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
}

.status-indicator.accepted {
    background-color: rgba(40, 167, 69, 0.2);
    color: #155724;
}

.status-indicator.rejected {
    background-color: rgba(220, 53, 69, 0.2);
    color: #721c24;
}

.status-indicator i {
    margin-right: 8px;
}

/* Dark mode for status indicator */
body.dark-mode .status-indicator.pending {
    background-color: rgba(255, 193, 7, 0.3);
    color: #ffc107;
}

body.dark-mode .status-indicator.accepted {
    background-color: rgba(40, 167, 69, 0.3);
    color: #28a745;
}

body.dark-mode .status-indicator.rejected {
    background-color: rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

body.dark-mode .status-indicator * {
    background-color: transparent;
}

/* Player info */
.player-info {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.player-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--danger-color);
}

.rule-violation {
    display: inline-block;
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Dark mode for player info */
body.dark-mode .player-info {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-light);
    border: 1px solid var(--border-color-dark);
}

/* Timeline */
.timeline {
    list-style: none;
    padding: 0;
    margin: 0;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.timeline-item:after {
    content: '';
    position: absolute;
    left: 5px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-item:last-child:after {
    display: none;
}

.timeline-label {
    font-weight: 600;
    margin-right: 5px;
}

/* Dark mode for timeline */
body.dark-mode .timeline-item:after {
    background-color: var(--border-color-dark);
}

/* Evidence box */
.evidence-box {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    white-space: normal;
    word-break: break-word;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Dark mode for evidence box */
body.dark-mode .evidence-box {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color-dark);
    color: var(--text-light);
}

body.dark-mode .evidence-box * {
    background-color: transparent;
    color: var(--text-light);
}

/* Status badge colors */
.badge.bg-warning {
    color: #212529;
}

/* Dark mode for reports table */
body.dark-mode .reports-table {
    color: var(--text-light);
    background-color: var(--secondary-color);
}

body.dark-mode .reports-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-light);
    border-color: var(--border-color-dark);
}

body.dark-mode .reports-table td {
    border-color: var(--border-color-dark);
}

body.dark-mode .reports-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Dark mode for report modal */
body.dark-mode .report-modal {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

body.dark-mode .report-modal::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background-color: var(--secondary-color);
    z-index: -1;
}

body.dark-mode .report-content {
    color: var(--text-light);
    background-color: var(--secondary-color);
}

body.dark-mode .report-section-title {
    color: var(--text-light);
}

body.dark-mode .report-section {
    border-bottom-color: var(--border-color-dark);
    background-color: var(--secondary-color);
}

body.dark-mode .rule-violation {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

body.dark-mode .timeline-item {
    color: var(--text-light);
}

body.dark-mode .timeline-label {
    color: var(--text-muted);
}

body.dark-mode .resolution-content h5 {
    color: var(--text-light);
}

body.dark-mode .resolution-content p {
    color: var(--text-light);
}

/* Ensure all elements in the report modal have dark mode styling */
body.dark-mode .report-modal * {
    background-color: inherit;
}

/* Fix any remaining white spots in the report modal */
body.dark-mode .report-modal div,
body.dark-mode .report-modal span,
body.dark-mode .report-modal p,
body.dark-mode .report-modal h1,
body.dark-mode .report-modal h2,
body.dark-mode .report-modal h3,
body.dark-mode .report-modal h4,
body.dark-mode .report-modal h5,
body.dark-mode .report-modal h6,
body.dark-mode .report-modal ul,
body.dark-mode .report-modal li {
    background-color: transparent !important;
}

/* Ensure the report content has the correct background color */
body.dark-mode #reportContent {
    background-color: var(--secondary-color);
}

/* Dark mode for report details text */
body.dark-mode .report-details p,
body.dark-mode .report-details span,
body.dark-mode .report-details div {
    color: var(--text-light);
}

body.dark-mode .report-details .text-muted {
    color: var(--text-muted) !important;
}

/* Action buttons */
.report-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.report-actions .btn {
    padding: 10px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 140px;
}

.report-resolution {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.report-resolution.accepted {
    border-left: 4px solid var(--success-color);
}

.report-resolution.rejected {
    border-left: 4px solid var(--danger-color);
}

.resolution-icon {
    font-size: 2rem;
}

.resolution-icon.accepted {
    color: var(--success-color);
}

.resolution-icon.rejected {
    color: var(--danger-color);
}

.resolution-content h5 {
    margin-bottom: 5px;
    font-weight: 600;
}

.resolution-content p {
    margin-bottom: 0;
}

/* Dark mode for resolution */
body.dark-mode .report-resolution {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-light);
    border-color: var(--border-color-dark);
}

body.dark-mode .report-resolution strong {
    color: var(--text-light);
}

body.dark-mode .report-resolution * {
    background-color: transparent;
}

/* High Priority Report Styling */
.high-priority-card {
    border: 2px solid #dc3545 !important;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.high-priority-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: #dc3545;
}

.priority-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #dc3545;
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Dark mode for high priority cards */
body.dark-mode .high-priority-card {
    border-color: #dc3545 !important;
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .report-modal {
        max-width: 95%;
    }

    .report-actions {
        flex-direction: column;
    }

    .report-actions .btn {
        width: 100%;
    }

    /* Fix button overlap with navbar on mobile */
    .btn, .btn-group .btn {
        z-index: 1020 !important; /* Lower than navbar (1030) to prevent overlap */
        position: relative;
    }

    /* Ensure filter buttons don't overlap with navbar */
    #resetFilters {
        z-index: 1020 !important;
        position: relative;
    }

    /* Fix for view details buttons in cards */
    .view-report, .btn-action {
        z-index: 1020 !important;
        position: relative;
    }

    /* Add margin to prevent content from hiding behind navbar */
    .main-content {
        padding-top: 1rem;
    }

    /* Ensure card actions don't overlap */
    .card-actions {
        z-index: 1020 !important;
        position: relative;
    }

    .card-footer {
        z-index: 1020 !important;
        position: relative;
    }
}