/* Player Notes Styling */

/* Important Notes Section */
.important-notes-section {
    background-color: rgba(231, 76, 60, 0.08);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--danger-color);
    box-shadow: var(--shadow-sm);
}

.important-notes-section h5 {
    color: var(--danger-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.important-notes-section h5 i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

/* Regular Notes Section */
.regular-notes-section {
    background-color: rgba(58, 134, 255, 0.05);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-sm);
}

.regular-notes-section h5 {
    color: var(--accent-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.regular-notes-section h5 i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

/* Note Cards */
.note-card {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.25rem;
    transition: all 0.3s ease;
    overflow: hidden;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
}

.note-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Important Note Card */
.note-card.important {
    border-left: 4px solid var(--danger-color);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.1);
}

.note-card.important:hover {
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.15);
}

.note-card.important .card-header {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
    color: white;
    border-bottom: none;
}

.note-card.important .card-header .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

.note-card.important .important-badge {
    background-color: white;
    color: var(--danger-color);
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    box-shadow: var(--shadow-sm);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.note-card.important .important-badge i {
    font-size: 0.75rem;
}

/* Ensure better contrast for type badges in important notes */
.note-card.important .note-type-badge {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--text-dark);
    font-weight: 600;
    border: none;
}

.note-card.important .note-date {
    color: rgba(255, 255, 255, 0.9);
}

/* Note Card Header */
.note-card .card-header {
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

/* Note Card Actions */
.note-card-actions {
    display: flex;
    gap: 0.5rem;
}

.note-card-actions .btn {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    background-color: rgba(0, 0, 0, 0.05);
    border: none;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.note-card-actions .btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Note Type Badges */
.note-type-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    border: 1px solid transparent;
    box-shadow: var(--shadow-sm);
}

.note-type-badge i {
    margin-right: 0.375rem;
    font-size: 0.875rem;
}

.note-type-badge.behavior {
    background-color: rgba(231, 76, 60, 0.15);
    color: var(--danger-color);
    border-color: rgba(231, 76, 60, 0.3);
}

.note-type-badge.support {
    background-color: rgba(58, 134, 255, 0.15);
    color: var(--accent-color);
    border-color: rgba(58, 134, 255, 0.3);
}

.note-type-badge.payment {
    background-color: rgba(46, 204, 113, 0.15);
    color: var(--success-color);
    border-color: rgba(46, 204, 113, 0.3);
}

.note-type-badge.bug {
    background-color: rgba(243, 156, 18, 0.15);
    color: var(--warning-color);
    border-color: rgba(243, 156, 18, 0.3);
}

.note-type-badge.other {
    background-color: rgba(52, 152, 219, 0.15);
    color: var(--info-color);
    border-color: rgba(52, 152, 219, 0.3);
}

/* Note Content */
.note-card .card-body {
    padding: 1.25rem;
}

.note-content {
    white-space: pre-line;
    color: var(--text-dark);
    line-height: 1.6;
    font-size: 0.9375rem;
}

/* Staff Info */
.note-staff-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-muted);
}

.note-staff-info i {
    margin-right: 0.5rem;
}

.note-staff-avatar {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 0.75rem;
    overflow: hidden;
    position: relative;
}

.note-staff-avatar .staff-avatar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

/* Date Display */
.note-date {
    font-size: 0.875rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

/* Empty State */
.notes-empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    margin: 1.5rem 0;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
}

.notes-empty-state i {
    font-size: 3.5rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    opacity: 0.3;
}

.notes-empty-state h5 {
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    font-weight: 600;
}

.notes-empty-state p {
    color: var(--text-muted);
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Add Note Button */
.add-note-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    z-index: 1000;
    border: none;
}

.add-note-btn:hover {
    transform: scale(1.1);
    background-color: var(--accent-hover);
    box-shadow: 0 6px 16px rgba(58, 134, 255, 0.4);
}

.add-note-btn i {
    font-size: 1.5rem;
}

/* SweetAlert2 Modal Customizations */
.swal2-popup {
    padding: 1.5rem;
}

.swal2-html-container {
    text-align: left !important;
    margin: 1rem 0 !important;
}

/* Ensure proper styling for textareas in modals */
.swal2-html-container textarea,
.swal2-html-container .form-control {
    text-align: left;
    font-family: inherit;
    padding: 0.5rem;
    width: 100%;
    display: block;
}

/* Fix for SweetAlert2 textarea content alignment */
.swal2-popup textarea {
    text-align: left;
}

/* Ensure placeholder text is also left-aligned */
.swal2-html-container textarea::placeholder,
.swal2-html-container .form-control::placeholder {
    text-align: left;
}

/* Override any potential Bootstrap or SweetAlert2 styles */
.swal2-html-container .form-control {
    text-align: left !important;
}

.swal2-html-container .form-check {
    display: flex;
    align-items: center;
    text-align: left;
}

.swal2-html-container .form-check-input {
    margin-top: 0 !important;
}

.swal2-html-container .form-check-label {
    margin-left: 0.5rem;
}

/* Dark mode support for SweetAlert2 */
body.dark-mode .swal2-popup {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

body.dark-mode .swal2-title,
body.dark-mode .swal2-html-container {
    color: var(--text-light);
}

body.dark-mode .swal2-input,
body.dark-mode .swal2-textarea,
body.dark-mode .swal2-select {
    background-color: var(--bg-dark);
    color: var(--text-light);
    border-color: var(--border-color-dark);
}

body.dark-mode .swal2-html-container .form-control,
body.dark-mode .swal2-html-container .form-select {
    background-color: var(--bg-dark);
    color: var(--text-light);
    border-color: var(--border-color-dark);
}

body.dark-mode .swal2-html-container label {
    color: var(--text-light);
}

/* Custom classes for edit note modal */
.edit-note-container {
    text-align: left;
}

.edit-note-popup textarea,
.edit-note-popup .form-control,
.edit-note-popup .form-select {
    text-align: left;
    display: block;
    width: 100%;
    margin-bottom: 1rem;
}
