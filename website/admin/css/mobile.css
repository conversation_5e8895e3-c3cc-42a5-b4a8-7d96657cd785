/* Mobile-specific styles */

/* Small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) {
    /* Typography adjustments */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.35rem; }
    h4 { font-size: 1.2rem; }
    h5 { font-size: 1.1rem; }

    /* Padding adjustments */
    .card-body {
        padding: 1.25rem;
    }

    .card-header {
        padding: 1rem 1.25rem;
    }

    /* Table adjustments */
    .table-responsive-mobile {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Stack form elements */
    .form-group {
        margin-bottom: 1rem;
    }

    /* Adjust modal padding */
    .modal-body {
        padding: 1.25rem;
    }

    /* Adjust button sizes */
    .btn {
        padding: 0.4rem 0.8rem;
    }

    /* Make cards full width */
    .card {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
        border-radius: var(--border-radius-sm);
    }

    /* Adjust stats cards for mobile */
    .stats-card {
        flex-direction: column;
        text-align: center;
    }

    .stats-card .stats-icon {
        margin-right: 0;
        margin-bottom: 0.75rem;
    }

    /* Adjust search containers */
    .search-container {
        flex-direction: column;
    }

    .search-input-container {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* Improve search bar on mobile */
    .search-modern {
        width: 100%;
    }

    .search-modern input {
        width: 100%;
        font-size: 16px; /* Prevents iOS zoom on focus */
        height: 48px;
    }

    .search-modern i {
        font-size: 1rem;
    }

    /* Make search button more visible */
    #searchButton {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-weight: 500;
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    /* Further reduce padding */
    .main-content {
        padding: 1rem;
    }

    /* Stack buttons in modals */
    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }

    /* Adjust navbar */
    .navbar {
        padding: 0 0.75rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }

    /* Fix notifications dropdown on mobile */
    .dropdown-menu {
        position: fixed !important;
        top: var(--navbar-height) !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        border-radius: 0 !important;
        max-height: 80vh !important;
        overflow-y: auto !important;
    }

    /* Fix notifications container on mobile */
    .notifications-container {
        max-height: 60vh !important;
    }

    /* Fix profile icon and notifications on mobile */
    .navbar-actions {
        display: flex;
        align-items: center;
    }

    .navbar-actions .dropdown {
        position: static;
    }

    /* Ensure notification badge is visible */
    .navbar .badge {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(25%, -25%);
    }

    /* Fix staff badges on mobile */
    .staff-info .badge,
    .staff-info .role-badge {
        position: relative;
        display: inline-block;
        margin-top: 4px;
    }

    /* Improve sidebar toggle button for mobile */
    #sidebarToggle {
        padding: 0.6rem;
        margin-right: 0.75rem;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #sidebarToggle i {
        font-size: 1.2rem;
    }

    body.sidebar-open #sidebarToggle {
        background-color: var(--accent-color);
        color: white;
    }

    /* Hide certain elements on very small screens */
    .d-xs-none {
        display: none !important;
    }

    /* Make tables scroll horizontally */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Adjust form controls */
    .form-control, .form-select {
        font-size: 16px; /* Prevents iOS zoom on focus */
    }

    /* Fix for punishment page */
    .punishment-list {
        grid-template-columns: 1fr !important;
    }

    .punishment-card {
        width: 100% !important;
    }

    /* Fix for content containers */
    .container-fluid {
        padding: 0.75rem !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Fix for staff management page */
    .staff-card-container {
        grid-template-columns: 1fr !important;
    }
}

/* Tablet devices (less than 992px) */
@media (max-width: 991.98px) {
    /* Adjust grid layouts */
    .row-cols-md-3 > * {
        flex: 0 0 50%;
        max-width: 50%;
    }

    /* Adjust sidebar toggle button */
    #sidebarToggle {
        display: block;
    }
}

/* Touch device optimizations */
@media (hover: none) {
    /* Increase tap target sizes */
    .btn, .nav-link, .dropdown-item {
        padding: 0.5rem 1rem;
    }

    /* Remove hover effects that might cause confusion */
    .table-hover tbody tr:hover {
        background-color: transparent;
    }

    /* Adjust form controls for touch */
    .form-control, .form-select {
        padding: 0.5rem 0.75rem;
    }
}

/* Orientation specific adjustments */
@media (orientation: landscape) and (max-height: 500px) {
    /* Adjust modal height for landscape on small devices */
    .modal-dialog {
        max-height: 90vh;
    }

    .modal-body {
        max-height: 60vh;
        overflow-y: auto;
    }
}

/* Removed auto dark mode - only dark mode is supported */

/* Mobile sidebar styles */
@media (max-width: 767.98px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 1050; /* Higher z-index to ensure it's above all content */
        width: 85%; /* Make sidebar wider on mobile for better usability */
        max-width: 300px;
    }

    body.sidebar-open .sidebar {
        transform: translateX(0);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3); /* Add shadow for better visibility */
    }

    .main-content {
        margin-left: 0;
        width: 100% !important;
    }

    .sidebar-backdrop {
        display: none;
        z-index: 1049; /* Just below sidebar */
    }

    body.sidebar-open .sidebar-backdrop {
        display: block;
        opacity: 1;
        width: 100vw; /* Ensure it covers the entire viewport width */
        height: calc(100vh - var(--navbar-height)); /* Ensure it covers the entire viewport height */
    }

    /* Fix for admin content when sidebar is open */
    body.sidebar-open .admin-content {
        position: relative;
        overflow: hidden;
    }
}

/* Mobile styles for sidebar user profile */
@media (max-width: 767.98px) {
    .sidebar-section.mt-auto {
        margin-top: 2rem !important;
    }

    .sidebar-user-info {
        padding: 0.75rem 1rem;
    }

    .sidebar-avatar {
        width: 36px;
        height: 36px;
    }

    .sidebar-username {
        font-size: 0.9rem;
    }

    .sidebar-role {
        font-size: 0.75rem;
    }

    .discord-id-text {
        max-width: 150px;
    }
}

/* Dark mode styles for mobile elements */
body.dark-mode #sidebarToggle {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

body.dark-mode .sidebar-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Dark mode styles for sidebar user profile */
body.dark-mode .sidebar-discord-id {
    background-color: rgba(255, 255, 255, 0.05) !important;
}
