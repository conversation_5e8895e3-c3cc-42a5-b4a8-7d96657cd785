/* Custom styles for the Appeals page */

/* Modal stylings for appeal details */
.modal-content {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-header {
    background-color: var(--primary-color);
    color: #fff;
    border-bottom: none;
}

.modal-header .btn-close {
    color: white;
    opacity: 0.8;
}

.modal-header .btn-close:hover {
    opacity: 1;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding-top: 15px;
    padding-bottom: 15px;
}

/* Status badges */
.badge {
    padding: 0.5em 0.7em;
    font-weight: 500;
}

/* Card stylings for appeal info */
.card.bg-light {
    background-color: #f8f9fa !important;
    border: none;
    box-shadow: none;
}

.card.bg-light .card-header {
    background-color: #e9ecef;
    border-bottom: none;
    font-weight: 500;
}

.card.bg-light .card-body {
    padding: 15px;
    white-space: pre-wrap;
}

/* Button spacing */
.modal-footer .btn {
    padding-left: 20px;
    padding-right: 20px;
}

/* Datatable customization */
.dataTables_filter input {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 5px 10px;
    margin-left: 5px;
}

.dataTables_length select {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 5px 10px;
    margin: 0 5px;
}