/* Additional fixes for punishment UI in dark mode */

/* Ensure template options have white text in dark mode */
body.dark-mode #punishmentModal .dropdown-menu div,
body.dark-mode #punishmentModal .dropdown-menu a,
body.dark-mode #punishmentModal .dropdown-menu .reason-template-item,
body.dark-mode #punishmentModal .reason-templates .dropdown-menu div {
    color: white !important;
}

/* Fix for hover state */
body.dark-mode #punishmentModal .dropdown-menu div:hover,
body.dark-mode #punishmentModal .dropdown-menu a:hover,
body.dark-mode #punishmentModal .dropdown-menu .reason-template-item:hover,
body.dark-mode #punishmentModal .reason-templates .dropdown-menu div:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white !important;
}

/* Fix for offense progression in dark mode */
body.dark-mode #offenseProgressionInfo .list-group-item {
    background-color: rgba(255, 255, 255, 0.05);
    color: white;
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode #offenseProgressionInfo h6 {
    color: white;
}

body.dark-mode #offenseProgressionInfo .bg-secondary.bg-opacity-10 {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Dark dropdown styling for punishment modals */
body.dark-mode #punishmentModal .form-select,
body.dark-mode #addPunishmentModal .form-select,
body.dark-mode .punishment-modal .form-select {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

body.dark-mode #punishmentModal .form-select:focus,
body.dark-mode #addPunishmentModal .form-select:focus,
body.dark-mode .punishment-modal .form-select:focus {
    background-color: #2d3748 !important;
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    color: #e2e8f0 !important;
}

body.dark-mode #punishmentModal .form-select option,
body.dark-mode #addPunishmentModal .form-select option,
body.dark-mode .punishment-modal .form-select option {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
}

/* Fix for punishment modal title in dark mode */
body.dark-mode #punishmentModal .modal-title,
body.dark-mode #addPunishmentModal .modal-title,
body.dark-mode .punishment-modal .modal-title {
    color: white !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 40px) !important;
    font-family: inherit !important;
}

/* Fix for punishment modal header in dark mode */
body.dark-mode #punishmentModal .modal-header,
body.dark-mode #addPunishmentModal .modal-header,
body.dark-mode .punishment-modal .modal-header {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 1rem 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

/* Ensure the modal header text is visible */
body.dark-mode #punishmentModal .modal-header *,
body.dark-mode #addPunishmentModal .modal-header *,
body.dark-mode .punishment-modal .modal-header * {
    color: white !important;
}
