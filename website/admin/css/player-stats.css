/* Player Stats Styles */

.player-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stat-title {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    color: var(--text-dark);
    font-size: 1.25rem;
    font-weight: 600;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

/* Player Avatar */
.player-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

/* Card Header with Gradient */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-bottom: none;
    padding: 1.25rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.header-actions .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.header-actions .btn:hover {
    transform: translateY(-2px);
}

/* Info Card */
.info-card {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    background-color: var(--bg-card);
}

.info-card .card-body {
    padding: 1.5rem;
}

/* Tabs */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
    color: var(--text-muted);
    border: none;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: var(--text-dark);
    border-bottom-color: var(--border-color);
}

.nav-tabs .nav-link.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
    background-color: transparent;
}

.tab-content {
    padding: 1.5rem 0;
}

/* Card Styles */
.card {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    color: var(--text-dark);
}

.card-body {
    padding: 1.25rem;
}

/* Form Styles */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    padding: 0.625rem 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--accent-rgb), 0.25);
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    border-color: var(--accent-hover);
    transform: translateY(-2px);
}

.btn-light {
    background-color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-light:hover {
    background-color: white;
    transform: translateY(-2px);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 50rem;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-spinner.active {
    opacity: 1;
    visibility: visible;
}

.spinner-container {
    text-align: center;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.25rem;
    color: var(--accent-color);
}

.spinner-text {
    margin-top: 1rem;
    color: var(--text-dark);
    font-weight: 500;
}
