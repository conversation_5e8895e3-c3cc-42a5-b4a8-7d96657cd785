/* Fix for dropdown menu positioning in punishment modal */

/* Change the dropdown to appear below the button instead of above */
#punishmentModal .reason-templates .dropdown-menu,
#punishmentModal .templates-dropdown {
    position: absolute;
    top: 100% !important;
    bottom: auto !important;
    left: 0 !important;
    right: auto !important;
    margin-top: 5px !important;
    margin-bottom: 0 !important;
    max-height: 200px;
    overflow-y: auto;
    width: 250px;
    z-index: 1080 !important;
}

/* Ensure the dropdown is visible and doesn't get cut off */
#punishmentModal .reason-templates,
#punishmentModal .dropdown {
    position: relative !important;
}

/* Make sure the dropdown items are properly styled */
#punishmentModal .reason-templates .dropdown-menu .reason-template-item,
#punishmentModal .templates-dropdown .reason-template-item {
    display: block;
    width: 100%;
    padding: 8px 12px;
    clear: both;
    font-weight: 400;
    text-align: left;
    white-space: normal;
    border: 0;
    cursor: pointer;
}

/* Fix for dark mode */
body.dark-mode #punishmentModal .reason-templates .dropdown-menu,
body.dark-mode #punishmentModal .templates-dropdown {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

/* Ensure the dropdown doesn't get cut off by the modal */
#punishmentModal .modal-body {
    overflow: visible !important;
}

#punishmentModal .modal-content {
    overflow: visible !important;
}

/* Ensure the dropdown has a higher z-index than other elements */
#punishmentModal .dropdown-menu {
    z-index: 1080 !important;
}

/* Add hover effect */
#punishmentModal .reason-template-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

body.dark-mode #punishmentModal .reason-template-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
