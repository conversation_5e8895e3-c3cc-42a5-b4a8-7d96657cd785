/* Modal Title Font Fix */
/* Ensures all modal titles display properly with correct font styling */

/* Base modal title styling */
.modal-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 50px) !important; /* Account for close button */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif !important;
    color: inherit !important;
}

/* Specific fixes for punishment modal */
#punishmentModal .modal-title,
#addPunishmentModal .modal-title,
.punishment-modal .modal-title {
    color: white !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 50px) !important;
    font-family: inherit !important;
}

/* Dark mode specific fixes */
body.dark-mode .modal-title {
    color: white !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: calc(100% - 50px) !important;
    font-family: inherit !important;
}

/* Ensure modal headers have proper styling */
.modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1rem 1.5rem !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125) !important;
}

body.dark-mode .modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.125) !important;
}

/* Fix for any text rendering issues */
.modal-title * {
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .modal-title {
        font-size: 1.1rem !important;
        max-width: calc(100% - 40px) !important;
    }
}

/* Fix for specific modal types */
#noteModal .modal-title,
#rankModal .modal-title,
#punishmentDetailsModal .modal-title {
    color: inherit !important;
}

/* Ensure close button doesn't interfere */
.modal-header .btn-close {
    margin-left: auto !important;
    flex-shrink: 0 !important;
}
