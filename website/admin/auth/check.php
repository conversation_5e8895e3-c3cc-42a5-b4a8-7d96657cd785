<?php

require_once __DIR__ . '/../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


header('Content-Type: application/json');


if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode([
        'authenticated' => false,
        'message' => 'Not authenticated'
    ]);
    exit;
}


refresh_session();


echo json_encode([
    'authenticated' => true,
    'user' => [
        'id' => $_SESSION['discord_user_id'],
        'username' => $_SESSION['discord_username'],
        'role' => $_SESSION['discord_user_role'],
        'avatar' => $_SESSION['discord_avatar'] ?? null
    ]
]);
?>
