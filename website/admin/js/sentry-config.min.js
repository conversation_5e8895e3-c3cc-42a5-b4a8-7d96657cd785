document.addEventListener("DOMContentLoaded",function(){if("undefined"!=typeof Sentry){const e=window.RELEASE_INFO||{},n=e.releaseName||"massacre-portal@unknown",t=e.environment||"development",i=window.AUTH_DATA||{},o=i.user_id||"",s=i.username||"",a=i.email||"",r=i.role||"";Sentry.onLoad(function(){Sentry.configureScope(function(e){e.setTag("release",n),e.setTag("environment",t)}),o&&Sentry.setUser({id:o,username:s,email:a,role:r}),Sentry.startSession(),console.log("Sentry configured with release: "+n+" in environment: "+t)}),document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState?Sentry.endSession():"visible"===document.visibilityState&&Sentry.startSession()}),window.addEventListener("beforeunload",function(){Sentry.endSession()})}else console.error("Sentry is not available")});
