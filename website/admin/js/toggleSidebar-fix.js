
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content') || document.querySelector('.admin-content');
    const body = document.body;
    const backdrop = document.querySelector('.sidebar-backdrop');

    if (sidebar) {
        sidebar.classList.toggle('active');
    }
    
    if (content) {
        content.classList.toggle('active');
    }
    
    if (body) {
        body.classList.toggle('sidebar-open');
    }


    if (backdrop) {
        if (body.classList.contains('sidebar-open')) {
            backdrop.style.display = 'block';

            setTimeout(() => {
                backdrop.style.opacity = '1';
            }, 10);
        } else {
            backdrop.style.opacity = '0';

            setTimeout(() => {
                backdrop.style.display = 'none';
            }, 300); // Match the transition duration
        }
    }
}


window.toggleSidebar = toggleSidebar;
