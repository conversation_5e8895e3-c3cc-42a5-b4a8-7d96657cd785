// Dark Mode Player Info Implementation
$(document).ready(function() {
    // Initialize dark mode player info
    initializeDarkPlayerInfo();
});

function initializeDarkPlayerInfo() {
    // Add dark mode styles
    addDarkModeStyles();
    
    // Initialize tab functionality
    initializeTabs();
}

function addDarkModeStyles() {
    const darkStyles = `
        <style>
        /* Dark Mode Player Info Styles */
        .dark-player-container {
            background: #1a1a1a;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .dark-player-header {
            background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%);
            padding: 24px;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .player-avatar-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .player-avatar {
            position: relative;
            width: 64px;
            height: 64px;
        }

        .player-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            border: 2px solid #444;
        }

        .status-dot {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #1a1a1a;
        }

        .status-dot.online {
            background: #10b981;
        }

        .status-dot.offline {
            background: #6b7280;
        }

        .player-details h1.player-name {
            color: #fff;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .player-badges {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        .rank-badge, .faction-badge, .no-faction-badge, .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .rank-badge {
            background: #3b82f6;
            color: white;
        }

        .faction-badge {
            background: #8b5cf6;
            color: white;
        }

        .no-faction-badge {
            background: #6b7280;
            color: white;
        }

        .status-badge.online {
            background: #10b981;
            color: white;
        }

        .status-badge.offline {
            background: #6b7280;
            color: white;
        }

        .player-meta {
            display: flex;
            gap: 16px;
            color: #9ca3af;
            font-size: 14px;
        }

        .player-meta i {
            margin-right: 4px;
        }

        .player-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            background: #374151;
            color: #fff;
            border: 1px solid #4b5563;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn:hover {
            background: #4b5563;
            border-color: #6b7280;
        }

        .action-btn.rank-btn:hover {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .action-btn.punish-btn:hover {
            background: #ef4444;
            border-color: #ef4444;
        }

        .action-btn.note-btn:hover {
            background: #f59e0b;
            border-color: #f59e0b;
        }

        /* Tabs */
        .dark-tabs-nav {
            background: #262626;
            display: flex;
            border-bottom: 1px solid #333;
        }

        .tab-btn {
            background: none;
            border: none;
            color: #9ca3af;
            padding: 16px 24px;
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 3px solid transparent;
            font-weight: 500;
        }

        .tab-btn:hover {
            color: #fff;
            background: #333;
        }

        .tab-btn.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: #1a1a1a;
        }

        .tab-btn i {
            margin-right: 8px;
        }

        /* Tab Content */
        .dark-tab-content {
            background: #1a1a1a;
        }

        .tab-panel {
            display: none;
            padding: 24px;
        }

        .tab-panel.active {
            display: block;
        }

        .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .tab-header h2 {
            color: #fff;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .tab-header i {
            margin-right: 8px;
        }

        .add-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .add-btn:hover {
            background: #2563eb;
        }

        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
        }

        .info-card {
            background: #262626;
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }

        .card-header {
            background: #2d2d2d;
            padding: 16px;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-header i {
            color: #3b82f6;
            font-size: 18px;
        }

        .card-header h3 {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .card-content {
            padding: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #333;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item .label {
            color: #9ca3af;
            font-weight: 500;
        }

        .info-item .value {
            color: #fff;
            font-weight: 600;
        }

        .info-item .value.online {
            color: #10b981;
        }

        .info-item .value.offline {
            color: #6b7280;
        }

        .info-item .value.monospace {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #1f1f1f;
            border-radius: 6px;
            border: 1px solid #333;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .stat-icon.kills { background: #ef4444; color: white; }
        .stat-icon.deaths { background: #6b7280; color: white; }
        .stat-icon.kdr { background: #10b981; color: white; }
        .stat-icon.balance { background: #f59e0b; color: white; }
        .stat-icon.strength { background: #8b5cf6; color: white; }
        .stat-icon.killstreak { background: #f97316; color: white; }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            display: block;
            color: #fff;
            font-size: 18px;
            font-weight: 700;
        }

        .stat-label {
            display: block;
            color: #9ca3af;
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* Content Area */
        .content-area {
            background: #262626;
            border: 1px solid #333;
            border-radius: 8px;
            min-height: 300px;
        }

        /* Loading State */
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: #9ca3af;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #333;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Empty State */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: #9ca3af;
            text-align: center;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h4 {
            color: #fff;
            margin-bottom: 8px;
        }
        </style>
    `;
    
    if (!document.getElementById('dark-player-styles')) {
        const styleElement = document.createElement('div');
        styleElement.id = 'dark-player-styles';
        styleElement.innerHTML = darkStyles;
        document.head.appendChild(styleElement);
    }
}

function initializeTabs() {
    $(document).on('click', '.tab-btn', function() {
        const tabName = $(this).data('tab');
        
        // Update tab buttons
        $('.tab-btn').removeClass('active');
        $(this).addClass('active');
        
        // Update tab panels
        $('.tab-panel').removeClass('active');
        $(`#${tabName}-panel`).addClass('active');
        
        // Load content for the selected tab
        const playerName = getCurrentPlayerName();
        if (playerName) {
            switch(tabName) {
                case 'punishments':
                    loadPunishments(playerName);
                    break;
                case 'notes':
                    loadNotes(playerName);
                    break;
                case 'accounts':
                    loadConnectedAccounts(playerName);
                    break;
            }
        }
    });
}

function getCurrentPlayerName() {
    return document.getElementById('searchInput')?.value?.trim() || '';
}
