document.addEventListener('DOMContentLoaded', function() {

    $(document).on('click', '.mark-read', function() {
        const announcementId = $(this).data('id');

        fetch('../adminapi/announcements/mark-read.php', {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Discord-ID': window.AUTH_DATA.user_id || '',
                'X-Discord-Username': window.AUTH_DATA.username || '',
                'X-Discord-Role': window.AUTH_DATA.role || '',
                'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                announcement_id: announcementId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(response => {
            if (response.success) {

                loadAnnouncements();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: response.message || 'Failed to mark announcement as read',
                    confirmButtonColor: '#3085d6'
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to mark announcement as read: ' + error.message,
                confirmButtonColor: '#3085d6'
            });
        });
    });

    loadDashboardStats();


    document.getElementById('refreshAnnouncements').addEventListener('click', function() {
        loadAnnouncements();
    });


    // Store interval ID for cleanup
    window.dashboardStatsInterval = setInterval(loadDashboardStats, 120000);

    // Clean up interval when page unloads
    window.addEventListener('beforeunload', function() {
        if (window.dashboardStatsInterval) {
            clearInterval(window.dashboardStatsInterval);
        }
    });


    loadAnnouncements();

    function loadDashboardStats() {
        $.ajax({
            url: '../adminapi/dashboard/stats.php',
            method: 'GET',
            dataType: 'json',
            xhrFields: {
                withCredentials: true  // This ensures cookies are sent with the request
            },
            success: function(response) {

                $('#pendingReportsCount').text(response.pendingReports || 0);
                $('#highPriorityReportsCount').text(response.highPriorityReports || 0);
                $('#pendingAppealsCount').text(response.pendingAppeals || 0);
                $('#activeBansCount').text(response.activeBans || 0);
                $('#activeMutesCount').text(response.activeMutes || 0);


                renderOnlineStaff(response.onlineStaff || []);


                loadAnnouncements();
            },
            error: function(xhr, status, error) {

                if (xhr.status === 401) {

                    document.cookie.split(";").forEach(function(c) {
                        document.cookie = c.replace(/^ +/, "").replace(/=.*/,
                            "=;expires=" + new Date().toUTCString() + ";path=/");
                    });

                    window.location.href = '../login?force_logout=1';
                    return;
                }


                $('#onlineStaff').html(`<div class="alert alert-danger">Failed to load staff data</div>`);


                $('#pendingReportsCount, #highPriorityReportsCount, #pendingAppealsCount, #activeBansCount, #activeMutesCount').text('N/A');
            }
        });
    }

    function renderOnlineStaff(staffList) {
        if (!staffList || staffList.length === 0) {
            $('#onlineStaff').html('<div class="text-center py-4"><i class="fas fa-users mb-3" style="font-size: 2rem; opacity: 0.2;"></i><p class="text-muted">No staff currently online</p></div>');
            return;
        }

        const staffHTML = staffList.map(staff => {

            let roleClass = 'role-trainee';

            const upperRole = (staff.role || '').toUpperCase();

            if (upperRole === 'OWNER') roleClass = 'role-owner';
            else if (upperRole === 'DEVELOPER') roleClass = 'role-developer';
            else if (upperRole === 'SUPERVISOR') roleClass = 'role-supervisor';
            else if (upperRole === 'ADMIN') roleClass = 'role-admin';
            else if (upperRole === 'MODERATOR') roleClass = 'role-moderator';
            else if (upperRole === 'TRAINEE') roleClass = 'role-trainee';
            else if (upperRole === 'UNAUTHORIZED') roleClass = 'role-unauthorized';


            const avatarHTML = staff.avatar_url ?
                `<img src="${staff.avatar_url}" class="rounded-circle" width="36" height="36" alt="${staff.username}">` :
                `<i class="fas fa-user"></i>`;

            return `
                <div class="staff-item">
                    <div class="staff-avatar">
                        ${avatarHTML}
                    </div>
                    <div class="staff-info">
                        <h6>${staff.username}</h6>
                        <p><span class="role-badge ${roleClass}">${staff.role}</span></p>
                    </div>
                    <div class="staff-status ms-auto"></div>
                </div>
            `;
        }).join('');

        $('#onlineStaff').html(staffHTML);
    }




    function loadAnnouncements() {
        $('#announcementsList').html(`
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `);

        fetch('../adminapi/announcements/list', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Discord-ID': window.AUTH_DATA.user_id || '',
                'X-Discord-Username': window.AUTH_DATA.username || '',
                'X-Discord-Role': window.AUTH_DATA.role || '',
                'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(response => {
            if (response.success && response.announcements && response.announcements.length > 0) {
                renderAnnouncements(response.announcements);
            } else {
                $('#announcementsList').html(`
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        No announcements found.
                    </div>
                `);
            }
        })
        .catch(error => {
            $('#announcementsList').html(`
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Failed to load announcements: ${error.message}
                </div>
            `);
        });
    }


    function renderAnnouncements(announcements) {
        if (!announcements || announcements.length === 0) {
            $('#announcementsList').html('<div class="text-center py-4"><i class="fas fa-bullhorn mb-3" style="font-size: 2rem; opacity: 0.2;"></i><p class="text-muted">No announcements found</p></div>');
            return;
        }


        announcements.sort((a, b) => {
            const dateA = new Date(a.timestamp);
            const dateB = new Date(b.timestamp);
            return dateB - dateA;
        });


        const recentAnnouncements = announcements.slice(0, 5);

        const announcementsHTML = recentAnnouncements.map(announcement => {

            let displayDate;

            if (announcement.formatted_date) {
                displayDate = announcement.formatted_date;
            } else if (announcement.timestamp) {
                displayDate = formatDate(announcement.timestamp);
            } else {
                displayDate = 'Unknown date';
            }

            return `
                <div class="activity-item d-flex align-items-start">
                    <div class="activity-icon bg-gradient-primary">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <div class="activity-content">
                        <h6><strong>${announcement.staff_name || announcement.sender_name || 'Admin'}</strong> posted an announcement</h6>
                        <p style="white-space: pre-line;">${announcement.message}</p>
                    </div>
                    <div class="activity-time ms-auto">
                        ${displayDate}
                    </div>
                </div>
            `;
        }).join('');

        $('#announcementsList').html(announcementsHTML);
    }
});

/**
 * Format a date in Eastern Time
 * @param {string|Date|Object} dateInput - Date to format
 * @returns {string} Formatted date string with ET timezone
 */
function formatDate(dateInput) {
    if (!dateInput) return 'N/A';


    if (typeof dateInput === 'string' && dateInput.endsWith(' ET')) {
        return dateInput;
    }

    try {
        let date;


        if (typeof dateInput === 'object') {
            if (dateInput.$date) {

                if (typeof dateInput.$date === 'string') {
                    date = new Date(dateInput.$date);
                } else if (dateInput.$date.$numberLong) {
                    date = new Date(parseInt(dateInput.$date.$numberLong));
                } else {
                    date = new Date(dateInput.$date);
                }
            } else if (dateInput instanceof Date) {
                date = dateInput;
            } else {

                date = new Date(String(dateInput));
            }
        } else if (typeof dateInput === 'number') {

            date = new Date(dateInput);
        } else {

            date = new Date(dateInput);
        }


        if (isNaN(date.getTime())) {
            return typeof dateInput === 'string' ? dateInput : 'Invalid Date';
        }


        const options = {
            timeZone: 'America/New_York',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };

        return date.toLocaleString('en-US', options) + ' ET';
    } catch (e) {
        return 'N/A'; // Return N/A for invalid dates
    }
}

