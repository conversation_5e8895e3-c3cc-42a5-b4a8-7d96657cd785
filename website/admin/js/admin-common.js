/**
 * Common admin functionality shared across all admin pages
 */


function checkSession() {
    $.ajax({
        url: '/auth/check_status',  // Updated path to match your PHP file structure
        method: 'GET',
        dataType: 'json',
        cache: false,
        success: function(data) {
            if (!data.authenticated) {
                // Get the reason for authentication failure if available
                const reason = data.reason || 'session_expired';
                let title = 'Session Expired';
                let message = 'Your session has expired. Please log in again.';

                // Customize message based on reason
                if (reason === 'role_verification_failed') {
                    title = 'Access Revoked';
                    message = 'Your Discord roles have changed. You no longer have access to this system.';
                }

                Swal.fire({
                    icon: 'warning',
                    title: title,
                    text: message,
                    confirmButtonText: 'Login Again',
                    allowOutsideClick: false
                }).then(() => {
                    // Clear all cookies
                    document.cookie.split(";").forEach(function(c) {
                        document.cookie = c.replace(/^ +/, "").replace(/=.*/,
                            "=;expires=" + new Date().toUTCString() + ";path=/");
                    });
                    window.location.href = '/login?force_logout=1';
                });
            } else if (data.hasRequiredRole === false) {
                Swal.fire({
                    icon: 'error',
                    title: 'Access Denied',
                    text: 'You do not have the required permissions for this page.',
                    confirmButtonText: 'Go to Dashboard',
                    allowOutsideClick: false
                }).then(() => {
                    window.location.href = '/dashboard';
                });
            }
        },
        error: function(xhr, status, error) {
            // Parse response to get detailed error information
            let errorMessage = 'Please log in again to continue.';
            let errorTitle = 'Authentication Error';

            try {
                const response = JSON.parse(xhr.responseText);
                if (response.message) {
                    errorMessage = response.message;
                }

                if (response.reason === 'role_verification_failed') {
                    errorTitle = 'Access Revoked';
                }
            } catch (e) {
                // Use default message if parsing fails
            }

            if (xhr.status === 401 || xhr.status === 403) {
                Swal.fire({
                    icon: 'warning',
                    title: errorTitle,
                    text: errorMessage,
                    confirmButtonText: 'Login',
                    allowOutsideClick: false
                }).then(() => {
                    window.location.href = '/login';
                });
            }
        }
    });
}


$(document).ready(function() {

    const authData = window.AUTH_DATA || {};


    // Store interval ID for cleanup
    window.sessionCheckInterval = setInterval(checkSession, 5 * 60 * 1000);

    setTimeout(checkSession, 3000);

    // Clean up intervals when page unloads
    window.addEventListener('beforeunload', function() {
        cleanupMemory();
    });

    // Global memory cleanup function
    window.cleanupMemory = function() {
        // Clear all known intervals
        const intervals = [
            'sessionCheckInterval',
            'notificationCheckInterval',
            'dashboardStatsInterval',
            'playerInfoSessionInterval',
            'sidebarNotificationInterval'
        ];

        intervals.forEach(intervalName => {
            if (window[intervalName]) {
                clearInterval(window[intervalName]);
                window[intervalName] = null;
            }
        });

        // Clear large data arrays
        const dataArrays = [
            'allReports',
            'filteredReports',
            'currentTickets',
            'currentFilters'
        ];

        dataArrays.forEach(arrayName => {
            if (window[arrayName]) {
                window[arrayName] = null;
            }
        });

        // Force garbage collection if available
        if (window.gc && typeof window.gc === 'function') {
            try {
                window.gc();
            } catch (e) {
                // Ignore if gc is not available
            }
        }
    };




    initDarkMode();


    initMobileSearch();


    initNotifications();


    initLogoutButtons();


    window.addEventListener('resize', handleResize);


    handleResize();



    let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');


    if (!csrfToken) {

        csrfToken = Math.random().toString(36).substring(2, 15);
    }


    window.CSRF_TOKEN = csrfToken;

    $.ajaxSetup({
        xhrFields: {
            withCredentials: true  // Ensure cookies are sent with requests
        },
        beforeSend: function(xhr) {

            if (window.AUTH_DATA) {
                xhr.setRequestHeader('X-Discord-ID', window.AUTH_DATA.userId || '');
                xhr.setRequestHeader('X-Discord-Username', window.AUTH_DATA.username || '');
                xhr.setRequestHeader('X-Discord-Role', window.AUTH_DATA.role || '');
                xhr.setRequestHeader('X-Discord-Avatar', window.AUTH_DATA.avatar || '');
                xhr.setRequestHeader('X-Session-ID', window.AUTH_DATA.sessionId || '');
            }


            if (csrfToken) {
                xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken);
            }


            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        },
        error: function(xhr, status, error) {

            if (xhr.status === 401) {

            } else if (xhr.status === 403) {

            } else if (xhr.status === 500) {

                try {
                    const response = JSON.parse(xhr.responseText);
                } catch (e) {

                }
            }
        }
    });


    $(document).ajaxError(function(_, jqXHR, settings) {
        if (jqXHR.status === 401 || jqXHR.status === 403) {
            if (!window.authErrorShown) {
                window.authErrorShown = true;

                let errorReason = "Your session has expired.";
                let errorTitle = "Authentication Required";
                let errorIcon = "warning";

                try {
                    const response = JSON.parse(jqXHR.responseText);

                    // Handle specific error reasons
                    if (response.reason === 'role_verification_failed') {
                        errorTitle = "Access Revoked";
                        errorReason = "Your Discord roles have changed. You no longer have access to this system.";
                        errorIcon = "error";
                    } else if (response.reason) {
                        errorReason = `Session error: ${response.reason}`;
                    } else if (response.message) {
                        errorReason = response.message;
                    } else if (response.error) {
                        errorReason = response.error;
                    }
                } catch (e) {
                    // Use default message if parsing fails
                }

                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: errorIcon,
                        title: errorTitle,
                        html: `Please log in again.<br><br><small class="text-muted">${errorReason}</small>`,
                        showCancelButton: true,
                        confirmButtonText: 'Login',
                        cancelButtonText: 'Try Again',
                        allowOutsideClick: false
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/login?force_logout=1';
                        } else {
                            window.location.reload();
                        }
                    });
                } else {
                    if (confirm(`${errorTitle}: ${errorReason}. Please log in again.`)) {
                        window.location.href = '/login?force_logout=1';
                    } else {
                        window.location.reload();
                    }
                }
            }
        }
    });
});


function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    const body = document.body;
    const backdrop = document.querySelector('.sidebar-backdrop');

    sidebar.classList.toggle('active');
    content.classList.toggle('active');
    body.classList.toggle('sidebar-open');


    if (backdrop) {
        if (body.classList.contains('sidebar-open')) {
            backdrop.style.display = 'block';

            setTimeout(() => {
                backdrop.style.opacity = '1';
            }, 10);
        } else {
            backdrop.style.opacity = '0';

            setTimeout(() => {
                backdrop.style.display = 'none';
            }, 300); // Match the transition duration
        }
    }
}


function openMobileSearch() {
    document.getElementById('mobileSearchOverlay').classList.add('active');
    document.querySelector('.mobile-search-input').focus();
    document.body.classList.add('search-open');
}

function closeMobileSearch() {
    document.getElementById('mobileSearchOverlay').classList.remove('active');
    document.body.classList.remove('search-open');
}


function toggleDarkMode() {
    // Always force dark mode - no toggle functionality
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Always save as enabled
    localStorage.setItem('darkMode', 'enabled');
    document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';
}

function initDarkMode() {
    // Always force dark mode regardless of saved preferences
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Always save as enabled
    localStorage.setItem('darkMode', 'enabled');
    document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';
}


function logout() {

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'Logout',
            text: 'Are you sure you want to log out?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, log out'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '/logout';
            }
        });
    } else {

        if (confirm('Are you sure you want to log out?')) {
            window.location.href = '/logout';
        }
    }
}

function formatDate(dateObj) {
    if (!dateObj) return 'N/A';


    const date = parseMongoDate(dateObj);


    if (!date || isNaN(date.getTime())) {
        return String(dateObj); // Return original if invalid
    }

    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}


function handleApiError(xhr, fallbackMessage = 'An error occurred') {
    let errorMsg = fallbackMessage;

    try {
        if (xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg = xhr.responseJSON.error;
        } else if (xhr.responseText) {
            const parsed = JSON.parse(xhr.responseText);
            errorMsg = parsed.error || parsed.message || fallbackMessage;
        }
    } catch (e) {

    }

    return errorMsg;
}

function parseMongoDate(dateObj) {
    if (!dateObj) return null;


    if (dateObj && typeof dateObj === 'object' && dateObj.$date) {
        return new Date(dateObj.$date);
    }


    if (dateObj && typeof dateObj === 'object' && dateObj.milliseconds) {
        return new Date(Number(dateObj.milliseconds));
    }


    if (dateObj instanceof Date) {
        return dateObj;
    }


    if (typeof dateObj === 'string') {

        const date = new Date(dateObj);
        if (!isNaN(date.getTime())) {
            return date;
        }


    }


    if (typeof dateObj === 'number') {
        return new Date(dateObj);
    }

    return null;
}



function initDarkModeSecondary() {
    // Always force dark mode regardless of saved preferences
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Always save as enabled
    localStorage.setItem('darkMode', 'enabled');
    document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';

    // Remove dark mode toggle functionality since we only use dark mode
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.style.display = 'none'; // Hide the toggle
    }

    // Remove dark mode menu item since we only use dark mode
    const darkModeMenuItem = document.querySelector('.dropdown-item[data-action="toggle-dark-mode"]');
    if (darkModeMenuItem) {
        darkModeMenuItem.style.display = 'none'; // Hide the menu item
    }
}


function initLogoutButtons() {

    const logoutLinks = document.querySelectorAll('a[href="#"][onclick="logout()"], a.logout-btn');
    logoutLinks.forEach(link => {

        link.removeAttribute('onclick');


        link.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    });
}


function initMobileSearch() {

    const mobileSearchButton = document.querySelector('.mobile-search-button');
    if (mobileSearchButton) {
        mobileSearchButton.addEventListener('click', openMobileSearch);
    }


    const mobileSearchInput = document.querySelector('.mobile-search-input');
    if (mobileSearchInput) {
        mobileSearchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {

                const searchTerm = this.value.trim();
                if (searchTerm) {




                }
            }
        });
    }
}


function initNotifications() {
    // Store interval ID for cleanup
    window.notificationCheckInterval = setInterval(checkNotifications, 2 * 60 * 1000);


    checkNotifications();


    setupNotificationListeners();
}


function setupNotificationListeners() {

    const notificationsDropdown = document.getElementById('notificationsDropdown');
    if (notificationsDropdown) {
        notificationsDropdown.addEventListener('click', function() {
            loadNotifications();
        });
    }


    const markAllReadBtn = document.getElementById('markAllReadBtn');
    if (markAllReadBtn && !markAllReadBtn.hasAttribute('data-event-attached')) {
        markAllReadBtn.setAttribute('data-event-attached', 'true');
        markAllReadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent dropdown from closing
            markAllNotificationsRead();
        });
    }
}


function checkNotifications() {


    $.ajax({
        url: '/adminapi/notifications/get?unread=true&limit=1&exclude_announcements=true',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                updateNotificationCount(response.unreadCount);
            }
        },
        error: function(xhr) {

        }
    });
}


function loadNotifications() {
    const notificationsContent = document.getElementById('notificationsContent');
    if (!notificationsContent) return;


    notificationsContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-muted small mt-2 mb-0">Loading notifications...</p>
        </div>
    `;


    $.ajax({
        url: '/adminapi/notifications/get?limit=5&exclude_announcements=true',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderNotifications(response.notifications);
            } else {
                notificationsContent.innerHTML = `
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-exclamation-circle mb-2" style="font-size: 1.5rem;"></i>
                        <p class="mb-0">Failed to load notifications</p>
                    </div>
                `;

            }
        },
        error: function(xhr) {
            notificationsContent.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-exclamation-circle mb-2" style="font-size: 1.5rem;"></i>
                    <p class="mb-0">Failed to load notifications</p>
                </div>
            `;

        }
    });
}


function renderNotifications(notifications) {
    const notificationsContent = document.getElementById('notificationsContent');
    if (!notificationsContent) return;

    if (!notifications || notifications.length === 0) {
        notificationsContent.innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-bell-slash mb-2" style="font-size: 1.5rem;"></i>
                <p class="mb-0">No new notifications</p>
            </div>
        `;
        return;
    }

    let html = '';

    notifications.forEach(notification => {
        const isRead = notification.read ? 'read' : 'unread';
        const date = new Date(notification.created_at);
        const timeAgo = formatTimeAgo(date);


        let typeClass = '';
        if (notification.type) {
            typeClass = notification.type;
        }


        if (notification.title && notification.title.toLowerCase().includes('maintenance') ||
            notification.message && notification.message.toLowerCase().includes('maintenance')) {
            typeClass = 'maintenance';
        }

        html += `
            <div class="notification-item ${isRead} ${typeClass}" data-id="${notification.id}">
                <div class="notification-icon ${typeClass}">
                    <i class="fas ${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
                ${!notification.read ? '<button class="btn btn-sm mark-read-btn" data-id="' + notification.id + '" data-source="dropdown"><i class="fas fa-check"></i></button>' : ''}
            </div>
        `;
    });

    notificationsContent.innerHTML = html;


    const markReadButtons = notificationsContent.querySelectorAll('.mark-read-btn');
    markReadButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent dropdown from closing
            const notificationId = this.getAttribute('data-id');
            markNotificationRead(notificationId);
        });
    });


    const notificationItems = notificationsContent.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = this.getAttribute('data-id');

            if (!this.classList.contains('read')) {
                markNotificationRead(notificationId);
            }


            const notification = notifications.find(n => n.id === notificationId);
            if (notification && notification.link) {
                window.location.href = notification.link;
            }
        });
    });
}


function markNotificationRead(notificationId) {
    $.ajax({
        url: '/adminapi/notifications/mark-read?exclude_announcements=true',
        method: 'POST',
        dataType: 'json',
        contentType: 'application/json',
        data: JSON.stringify({
            notification_id: notificationId
        }),
        success: function(response) {
            if (response.success) {

                const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
                if (notificationItem) {
                    notificationItem.classList.add('read');
                    const markReadBtn = notificationItem.querySelector('.mark-read-btn');
                    if (markReadBtn) {
                        markReadBtn.remove();
                    }
                }


                updateNotificationCount(response.unreadCount);
            }
        },
        error: function(xhr) {

        }
    });
}


function markAllNotificationsRead() {
    $.ajax({
        url: '/adminapi/notifications/mark-read?exclude_announcements=true',
        method: 'POST',
        dataType: 'json',
        contentType: 'application/json',
        data: JSON.stringify({}), // Empty object to mark all as read
        success: function(response) {
            if (response.success) {

                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.classList.add('read');
                    const markReadBtn = item.querySelector('.mark-read-btn');
                    if (markReadBtn) {
                        markReadBtn.remove();
                    }
                });


                updateNotificationCount(0);
            }
        },
        error: function(xhr) {

        }
    });
}


function updateNotificationCount(count) {
    const notificationCount = document.getElementById('notificationCount');
    if (notificationCount) {
        notificationCount.textContent = count;


        const badge = notificationCount.closest('.badge');
        if (badge) {
            badge.style.display = count > 0 ? 'block' : 'none';
        }
    }
}


function formatTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
        return diffDay === 1 ? 'Yesterday' : `${diffDay} days ago`;
    } else if (diffHour > 0) {
        return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffMin > 0) {
        return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
    } else {
        return 'Just now';
    }
}


function handleResize() {
    const width = window.innerWidth;


    if (width < 992) {
        document.getElementById('sidebar')?.classList.remove('active');
        document.getElementById('content')?.classList.remove('active');
        document.body.classList.remove('sidebar-open');
    }
}


function copyDiscordId(event) {
    event.preventDefault();
    event.stopPropagation();

    const discordId = document.querySelector('.discord-id-text').textContent.trim();


    const tempInput = document.createElement('input');
    tempInput.value = discordId;
    document.body.appendChild(tempInput);


    tempInput.select();
    document.execCommand('copy');


    document.body.removeChild(tempInput);


    const copyIcon = event.target;
    const originalTitle = copyIcon.getAttribute('title');
    copyIcon.setAttribute('title', 'Copied!');
    copyIcon.classList.add('text-success');


    setTimeout(() => {
        copyIcon.setAttribute('title', originalTitle);
        copyIcon.classList.remove('text-success');
    }, 2000);
}


window.parseMongoDate = parseMongoDate;
window.toggleSidebar = toggleSidebar;
window.logout = logout;
window.formatDate = formatDate;
window.handleApiError = handleApiError;
window.checkSession = checkSession;
window.toggleDarkMode = toggleDarkMode;
window.openMobileSearch = openMobileSearch;
window.closeMobileSearch = closeMobileSearch;
window.copyDiscordId = copyDiscordId;

