/**
 * MassacreMC Admin Portal - Appeals Management
 * Handles the appeals management interface in the admin dashboard
 */

'use strict';


let appealsData = [];
let filteredAppeals = [];
let currentAppealId = null;
let currentPage = 1;
let itemsPerPage = 12;
let currentFilter = '';
let currentSort = 'newest';
let currentSearch = '';


document.addEventListener('DOMContentLoaded', function() {

    loadAppeals();


    setupEventListeners();


    checkUrlForAppealId();
});

/**
 * Set up event listeners for the page
 */
function setupEventListeners() {

    $('#appealsContainer').on('click', '.view-appeal', function() {
        const appealId = $(this).data('id');
        viewAppealDetails(appealId);
    });


    $(document).on('click', function(e) {
        const appealDetails = $('#appealDetails');
        const appealContent = $('#appealContent');


        if (appealDetails.hasClass('active') &&
            !appealContent.is(e.target) &&
            appealContent.has(e.target).length === 0 &&
            !$(e.target).hasClass('view-appeal') &&
            !$(e.target).closest('.view-appeal').length) {


            if ($(e.target).closest('.appeal-actions').length === 0) {
                closeAppealDetails();
            }
        }
    });


    $('#appealContent').on('click', function(e) {
        e.stopPropagation();
    });


    $('#appealContent').on('click', '.btn-approve', function() {
        const appealId = $(this).data('id');
        showApproveConfirmation(appealId);
    });

    $('#appealContent').on('click', '.btn-deny', function() {
        const appealId = $(this).data('id');
        showDenyConfirmation(appealId);
    });


    $('#appealContent').on('click', '.btn-close-details', function() {
        closeAppealDetails();
    });


    $('#refreshAppeals').on('click', function() {
        loadAppeals();
    });


    $('#appealSearch').on('input', function() {
        currentSearch = $(this).val().trim().toLowerCase();
        currentPage = 1;
        filterAndDisplayAppeals();
    });


    $('#statusFilter').on('change', function() {
        currentFilter = $(this).val();
        currentPage = 1;
        filterAndDisplayAppeals();
    });


    $('#sortOrder').on('change', function() {
        currentSort = $(this).val();
        filterAndDisplayAppeals();
    });


    $('#loadMoreBtn').on('click', function() {
        currentPage++;
        displayAppeals();
    });
}

/**
 * Load appeals data from the API
 */
function loadAppeals() {

    $('#appealsContainer').html(`
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading appeals...</p>
        </div>
    `);


    currentPage = 1;


    fetch('/adminapi/appeals.php', {
        method: 'GET',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Discord-ID': window.AUTH_DATA?.userId || '',
            'X-Discord-Username': window.AUTH_DATA?.username || '',
            'X-Discord-Role': window.AUTH_DATA?.role || '',
            'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || `HTTP error! Status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(appeals => {

        appealsData = appeals || [];


        filterAndDisplayAppeals();
    })
    .catch(error => {

        $('#appealsContainer').html(`
            <div class="col-12 text-center py-5 text-danger">
                <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                <h5>Error Loading Appeals</h5>
                <p>${error.message}</p>
                <button id="retryLoad" class="btn btn-outline-primary mt-3">
                    <i class="fas fa-sync-alt me-2"></i> Try Again
                </button>
            </div>
        `);


        $('#retryLoad').on('click', function() {
            loadAppeals();
        });


        Swal.fire({
            icon: 'error',
            title: 'Error Loading Appeals',
            text: error.message,
            confirmButtonText: 'Try Again',
            showCancelButton: true,
            cancelButtonText: 'Dismiss'
        }).then((result) => {
            if (result.isConfirmed) {
                loadAppeals();
            }
        });
    });
}

/**
 * Filter and display appeals based on current filters
 */
function filterAndDisplayAppeals() {

    filteredAppeals = appealsData.filter(appeal => {

        const searchMatch = !currentSearch ||
            appeal.player_name.toLowerCase().includes(currentSearch) ||
            appeal.id.toLowerCase().includes(currentSearch) ||
            appeal.punishment_id.toLowerCase().includes(currentSearch);


        const statusMatch = !currentFilter || appeal.status === currentFilter;

        return searchMatch && statusMatch;
    });


    sortAppeals();


    displayAppeals();


    updateAppealCounts();
}

/**
 * Sort appeals based on current sort option
 */
function sortAppeals() {
    filteredAppeals.sort((a, b) => {
        switch (currentSort) {
            case 'newest':
                return getTimestamp(b.created_at) - getTimestamp(a.created_at);
            case 'oldest':
                return getTimestamp(a.created_at) - getTimestamp(b.created_at);
            case 'player_asc':
                return a.player_name.localeCompare(b.player_name);
            case 'player_desc':
                return b.player_name.localeCompare(a.player_name);
            default:
                return getTimestamp(b.created_at) - getTimestamp(a.created_at);
        }
    });
}

/**
 * Get timestamp from various date formats
 * @param {Object|string} dateObj - The date object or string
 * @returns {number} - Timestamp in milliseconds
 */
function getTimestamp(dateObj) {
    if (!dateObj) return 0;

    if (dateObj.$date && dateObj.$date.$numberLong) {
        return parseInt(dateObj.$date.$numberLong);
    } else if (dateObj.$date) {
        return dateObj.$date;
    } else if (typeof dateObj === 'string') {
        return new Date(dateObj).getTime();
    } else if (dateObj instanceof Date) {
        return dateObj.getTime();
    }

    return 0;
}

/**
 * Display appeals with pagination
 */
function displayAppeals() {

    const startIndex = 0;
    const endIndex = currentPage * itemsPerPage;
    const appealsToShow = filteredAppeals.slice(startIndex, endIndex);
    const hasMoreAppeals = filteredAppeals.length > endIndex;


    let html = '';

    if (appealsToShow.length === 0) {
        html = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No appeals found</h5>
                <p class="text-muted">Try adjusting your search or filters</p>
            </div>
        `;
    } else {

        appealsToShow.forEach(appeal => {
            html += createAppealCard(appeal);
        });
    }


    $('#appealsContainer').html(html);


    $('#loadMoreBtn').toggle(hasMoreAppeals);
    $('#showingCount').text(appealsToShow.length);
}

/**
 * Create HTML for an appeal card
 * @param {Object} appeal - The appeal data
 * @returns {string} - HTML for the appeal card
 */
function createAppealCard(appeal) {

    let createdDate = 'Unknown date';

    if (appeal.created_at_formatted) {
        const date = new Date(appeal.created_at_formatted);
        createdDate = formatDate(date);
    } else if (appeal.created_at && appeal.created_at.$date) {
        const timestamp = appeal.created_at.$date.$numberLong
            ? parseInt(appeal.created_at.$date.$numberLong)
            : appeal.created_at.$date;
        createdDate = formatDate(new Date(timestamp));
    } else if (appeal.created_at) {
        createdDate = formatDate(appeal.created_at);
    }


    let statusClass = 'bg-secondary';
    let statusText = appeal.status || 'Unknown';

    if (statusText === 'Pending') {
        statusClass = 'bg-warning text-dark';
    } else if (statusText === 'Approved') {
        statusClass = 'bg-success';
    } else if (statusText === 'Denied') {
        statusClass = 'bg-danger';
    }


    return `
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 appeal-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="badge ${statusClass}">${statusText}</span>
                    <small class="text-muted">${createdDate}</small>
                </div>
                <div class="card-body">
                    <h5 class="card-title">${escapeHtml(appeal.player_name)}</h5>
                    <div class="mb-3">
                        <small class="text-muted">Appeal ID:</small>
                        <code class="ms-2">${appeal.id}</code>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Punishment ID:</small>
                        <code class="ms-2">${appeal.punishment_id}</code>
                    </div>
                    ${appeal.punishment_type ? `
                    <div class="mb-3">
                        <small class="text-muted">Type:</small>
                        <span class="ms-2">${escapeHtml(appeal.punishment_type)}</span>
                    </div>
                    ` : ''}
                </div>
                <div class="card-footer bg-transparent">
                    <button class="btn btn-primary btn-sm w-100 view-appeal" data-id="${appeal.id}">
                        <i class="fas fa-eye me-2"></i> View Details
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Update the appeal counts in the UI
 */
function updateAppealCounts() {

    let pendingCount = 0;
    appealsData.forEach(appeal => {
        if (appeal.status === 'Pending') {
            pendingCount++;
        }
    });


    $('#totalCount').text(appealsData.length);
    $('#pendingCount').text(pendingCount);
}

/**
 * View appeal details
 * @param {string} appealId - The ID of the appeal to view
 */
function viewAppealDetails(appealId) {

    currentAppealId = appealId;


    const appealData = appealsData.find(appeal => appeal.id === appealId);

    if (!appealData) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Appeal not found',
            confirmButtonText: 'OK'
        });
        return;
    }


    let createdDate = 'Unknown date';


    if (appealData.created_at_formatted) {

        const date = new Date(appealData.created_at_formatted);
        createdDate = formatDate(date);
    } else if (appealData.created_at && appealData.created_at.$date) {

        const timestamp = appealData.created_at.$date.$numberLong
            ? parseInt(appealData.created_at.$date.$numberLong)
            : appealData.created_at.$date;
        createdDate = formatDate(new Date(timestamp));
    } else if (appealData.created_at) {
        createdDate = formatDate(appealData.created_at);
    }


    let statusClass, statusText, statusBgClass, statusIcon;

    if (appealData.status === 'Pending') {
        statusClass = 'text-warning';
        statusBgClass = 'bg-warning-subtle';
        statusText = 'Pending Review';
        statusIcon = 'fa-clock';
    } else if (appealData.status === 'Approved') {
        statusClass = 'text-success';
        statusBgClass = 'bg-success-subtle';
        statusText = 'Approved';
        statusIcon = 'fa-check-circle';
    } else if (appealData.status === 'Denied') {
        statusClass = 'text-danger';
        statusBgClass = 'bg-danger-subtle';
        statusText = 'Denied';
        statusIcon = 'fa-times-circle';
    } else {
        statusClass = 'text-secondary';
        statusBgClass = 'bg-secondary-subtle';
        statusText = appealData.status || 'Unknown';
        statusIcon = 'fa-question-circle';
    }


    let resolvedDate = 'Unknown';
    let resolvedBy = 'Unknown';

    if (appealData.status !== 'Pending') {

        if (appealData.processed_by_name) {
            resolvedBy = appealData.processed_by_name;
        } else if (appealData.resolved_by) {
            resolvedBy = appealData.resolved_by;
        }


        if (appealData.processed_at_et) {

            resolvedDate = appealData.processed_at_et;
        } else if (appealData.resolved_at_et) {
            resolvedDate = appealData.resolved_at_et;
        } else if (appealData.processed_at_formatted) {
            const date = new Date(appealData.processed_at_formatted);
            resolvedDate = formatDate(date);
        } else if (appealData.resolved_at_formatted) {
            const date = new Date(appealData.resolved_at_formatted);
            resolvedDate = formatDate(date);
        } else if (appealData.processed_at && appealData.processed_at.$date) {

            const timestamp = appealData.processed_at.$date.$numberLong
                ? parseInt(appealData.processed_at.$date.$numberLong)
                : appealData.processed_at.$date;
            resolvedDate = formatDate(new Date(timestamp));
        } else if (appealData.resolved_at && appealData.resolved_at.$date) {

            const timestamp = appealData.resolved_at.$date.$numberLong
                ? parseInt(appealData.resolved_at.$date.$numberLong)
                : appealData.resolved_at.$date;
            resolvedDate = formatDate(new Date(timestamp));
        } else if (appealData.processed_at) {
            resolvedDate = formatDate(appealData.processed_at);
        } else if (appealData.resolved_at) {
            resolvedDate = formatDate(appealData.resolved_at);
        }


        if ((resolvedBy === 'Unknown' || resolvedDate === 'Unknown') && appealData.history && Array.isArray(appealData.history)) {

            const resolutionAction = appealData.history.find(h =>
                h.action === 'approved' || h.action === 'denied' ||
                h.action === 'approve' || h.action === 'deny' ||
                h.action === 'denyed'); // Include misspelled version

            if (resolutionAction) {
                if (resolutionAction.staff_name && resolvedBy === 'Unknown') {
                    resolvedBy = resolutionAction.staff_name;
                }

                if (resolutionAction.timestamp && resolvedDate === 'Unknown') {
                    if (resolutionAction.timestamp.$date) {
                        const timestamp = resolutionAction.timestamp.$date.$numberLong
                            ? parseInt(resolutionAction.timestamp.$date.$numberLong)
                            : resolutionAction.timestamp.$date;
                        resolvedDate = formatDate(new Date(timestamp));
                    } else {
                        resolvedDate = formatDate(new Date(resolutionAction.timestamp));
                    }
                }
            }
        }
    }


    let detailsHtml = `
        <div class="report-modal">
            <!-- Header with close button and status badge -->
            <div class="appeal-header p-3 border-bottom d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="appeal-icon-container me-3 ${statusClass}">
                        <i class="fas fa-gavel"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">${escapeHtml(appealData.player_name)}</h5>
                        <div class="d-flex align-items-center mt-1">
                            <span class="badge ${statusClass} bg-opacity-10">${statusText}</span>
                            <span class="ms-2 small text-muted">${appealData.id}</span>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-details" aria-label="Close"></button>
            </div>

            <!-- Main content -->
            <div class="p-3">
                <!-- Quick info cards -->
                <div class="appeal-quick-info mb-4">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="quick-info-card ${statusBgClass}">
                                <div class="quick-info-icon">
                                    <i class="fas ${statusIcon}"></i>
                                </div>
                                <div class="quick-info-content">
                                    <div class="quick-info-label">Status</div>
                                    <div class="quick-info-value">${statusText}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="quick-info-card bg-info-subtle">
                                <div class="quick-info-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="quick-info-content">
                                    <div class="quick-info-label">Submitted</div>
                                    <div class="quick-info-value">${createdDate}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Punishment information section -->
                <div class="report-section">
                    <div class="report-section-title">
                        <i class="fas fa-ban"></i>
                        <span>Punishment Details</span>
                    </div>

                    <div class="punishment-card">
                        <div class="punishment-header">
                            <div class="punishment-id">
                                <i class="fas fa-hashtag me-2"></i>
                                <code>${appealData.punishment_id}</code>
                                ${appealData.status === 'Approved' ?
                                    '<span class="badge bg-success ms-2">Punishment Removed</span>' : ''}
                            </div>
                            ${appealData.punishment_type ? `
                            <div class="punishment-type">
                                <span class="badge ${getPunishmentBadgeClass(appealData.punishment_type)}">
                                    ${formatPunishmentType(appealData.punishment_type)}
                                </span>
                            </div>
                            ` : ''}
                        </div>

                        <div class="punishment-details-content">
                            ${appealData.offense_type ? `
                            <div class="punishment-detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Offense Type
                                </div>
                                <div class="detail-value">
                                    ${formatOffenseType(appealData.offense_type)}
                                </div>
                            </div>
                            ` : ''}

                            ${appealData.punishment_reason ? `
                            <div class="punishment-detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-comment-alt me-2"></i>
                                    Reason
                                </div>
                                <div class="detail-value">
                                    ${escapeHtml(appealData.punishment_reason)}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <!-- Appeal reason section -->
                <div class="report-section">
                    <div class="report-section-title">
                        <i class="fas fa-comment-dots"></i>
                        <span>Appeal Reason</span>
                    </div>

                    <div class="appeal-reason-box">
                        <div class="appeal-reason-content">
                            ${escapeHtml(appealData.reason) || 'No reason provided'}
                        </div>
                    </div>
                </div>

                <!-- Resolution section for non-pending appeals -->
                ${appealData.status !== 'Pending' ? `
                <div class="report-section">
                    <div class="report-section-title">
                        <i class="fas fa-clipboard-check"></i>
                        <span>Resolution</span>
                    </div>

                    <div class="resolution-card ${appealData.status === 'Approved' ? 'resolution-approved' : 'resolution-denied'}">
                        <div class="resolution-header">
                            <div class="resolution-status">
                                <i class="fas ${appealData.status === 'Approved' ? 'fa-check-circle' : 'fa-times-circle'} me-2"></i>
                                <span>${appealData.status === 'Approved' ? 'Appeal Approved' : 'Appeal Denied'}</span>
                            </div>
                        </div>

                        <div class="resolution-details">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="resolution-detail-item">
                                        <div class="detail-label">
                                            <i class="fas fa-user-shield me-2"></i>
                                            Resolved By
                                        </div>
                                        <div class="detail-value">
                                            ${resolvedBy}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="resolution-detail-item">
                                        <div class="detail-label">
                                            <i class="fas fa-clock me-2"></i>
                                            Resolved On
                                        </div>
                                        <div class="detail-value">
                                            ${resolvedDate}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${appealData.staff_notes ? `
                            <div class="staff-notes mt-3">
                                <div class="staff-notes-label">
                                    <i class="fas fa-sticky-note me-2"></i>
                                    Staff Notes
                                </div>
                                <div class="staff-notes-content">
                                    ${escapeHtml(appealData.staff_notes)}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                ` : ''}

                <!-- Action buttons for pending appeals -->
                ${appealData.status === 'Pending' ? `
                <div class="appeal-actions">
                    <button class="btn btn-success btn-approve" data-id="${appealData.id}">
                        <i class="fas fa-check me-2"></i> Approve Appeal
                    </button>
                    <button class="btn btn-danger btn-deny" data-id="${appealData.id}">
                        <i class="fas fa-times me-2"></i> Deny Appeal
                    </button>
                </div>
                ` : ''}

                <!-- History section if available -->
                ${appealData.history && appealData.history.length > 0 ? `
                <div class="report-section">
                    <div class="report-section-title">
                        <i class="fas fa-history"></i>
                        <span>Appeal History</span>
                    </div>

                    <div class="timeline">
                        ${appealData.history.map((item, index) => {
                            let timestamp = 'Unknown';
                            if (item.timestamp && item.timestamp.$date) {
                                const date = new Date(parseInt(item.timestamp.$date.$numberLong || item.timestamp.$date));
                                timestamp = formatDate(date);
                            } else if (item.timestamp) {
                                timestamp = formatDate(new Date(item.timestamp));
                            }


                            const markerColorClass = getHistoryColor(item.action);


                            let bgColorClass = '';
                            if (item.action === 'approved' || item.action === 'approve' ||
                                item.action === 'punishment_removed' || item.action === 'punishment_deleted' ||
                                item.action === 'ban_removed' || item.action === 'mute_removed' || item.action === 'warning_removed') {
                                bgColorClass = 'bg-success-subtle';
                            } else if (item.action === 'denied' || item.action === 'deny' || item.action === 'denyed') {
                                bgColorClass = 'bg-danger-subtle';
                            } else if (item.action === 'notification_failed' || item.action === 'email_failed' || item.action === 'escalated') {
                                bgColorClass = 'bg-warning-subtle';
                            } else if (item.action === 'reviewed' || item.action === 'under_review' || item.action === 'staff_note' || item.action === 'note_added') {
                                bgColorClass = 'bg-primary-subtle';
                            }

                            return `
                            <div class="timeline-item ${index === appealData.history.length - 1 ? 'timeline-item-last' : ''} ${bgColorClass}">
                                <div class="timeline-marker ${markerColorClass}">
                                    <i class="fas ${getHistoryIcon(item.action)}"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-title">${formatHistoryAction(item.action)}</span>
                                        <span class="timeline-date">${timestamp}</span>
                                    </div>
                                    ${item.details ? `
                                    <div class="timeline-body">
                                        ${escapeHtml(item.details)}
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                            `;
                        }).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;


    $('#appealContent').html(detailsHtml);
    $('#appealDetails').addClass('active');


    updateUrlWithAppealId(appealId);
}

/**
 * Get appropriate icon for history action
 * @param {string} action - The history action
 * @returns {string} - Font Awesome icon class
 */
function getHistoryIcon(action) {
    switch (action) {
        case 'created':
            return 'fa-plus-circle';
        case 'approved':
        case 'approve':
            return 'fa-check-circle';
        case 'denied':
        case 'deny':
        case 'denyed': // Handle the misspelled version
            return 'fa-times-circle';
        case 'updated':
            return 'fa-edit';
        case 'notification':
        case 'email_sent':
        case 'notification_sent':
            return 'fa-envelope';
        case 'notification_failed':
        case 'email_failed':
            return 'fa-envelope-open';
        case 'punishment_removed':
        case 'punishment_deleted':
        case 'ban_removed':
        case 'mute_removed':
        case 'warning_removed':
            return 'fa-trash-alt';
        case 'reviewed':
        case 'under_review':
            return 'fa-eye';
        case 'escalated':
            return 'fa-arrow-up';
        case 'reopened':
            return 'fa-redo';
        case 'closed':
            return 'fa-lock';
        case 'staff_note':
        case 'note_added':
            return 'fa-sticky-note';
        case 'submitted':
        case 'submit':
            return 'fa-paper-plane';
        case 'processed':
        case 'processing':
            return 'fa-cogs';
        case 'email_notification_sent':
            return 'fa-envelope';
        case 'email_notification_failed':
            return 'fa-envelope-open';
        default:
            return 'fa-circle';
    }
}

/**
 * Get appropriate color class for history action
 * @param {string} action - The history action
 * @returns {string} - CSS color class
 */
function getHistoryColor(action) {
    switch (action) {
        case 'created':
            return 'text-info';
        case 'approved':
        case 'approve':
            return 'text-success';
        case 'denied':
        case 'deny':
        case 'denyed': // Handle the misspelled version
            return 'text-danger';
        case 'updated':
            return 'text-primary';
        case 'notification':
        case 'email_sent':
        case 'notification_sent':
            return 'text-info';
        case 'notification_failed':
        case 'email_failed':
            return 'text-warning';
        case 'punishment_removed':
        case 'punishment_deleted':
        case 'ban_removed':
        case 'mute_removed':
        case 'warning_removed':
            return 'text-success';
        case 'reviewed':
        case 'under_review':
            return 'text-primary';
        case 'escalated':
            return 'text-warning';
        case 'reopened':
            return 'text-info';
        case 'closed':
            return 'text-secondary';
        case 'staff_note':
        case 'note_added':
            return 'text-primary';
        case 'submitted':
        case 'submit':
            return 'text-info';
        case 'processed':
        case 'processing':
            return 'text-primary';
        case 'email_notification_sent':
        case 'email_notification_failed':
            return 'text-warning';
        default:
            return 'text-secondary';
    }
}

/**
 * Format history action for display
 * @param {string} action - The history action
 * @returns {string} - Formatted action text
 */
function formatHistoryAction(action) {
    switch (action) {
        case 'created':
            return 'Appeal Created';
        case 'approved':
        case 'approve':
            return 'Appeal Approved';
        case 'denied':
        case 'deny':
        case 'denyed': // Handle the misspelled version
            return 'Appeal Denied';
        case 'updated':
            return 'Appeal Updated';
        case 'notification':
        case 'email_sent':
        case 'notification_sent':
            return 'Email Notification Sent';
        case 'notification_failed':
        case 'email_failed':
            return 'Email Notification Failed';
        case 'punishment_removed':
        case 'punishment_deleted':
        case 'ban_removed':
        case 'mute_removed':
        case 'warning_removed':
            return 'Punishment Removed';
        case 'reviewed':
            return 'Appeal Reviewed';
        case 'under_review':
            return 'Under Review';
        case 'escalated':
            return 'Appeal Escalated';
        case 'reopened':
            return 'Appeal Reopened';
        case 'closed':
            return 'Appeal Closed';
        case 'staff_note':
        case 'note_added':
            return 'Staff Note Added';
        case 'submitted':
        case 'submit':
            return 'Appeal Submitted';
        case 'processed':
        case 'processing':
            return 'Appeal Processed';
        case 'email_notification_sent':
            return 'Email Notification Sent';
        case 'email_notification_failed':
            return 'Email Notification Failed';
        default:
            return action.charAt(0).toUpperCase() + action.slice(1).replace(/_/g, ' ');
    }
}

/**
 * Format offense type for display
 * @param {string} offenseType - The offense type from the database
 * @returns {string} - Formatted offense type text
 */
function formatOffenseType(offenseType) {
    if (!offenseType) return '';


    switch (offenseType.toLowerCase()) {
        case 'hacking_cheating':
            return 'Hacking/Cheating';
        case 'kill_farming':
                return 'Kill Farming';
        case 'punishment_evading':
                return 'Punishment Evading';
        case 'inappropriate_content':
            return 'Inappropriate Content';
        case 'inappropriate_language':
            return 'Inappropriate Language';
        case 'griefing':
            return 'Griefing';
        case 'spam':
            return 'Spam';
        case 'hate_speech':
            return 'Hate Speech';
        case 'advertising':
            return 'Advertising';
        case 'impersonation':
            return 'Impersonation';
        case 'ban_evasion':
            return 'Ban Evasion';
        case 'exploiting':
            return 'Exploiting';
        case 'harassment':
            return 'Harassment';
        case 'threats':
            return 'Threats';
        case 'inappropriate_skin':
            return 'Inappropriate Skin';
        case 'impersonating_staff':
            return 'Impersonating Staff';
        case 'inappropriate_username':
            return 'Inappropriate Username';
        case 'doxxing':
            return 'Doxxing';
        default:

            return offenseType
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
    }
}

/**
 * Format punishment type for display
 * @param {string} punishmentType - The punishment type from the database
 * @returns {string} - Formatted punishment type text
 */
function formatPunishmentType(punishmentType) {
    if (!punishmentType) return '';

    switch (punishmentType.toLowerCase()) {
        case 'ban':
            return 'Ban';
        case 'temp_ban':
        case 'tempban':
            return 'Temporary Ban';
        case 'mute':
            return 'Mute';
        case 'temp_mute':
        case 'tempmute':
            return 'Temporary Mute';
        case 'kick':
            return 'Kick';
        case 'warn':
            return 'Warning';
        default:

            return punishmentType
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
    }
}

/**
 * Get appropriate badge class for punishment type
 * @param {string} punishmentType - The punishment type
 * @returns {string} - Bootstrap badge class
 */
function getPunishmentBadgeClass(punishmentType) {
    if (!punishmentType) return 'bg-secondary';

    switch (punishmentType.toLowerCase()) {
        case 'ban':
            return 'bg-danger';
        case 'temp_ban':
        case 'tempban':
            return 'bg-warning text-dark';
        case 'mute':
            return 'bg-info text-dark';
        case 'temp_mute':
        case 'tempmute':
            return 'bg-info text-dark';
        case 'kick':
            return 'bg-primary';
        case 'warn':
            return 'bg-secondary';
        default:
            return 'bg-secondary';
    }
}

/**
 * Close the appeal details sidebar
 */
function closeAppealDetails() {
    $('#appealDetails').removeClass('active');
    currentAppealId = null;


    updateUrlWithAppealId(null);
}

/**
 * Show confirmation dialog for approving an appeal
 * @param {string} appealId - The ID of the appeal to approve
 */
function showApproveConfirmation(appealId) {
    Swal.fire({
        title: 'Approve Appeal',
        html: `
            <p>Are you sure you want to approve this appeal?</p>
            <p class="text-success"><i class="fas fa-info-circle me-2"></i>This will <strong>remove</strong> the punishment.</p>
            <div class="form-group mt-3">
                <label for="staff-notes" class="form-label">Staff Notes (Optional)</label>
                <textarea id="staff-notes" class="form-control" rows="3" placeholder="Add any notes about this decision..."></textarea>
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Approve Appeal',
        cancelButtonText: 'Cancel',
        focusConfirm: false,
        preConfirm: () => {
            return document.getElementById('staff-notes').value;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            approveAppeal(appealId, result.value);
        }
    });
}

/**
 * Show confirmation dialog for denying an appeal
 * @param {string} appealId - The ID of the appeal to deny
 */
function showDenyConfirmation(appealId) {
    Swal.fire({
        title: 'Deny Appeal',
        html: `
            <p>Are you sure you want to deny this appeal?</p>
            <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>The punishment will remain in effect.</p>
            <div class="form-group mt-3">
                <label for="staff-notes" class="form-label">Staff Notes (Optional)</label>
                <textarea id="staff-notes" class="form-control" rows="3" placeholder="Add any notes about this decision..."></textarea>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Deny Appeal',
        cancelButtonText: 'Cancel',
        focusConfirm: false,
        preConfirm: () => {
            return document.getElementById('staff-notes').value;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            denyAppeal(appealId, result.value);
        }
    });
}

/**
 * Approve an appeal
 * @param {string} appealId - The ID of the appeal to approve
 * @param {string} staffNotes - Optional notes from staff
 */
function approveAppeal(appealId, staffNotes) {

    Swal.fire({
        title: 'Processing...',
        html: 'Approving appeal, please wait...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });


    fetch(`/adminapi/appeals/${appealId}/approve`, {
        method: 'POST',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Discord-ID': window.AUTH_DATA?.userId || '',
            'X-Discord-Username': window.AUTH_DATA?.username || '',
            'X-Discord-Role': window.AUTH_DATA?.role || '',
            'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            staff_notes: staffNotes
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || `HTTP error! Status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {

        Swal.fire({
            icon: 'success',
            title: 'Appeal Approved',
            text: data.message || 'The appeal has been approved successfully. The punishment has been removed.',
            confirmButtonText: 'OK'
        });


        loadAppeals();


        closeAppealDetails();
    })
    .catch(error => {


        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.message || 'An error occurred while approving the appeal.',
            confirmButtonText: 'OK'
        });
    });
}

/**
 * Deny an appeal
 * @param {string} appealId - The ID of the appeal to deny
 * @param {string} staffNotes - Optional notes from staff
 */
function denyAppeal(appealId, staffNotes) {

    Swal.fire({
        title: 'Processing...',
        html: 'Denying appeal, please wait...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });


    fetch(`/adminapi/appeals/${appealId}/deny`, {
        method: 'POST',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Discord-ID': window.AUTH_DATA?.userId || '',
            'X-Discord-Username': window.AUTH_DATA?.username || '',
            'X-Discord-Role': window.AUTH_DATA?.role || '',
            'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            staff_notes: staffNotes
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || `HTTP error! Status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {

        Swal.fire({
            icon: 'success',
            title: 'Appeal Denied',
            text: data.message || 'The appeal has been denied successfully.',
            confirmButtonText: 'OK'
        });


        loadAppeals();


        closeAppealDetails();
    })
    .catch(error => {


        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.message || 'An error occurred while denying the appeal.',
            confirmButtonText: 'OK'
        });
    });
}

/**
 * Update the URL with the appeal ID for direct linking
 * @param {string|null} appealId - The ID of the appeal, or null to remove
 */
function updateUrlWithAppealId(appealId) {

    const url = new URL(window.location.href);

    if (appealId) {

        url.searchParams.set('appeal', appealId);
    } else {

        url.searchParams.delete('appeal');
    }


    window.history.replaceState({}, '', url);
}

/**
 * Check if there's an appeal ID in the URL and open it
 */
function checkUrlForAppealId() {
    const urlParams = new URLSearchParams(window.location.search);
    const appealId = urlParams.get('appeal');

    if (appealId) {

        const checkDataReady = setInterval(() => {
            if (appealsData && appealsData.length > 0) {
                clearInterval(checkDataReady);


                const appealData = appealsData.find(appeal => appeal.id === appealId);

                if (appealData) {

                    viewAppealDetails(appealId);
                } else {

                    updateUrlWithAppealId(null);
                }
            }
        }, 100);


        setTimeout(() => {
            clearInterval(checkDataReady);
        }, 5000);
    }
}

/**
 * Escape HTML to prevent XSS
 * @param {string} unsafe - The unsafe string to escape
 * @returns {string} - The escaped string
 */
function escapeHtml(unsafe) {
    if (!unsafe) return '';

    return unsafe
        .toString()
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

/**
 * Format a date for display in Eastern Time
 * @param {Date} date - The date to format
 * @returns {string} - The formatted date string in Eastern Time
 */
function formatDate(date) {
    if (!date || isNaN(date.getTime())) {
        return 'Unknown';
    }

    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: 'America/New_York'
    };

    return date.toLocaleDateString('en-US', options) + ' ET';
}
