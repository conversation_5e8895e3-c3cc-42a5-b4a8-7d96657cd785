<?php
/**
 * API endpoint to get Discord account info for a player
 * Admin only functionality
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Require authentication and minimum role (Moderator+ can view linked accounts)
require_auth(ROLE_MODERATOR);

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $username = $_GET['username'] ?? '';

    if (empty($username)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Username is required']);
        exit;
    }

    // Sanitize and validate username
    $username = trim($username);
    if (strlen($username) > 50 || !preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid username format']);
        exit;
    }
    
    // Get Discord account info
    $discordAccount = get_player_discord_account($username);
    
    if ($discordAccount) {
        echo json_encode([
            'success' => true,
            'data' => [
                'has_discord_account' => true,
                'discord_id' => $discordAccount['discord_id'],
                'discord_username' => $discordAccount['discord_username'],
                'discord_email' => $discordAccount['discord_email'],
                'xbox_username' => $discordAccount['xbox_username'],
                'linked_at' => $discordAccount['linked_at'] ? $discordAccount['linked_at']->toDateTime()->format('Y-m-d H:i:s') : null
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'data' => [
                'has_discord_account' => false
            ]
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error in get-discord-account.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
