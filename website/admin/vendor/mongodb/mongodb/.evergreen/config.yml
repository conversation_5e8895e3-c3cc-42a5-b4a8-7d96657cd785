########################################
# Evergreen Template for MongoDB Drivers
########################################

# When a task that used to pass starts to fail
# Go through all versions that may have been skipped to detect
# when the task started failing
stepback: true

# Mark a failure as a system/bootstrap failure (purple box) rather then a task
# failure by default.
# Actual testing tasks are marked with `type: test`
command_type: system

# Protect ourself against rogue test case, or curl gone wild, that runs forever
# Good rule of thumb: the averageish length a task takes, times 5
# That roughly accounts for variable system performance for various buildvariants
exec_timeout_secs: 1800 # 6 minutes is the longest we'll ever run

# What to do when evergreen hits the timeout (`post:` tasks are run automatically)
timeout:
  - command: shell.exec
    params:
      script: |
        ls -la

functions:
  "fetch source":
    # Executes git clone and applies the submitted patch, if any
    - command: git.get_project
      params:
        directory: "src"
    # Make an evergreen exapanstion file with dynamic values
    - command: shell.exec
      params:
        working_dir: "src"
        script: |
           # Get the current unique version of this checkout
           if [ "${is_patch}" = "true" ]; then
              CURRENT_VERSION=$(git describe)-patch-${version_id}
           else
              CURRENT_VERSION=latest
           fi

           export DRIVERS_TOOLS="$(pwd)/../drivers-tools"
           export PROJECT_DIRECTORY="$(pwd)"

           # Python has cygwin path problems on Windows. Detect prospective mongo-orchestration home directory
           if [ "Windows_NT" = "$OS" ]; then # Magic variable in cygwin
              export DRIVERS_TOOLS=$(cygpath -m $DRIVERS_TOOLS)
              export PROJECT_DIRECTORY=$(cygpath -m $PROJECT_DIRECTORY)
           fi

           export MONGO_ORCHESTRATION_HOME="$DRIVERS_TOOLS/.evergreen/orchestration"
           export MONGODB_BINARIES="$DRIVERS_TOOLS/mongodb/bin"
           export UPLOAD_BUCKET="${project}"

           cat <<EOT > expansion.yml
           CURRENT_VERSION: "$CURRENT_VERSION"
           DRIVERS_TOOLS: "$DRIVERS_TOOLS"
           MONGO_ORCHESTRATION_HOME: "$MONGO_ORCHESTRATION_HOME"
           MONGODB_BINARIES: "$MONGODB_BINARIES"
           UPLOAD_BUCKET: "$UPLOAD_BUCKET"
           PROJECT_DIRECTORY: "$PROJECT_DIRECTORY"
           PREPARE_SHELL: |
              set -o errexit
              export DRIVERS_TOOLS="$DRIVERS_TOOLS"
              export MONGO_ORCHESTRATION_HOME="$MONGO_ORCHESTRATION_HOME"
              export MONGODB_BINARIES="$MONGODB_BINARIES"
              export UPLOAD_BUCKET="$UPLOAD_BUCKET"
              export PROJECT_DIRECTORY="$PROJECT_DIRECTORY"

              export TMPDIR="$MONGO_ORCHESTRATION_HOME/db"
              export PATH="$MONGODB_BINARIES:$PATH"
              export PROJECT="${project}"
           EOT
           # See what we've done
           cat expansion.yml

    # Load the expansion file to make an evergreen variable with the current unique version
    - command: expansions.update
      params:
        file: src/expansion.yml

  "prepare resources":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          rm -rf $DRIVERS_TOOLS
          if [ "${project}" = "drivers-tools" ]; then
            # If this was a patch build, doing a fresh clone would not actually test the patch
            cp -R ${PROJECT_DIRECTORY}/ $DRIVERS_TOOLS
          else
            git clone https://github.com/mongodb-labs/drivers-evergreen-tools.git --depth 1 $DRIVERS_TOOLS
          fi
          echo "{ \"releases\": { \"default\": \"$MONGODB_BINARIES\" }}" > $MONGO_ORCHESTRATION_HOME/orchestration.config

  "upload mo artifacts":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          find $MONGO_ORCHESTRATION_HOME -name \*.log | xargs tar czf mongodb-logs.tar.gz
    - command: s3.put
      params:
        aws_key: ${aws_key}
        aws_secret: ${aws_secret}
        local_file: mongodb-logs.tar.gz
        remote_file: ${UPLOAD_BUCKET}/${build_variant}/${revision}/${version_id}/${build_id}/logs/${task_id}-${execution}-mongodb-logs.tar.gz
        bucket: mciuploads
        permissions: public-read
        content_type: ${content_type|application/x-gzip}
        display_name: "mongodb-logs.tar.gz"
    - command: s3.put
      params:
        aws_key: ${aws_key}
        aws_secret: ${aws_secret}
        local_file: ${DRIVERS_TOOLS}/.evergreen/orchestration/server.log
        remote_file: ${UPLOAD_BUCKET}/${build_variant}/${revision}/${version_id}/${build_id}/logs/${task_id}-${execution}-orchestration.log
        bucket: mciuploads
        permissions: public-read
        content_type: ${content_type|text/plain}
        display_name: "orchestration.log"

  "upload working dir":
    - command: archive.targz_pack
      params:
        target: "working-dir.tar.gz"
        source_dir: ${PROJECT_DIRECTORY}/
        include:
          - "./**"
    - command: s3.put
      params:
        aws_key: ${aws_key}
        aws_secret: ${aws_secret}
        local_file: working-dir.tar.gz
        remote_file: ${UPLOAD_BUCKET}/${build_variant}/${revision}/${version_id}/${build_id}/artifacts/${task_id}-${execution}-working-dir.tar.gz
        bucket: mciuploads
        permissions: public-read
        content_type: ${content_type|application/x-gzip}
        display_name: "working-dir.tar.gz"
    - command: archive.targz_pack
      params:
        target: "drivers-dir.tar.gz"
        source_dir: ${DRIVERS_TOOLS}
        include:
          - "./**"
        exclude_files:
          # Windows cannot read the mongod *.lock files because they are locked.
          - "*.lock"
    - command: s3.put
      params:
        aws_key: ${aws_key}
        aws_secret: ${aws_secret}
        local_file: drivers-dir.tar.gz
        remote_file: ${UPLOAD_BUCKET}/${build_variant}/${revision}/${version_id}/${build_id}/artifacts/${task_id}-${execution}-drivers-dir.tar.gz
        bucket: mciuploads
        permissions: public-read
        content_type: ${content_type|application/x-gzip}
        display_name: "drivers-dir.tar.gz"

  "upload test results":
    - command: attach.xunit_results
      params:
        # Uploading test results does not work when using ${PROJECT_DIRECTORY},
        # so we use an absolute path to work around this
        file: "src/test-results.xml"
    - command: attach.results
      params:
        file_location: "${DRIVERS_TOOLS}/results.json"

  "bootstrap mongo-orchestration":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          MONGODB_VERSION=${VERSION} ORCHESTRATION_FILE=${ORCHESTRATION_FILE} TOPOLOGY=${TOPOLOGY} AUTH=${AUTH} SSL=${SSL} STORAGE_ENGINE=${STORAGE_ENGINE} LOAD_BALANCER=${LOAD_BALANCER} REQUIRE_API_VERSION=${REQUIRE_API_VERSION} sh ${DRIVERS_TOOLS}/.evergreen/run-orchestration.sh
    # run-orchestration generates expansion file with MONGODB_URI and CRYPT_SHARED_LIB_PATH
    - command: expansions.update
      params:
        file: mo-expansion.yml

  "stop mongo-orchestration":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          sh ${DRIVERS_TOOLS}/.evergreen/stop-orchestration.sh

  "bootstrap mongohoused":
    - command: shell.exec
      params:
        script: |
          VARIANT=${VARIANT} DRIVERS_TOOLS="${DRIVERS_TOOLS}" sh ${DRIVERS_TOOLS}/.evergreen/atlas_data_lake/build-mongohouse-local.sh
    - command: shell.exec
      params:
        background: true
        script: |
          DRIVERS_TOOLS="${DRIVERS_TOOLS}" sh ${DRIVERS_TOOLS}/.evergreen/atlas_data_lake/run-mongohouse-local.sh

  "create serverless instance":
    - command: shell.exec
      params:
        working_dir: "src"
        script: |
          ${PREPARE_SHELL}
          SERVERLESS_DRIVERS_GROUP=${SERVERLESS_DRIVERS_GROUP} \
          SERVERLESS_API_PUBLIC_KEY=${SERVERLESS_API_PUBLIC_KEY} \
          SERVERLESS_API_PRIVATE_KEY=${SERVERLESS_API_PRIVATE_KEY} \
          bash ${DRIVERS_TOOLS}/.evergreen/serverless/create-instance.sh
    - command: expansions.update
      params:
        file: src/serverless-expansion.yml
    - command: shell.exec
      params:
        shell: "bash"
        script: |
          ${PREPARE_SHELL}
          
          if [ -z "${SERVERLESS_MONGODB_VERSION}" ]; then
            echo "expected SERVERLESS_MONGODB_VERSION to be set"
            exit 1
          fi
          
          # Download the enterprise server download for current platform to $MONGODB_BINARIES.
          # This is required for tests that need mongocryptd.
          # $MONGODB_BINARIES is added to the $PATH in fetch-source.
          ${PYTHON3_BINARY} $DRIVERS_TOOLS/.evergreen/mongodl.py \
              --component archive \
              --version ${SERVERLESS_MONGODB_VERSION} \
              --edition enterprise \
              --out $MONGODB_BINARIES \
              --strip-path-components 2
          
          # Download the crypt_shared dynamic library for the current platform.
          ${PYTHON3_BINARY} $DRIVERS_TOOLS/.evergreen/mongodl.py \
            --component crypt_shared \
            --version ${SERVERLESS_MONGODB_VERSION} \
            --edition enterprise \
            --out . \
            --only "**/mongo_crypt_v1.*" \
            --strip-path-components 1
          
          # Find the crypt_shared library file in the current directory and set the CRYPT_SHARED_LIB_PATH to
          # the path of that file. Only look for .so, .dll, or .dylib files to prevent matching any other
          # downloaded files.
          CRYPT_SHARED_LIB_PATH="$(find $(pwd) -maxdepth 1 -type f \
            -name 'mongo_crypt_v1.so' -o \
            -name 'mongo_crypt_v1.dll' -o \
            -name 'mongo_crypt_v1.dylib')"
          
          # If we're on Windows, convert the "cygdrive" path to Windows-style paths.
          if [ "Windows_NT" = "$OS" ]; then
              CRYPT_SHARED_LIB_PATH=$(cygpath -m $CRYPT_SHARED_LIB_PATH)
          fi
          
          echo "CRYPT_SHARED_LIB_PATH: $CRYPT_SHARED_LIB_PATH" >> crypt-expansion.yml

    # Load the expansion file to make an evergreen variable with the current unique version
    - command: expansions.update
      params:
        file: crypt-expansion.yml

  "delete serverless instance":
    - command: shell.exec
      params:
        script: |
          # Only run if a serverless instance was started
          if [ -n "${SERVERLESS_INSTANCE_NAME}" ]; then
            SERVERLESS_INSTANCE_NAME=${SERVERLESS_INSTANCE_NAME} \
            SERVERLESS_DRIVERS_GROUP=${SERVERLESS_DRIVERS_GROUP} \
            SERVERLESS_API_PUBLIC_KEY=${SERVERLESS_API_PUBLIC_KEY} \
            SERVERLESS_API_PRIVATE_KEY=${SERVERLESS_API_PRIVATE_KEY} \
            bash ${DRIVERS_TOOLS}/.evergreen/serverless/delete-instance.sh
          fi

  "run tests":
    - command: shell.exec
      type: test
      params:
        working_dir: "src"
        script: |
          ${PREPARE_SHELL}
          export AWS_ACCESS_KEY_ID="${client_side_encryption_aws_access_key_id}"
          export AWS_SECRET_ACCESS_KEY="${client_side_encryption_aws_secret_access_key}"
          export AZURE_TENANT_ID="${client_side_encryption_azure_tenant_id}"
          export AZURE_CLIENT_ID="${client_side_encryption_azure_client_id}"
          export AZURE_CLIENT_SECRET="${client_side_encryption_azure_client_secret}"
          export GCP_EMAIL="${client_side_encryption_gcp_email}"
          export GCP_PRIVATE_KEY="${client_side_encryption_gcp_privatekey}"
          export KMIP_ENDPOINT="${client_side_encryption_kmip_endpoint}"
          export KMS_ENDPOINT_EXPIRED="${client_side_encryption_kms_endpoint_expired}"
          export KMS_ENDPOINT_WRONG_HOST="${client_side_encryption_kms_endpoint_wrong_host}"
          export KMS_ENDPOINT_REQUIRE_CLIENT_CERT="${client_side_encryption_kms_endpoint_require_client_cert}"
          export KMS_TLS_CA_FILE="${client_side_encryption_kms_tls_ca_file}"
          export KMS_TLS_CERTIFICATE_KEY_FILE="${client_side_encryption_kms_tls_certificate_key_file}"
          export PATH="${PHP_PATH}/bin:$PATH"

          API_VERSION=${API_VERSION} \
          CRYPT_SHARED_LIB_PATH=${CRYPT_SHARED_LIB_PATH} \
          MONGODB_URI="${MONGODB_URI}" \
          PHP_VERSION=${PHP_VERSION} \
          SKIP_CRYPT_SHARED=${SKIP_CRYPT_SHARED} \
          SSL=${SSL} \
          TESTS=${TESTS} \
          sh ${PROJECT_DIRECTORY}/.evergreen/run-tests.sh

  "run atlas data lake test":
     - command: shell.exec
       type: test
       params:
         working_dir: "src"
         script: |
           ${PREPARE_SHELL}
           export PATH="${PHP_PATH}/bin:$PATH"

           MONGODB_URI="***************************************" \
           TESTS="atlas-data-lake" \
           sh ${PROJECT_DIRECTORY}/.evergreen/run-tests.sh

  "run serverless tests":
    - command: shell.exec
      type: test
      params:
        working_dir: "src"
        script: |
          ${PREPARE_SHELL}
          export AWS_ACCESS_KEY_ID="${client_side_encryption_aws_access_key_id}"
          export AWS_SECRET_ACCESS_KEY="${client_side_encryption_aws_secret_access_key}"
          export AZURE_TENANT_ID="${client_side_encryption_azure_tenant_id}"
          export AZURE_CLIENT_ID="${client_side_encryption_azure_client_id}"
          export AZURE_CLIENT_SECRET="${client_side_encryption_azure_client_secret}"
          export GCP_EMAIL="${client_side_encryption_gcp_email}"
          export GCP_PRIVATE_KEY="${client_side_encryption_gcp_privatekey}"
          export KMIP_ENDPOINT="${client_side_encryption_kmip_endpoint}"
          export KMS_ENDPOINT_EXPIRED="${client_side_encryption_kms_endpoint_expired}"
          export KMS_ENDPOINT_WRONG_HOST="${client_side_encryption_kms_endpoint_wrong_host}"
          export KMS_ENDPOINT_REQUIRE_CLIENT_CERT="${client_side_encryption_kms_endpoint_require_client_cert}"
          export KMS_TLS_CA_FILE="${client_side_encryption_kms_tls_ca_file}"
          export KMS_TLS_CERTIFICATE_KEY_FILE="${client_side_encryption_kms_tls_certificate_key_file}"
          export MONGODB_IS_SERVERLESS=on
          export MONGODB_USERNAME=${SERVERLESS_ATLAS_USER}
          export MONGODB_PASSWORD=${SERVERLESS_ATLAS_PASSWORD}
          export PATH="${PHP_PATH}/bin:$PATH"

          CRYPT_SHARED_LIB_PATH=${CRYPT_SHARED_LIB_PATH} \
          MONGODB_URI="${SERVERLESS_URI}" \
          SKIP_CRYPT_SHARED=${SKIP_CRYPT_SHARED} \
          TESTS="serverless" \
          sh ${PROJECT_DIRECTORY}/.evergreen/run-tests.sh

  "cleanup":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          rm -rf $DRIVERS_TOOLS || true

  "fix absolute paths":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          for filename in $(find ${DRIVERS_TOOLS} -name \*.json); do
            perl -p -i -e "s|ABSOLUTE_PATH_REPLACEMENT_TOKEN|${DRIVERS_TOOLS}|g" $filename
          done

  "windows fix":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          for i in $(find ${DRIVERS_TOOLS}/.evergreen ${PROJECT_DIRECTORY}/.evergreen -name \*.sh); do
            cat $i | tr -d '\r' > $i.new
            mv $i.new $i
          done
          # Copy client certificate because symlinks do not work on Windows.
          cp ${DRIVERS_TOOLS}/.evergreen/x509gen/client.pem ${MONGO_ORCHESTRATION_HOME}/lib/client.pem

  "make files executable":
    - command: shell.exec
      params:
        script: |
          ${PREPARE_SHELL}
          for i in $(find ${DRIVERS_TOOLS}/.evergreen ${PROJECT_DIRECTORY}/.evergreen -name \*.sh); do
            chmod +x $i
          done

  "install dependencies":
    - command: shell.exec
      params:
        working_dir: "src"
        script: |
          ${PREPARE_SHELL}
          file="${PROJECT_DIRECTORY}/.evergreen/install-dependencies.sh"
          # Don't use ${file} syntax here because evergreen treats it as an empty expansion.
          [ -f "$file" ] && PHP_VERSION=${PHP_VERSION} EXTENSION_VERSION=${EXTENSION_VERSION} EXTENSION_REPO=${EXTENSION_REPO} EXTENSION_BRANCH=${EXTENSION_BRANCH} DEPENDENCIES=${DEPENDENCIES} sh $file || echo "$file not available, skipping"
    # install-dependencies generates expansion file with the PHP_PATH for the chosen PHP version
    - command: expansions.update
      params:
        file: src/php-expansion.yml

  "start load balancer":
    - command: shell.exec
      params:
        script: |
          MONGODB_URI="${MONGODB_URI}" ${DRIVERS_TOOLS}/.evergreen/run-load-balancer.sh start
    - command: expansions.update
      params:
        file: lb-expansion.yml

  "stop load balancer":
    - command: shell.exec
      params:
        script: |
          # Only run if a load balancer was started
          if [ -n "${SINGLE_MONGOS_LB_URI}" ]; then
            ${DRIVERS_TOOLS}/.evergreen/run-load-balancer.sh stop
          fi

  "start kms servers":
    - command: shell.exec
      # Init venv without background:true to install dependencies
      params:
        script: |-
          set -o errexit
          cd ${DRIVERS_TOOLS}/.evergreen/csfle
          . ./activate_venv.sh
    - command: shell.exec
      params:
        background: true
        # Use different ports for KMS HTTP servers to avoid conflicts with load balancers
        script: |-
          set -o errexit
          cd ${DRIVERS_TOOLS}/.evergreen/csfle
          . ./activate_venv.sh
          python -u kms_http_server.py --ca_file ../x509gen/ca.pem --cert_file ../x509gen/expired.pem --port 8100 &
          python -u kms_http_server.py --ca_file ../x509gen/ca.pem --cert_file ../x509gen/wrong-host.pem --port 8101 &
          python -u kms_http_server.py --ca_file ../x509gen/ca.pem --cert_file ../x509gen/server.pem --port 8102 --require_client_cert &
          python -u kms_kmip_server.py --port 5698 &
    - command: expansions.update
      params:
        updates:
          - key: client_side_encryption_kms_tls_ca_file
            value: ${DRIVERS_TOOLS}/.evergreen/x509gen/ca.pem
          - key: client_side_encryption_kms_tls_certificate_key_file
            value: ${DRIVERS_TOOLS}/.evergreen/x509gen/client.pem
          - key: client_side_encryption_kms_endpoint_expired
            value: 127.0.0.1:8100
          - key: client_side_encryption_kms_endpoint_wrong_host
            value: 127.0.0.1:8101
          - key: client_side_encryption_kms_endpoint_require_client_cert
            value: 127.0.0.1:8102
          - key: client_side_encryption_kmip_endpoint
            value: localhost:5698

pre:
  - func: "fetch source"
  - func: "prepare resources"
  - func: "windows fix"
  - func: "fix absolute paths"
  - func: "make files executable"
  - func: "install dependencies"

post:
  # Skip: uploading the full working directory is not needed by drivers-evergreen-tools.
  # - func: "upload working dir"
  - func: "upload mo artifacts"
  - func: "upload test results"
  - func: "delete serverless instance"
  - func: "stop load balancer"
  - func: "stop mongo-orchestration"
  - func: "cleanup"

tasks:

    # Wildcard task. Do you need to find out what tools are available and where?
    # Throw it here, and execute this task on all buildvariants
    - name: getdata
      commands:
        - command: shell.exec
          type: test
          params:
            script: |
               . ${DRIVERS_TOOLS}/.evergreen/download-mongodb.sh || true
               get_distro || true
               echo $DISTRO
               echo $MARCH
               echo $OS
               uname -a || true
               ls /etc/*release* || true
               cc --version || true
               gcc --version || true
               clang --version || true
               gcov --version || true
               lcov --version || true
               llvm-cov --version || true
               echo $PATH
               ls -la /usr/local/Cellar/llvm/*/bin/ || true
               ls -la /usr/local/Cellar/ || true
               scan-build --version || true
               genhtml --version || true
               valgrind --version || true


# Standard test tasks {{{

    - name: "test-standalone"
      tags: ["standalone"]
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "server"
        - func: "start kms servers"
        - func: "run tests"

    - name: "test-replica_set"
      tags: ["replica_set"]
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "replica_set"
        - func: "start kms servers"
        - func: "run tests"

    - name: "test-sharded_cluster"
      tags: ["sharded_cluster"]
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "sharded_cluster"
        - func: "start kms servers"
        - func: "run tests"

    - name: "test-atlas-data-lake"
      commands:
        - func: "bootstrap mongohoused"
        - func: "run atlas data lake test"

    - name: "test-requireApiVersion"
      tags: ["versioned-api"]
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "server"
            AUTH: "auth"
            REQUIRE_API_VERSION: "yes"
        - func: "start kms servers"
        - func: "run tests"
          vars:
            API_VERSION: "1"

    - name: "test-acceptApiVersion2"
      tags: ["versioned-api"]
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "server"
            ORCHESTRATION_FILE: "versioned-api-testing.json"
        - func: "start kms servers"
        - func: "run tests"
          vars:
            TESTS: "versioned-api"

    - name: "test-serverless"
      tags: ["serverless"]
      commands:
        - func: "create serverless instance"
        - func: "start kms servers"
        - func: "run serverless tests"

    - name: "test-loadBalanced"
      tags: ["loadbalanced"]
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "sharded_cluster"
            LOAD_BALANCER: "true"
            SSL: "yes"
        - func: "start load balancer"
        - func: "start kms servers"
        - func: "run tests"
          vars:
            # Note: loadBalanced=true should already be appended to SINGLE_MONGOS_LB_URI
            MONGODB_URI: "${SINGLE_MONGOS_LB_URI}"
            SSL: "yes"
        # Note: "stop load balancer" will be called from "post"

    - name: "test-skip_crypt_shared"
      commands:
        - func: "bootstrap mongo-orchestration"
          vars:
            TOPOLOGY: "replica_set"
        - func: "start kms servers"
        - func: "run tests"
          vars:
            SKIP_CRYPT_SHARED: "yes"
            TESTS: "csfle"

# }}}


axes:
  # Note: install-dependencies.sh will search for the latest minor version
  # matching the PHP_VERSION constant
  - id: php-versions
    display_name: PHP Version
    values:
      - id: "8.2"
        display_name: "PHP 8.2"
        variables:
          PHP_VERSION: "8.2"
      - id: "8.1"
        display_name: "PHP 8.1"
        variables:
          PHP_VERSION: "8.1"
      - id: "8.0"
        display_name: "PHP 8.0"
        variables:
          PHP_VERSION: "8.0"
      - id: "7.4"
        display_name: "PHP 7.4"
        variables:
          PHP_VERSION: "7.4"
      - id: "7.3"
        display_name: "PHP 7.3"
        variables:
          PHP_VERSION: "7.3"
      - id: "7.2"
        display_name: "PHP 7.2"
        variables:
          PHP_VERSION: "7.2"

  - id: php-edge-versions
    display_name: PHP Version
    values:
      - id: "latest-stable"
        display_name: "PHP 8.1"
        variables:
          PHP_VERSION: "8.1"
      - id: "oldest-supported"
        display_name: "PHP 7.2"
        variables:
          PHP_VERSION: "7.2"

  - id: mongodb-versions
    display_name: MongoDB Version
    values:
      - id: "latest"
        display_name: "MongoDB latest"
        variables:
           VERSION: "latest"
      - id: "6.0"
        display_name: "MongoDB 6.0"
        variables:
           VERSION: "6.0"
      - id: "5.0"
        display_name: "MongoDB 5.0"
        variables:
           VERSION: "5.0"
      - id: "4.4"
        display_name: "MongoDB 4.4"
        variables:
           VERSION: "4.4"
      - id: "4.2"
        display_name: "MongoDB 4.2"
        variables:
           VERSION: "4.2"
      - id: "4.0"
        display_name: "MongoDB 4.0"
        variables:
           VERSION: "4.0"
      - id: "3.6"
        display_name: "MongoDB 3.6"
        variables:
           VERSION: "3.6"

  - id: mongodb-edge-versions
    display_name: MongoDB Version
    values:
      - id: "latest-stable"
        display_name: "MongoDB 6.0"
        variables:
          VERSION: "6.0"
      - id: "oldest-supported"
        display_name: "MongoDB 3.6"
        variables:
          VERSION: "3.6"

  - id: driver-versions
    display_name: Driver Version
    values:
      - id: "oldest-supported"
        display_name: "PHPC 1.15.0"
        variables:
          EXTENSION_VERSION: "1.15.0"
      - id: "latest-stable"
        display_name: "PHPC (1.15.x)"
        variables:
          EXTENSION_VERSION: "stable"
      - id: "latest-dev"
        display_name: "PHPC (1.16-dev)"
        variables:
          EXTENSION_BRANCH: "master"

  - id: os
    display_name: OS
    values:
      - id: debian11
        display_name: "Debian 11"
        run_on: debian11
      - id: debian10
        display_name: "Debian 10"
        run_on: debian10
      - id: debian92
        display_name: "Debian 9.2"
        run_on: debian92
      - id: rhel70
        display_name: "RHEL 7.0"
        run_on: rhel70
      - id: rhel71-power8
        display_name: "RHEL 7.1 Power 8"
        run_on: rhel71-power8-build
      - id: rhel72-zseries
        display_name: "RHEL 7.2 zSeries"
        run_on: rhel72-zseries-build
      - id: ubuntu1804-arm64
        display_name: "Ubuntu 18.04 ARM64"
        run_on: ubuntu1804-arm64-test

  - id: topology
    display_name: Topology
    values:
      - id: standalone
        display_name: Standalone
        variables:
           TOPOLOGY: "server"
      - id: replicaset
        display_name: Replica Set
        variables:
           TOPOLOGY: "replica_set"
      - id: sharded-cluster
        display_name: Sharded Cluster
        variables:
           TOPOLOGY: "sharded_cluster"

  - id: auth
    display_name: Authentication
    values:
      - id: auth
        display_name: Auth
        variables:
           AUTH: "auth"
      - id: noauth
        display_name: NoAuth
        variables:
           AUTH: "noauth"

  - id: ssl
    display_name: SSL
    values:
      - id: ssl
        display_name: SSL
        variables:
           SSL: "ssl"
      - id: nossl
        display_name: NoSSL
        variables:
           SSL: "nossl"

  - id: storage-engine
    display_name: Storage
    values:
      - id: mmapv1
        display_name: MMAPv1
        variables:
           STORAGE_ENGINE: "mmapv1"
      - id: wiredtiger
        display_name: WiredTiger
        variables:
           STORAGE_ENGINE: "wiredtiger"
      - id: inmemory
        display_name: InMemory
        variables:
           STORAGE_ENGINE: "inmemory"

  - id: dependencies
    display_name: Dependencies
    values:
      - id: lowest
        display_name: Lowest
        variables:
          DEPENDENCIES: "lowest"

buildvariants:
# Test all PHP versions with latest-stable MongoDB and PHPC on Debian
- matrix_name: "test-php-versions"
  matrix_spec: { "os": "debian11", "mongodb-edge-versions": "latest-stable", "php-versions": "*", "driver-versions": "latest-stable" }
  display_name: "${os}, ${mongodb-edge-versions}, ${php-versions}, ${driver-versions}"
  exclude_spec:
    # Exclude "latest-stable" PHP version for Debian 11 (see: test-mongodb-versions matrix)
    - { "os": "debian11", "mongodb-edge-versions": "latest-stable", "php-versions": "8.1", "driver-versions": "latest-stable" }
  tasks:
    - name: "test-standalone"
    - name: "test-replica_set"
    - name: "test-sharded_cluster"

# Test all topologies and MongoDB versions with latest-stable PHP and PHPC on Debian
- matrix_name: "test-mongodb-versions"
  matrix_spec: { "os": ["debian92", "debian11"], "mongodb-versions": "*", "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  display_name: "${os}, ${mongodb-versions}, ${php-edge-versions}, ${driver-versions}"
  exclude_spec:
    # Debian 9.2 only supports up to MongoDB 5.0
    - { "os": "debian92", "mongodb-versions": ["6.0", "latest"], "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
    - { "os": "debian11", "mongodb-versions": ["3.6", "4.0", "4.2", "4.4", "5.0"], "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  tasks:
    - name: "test-standalone"
    - name: "test-replica_set"
    - name: "test-sharded_cluster"

# Test oldest-supported PHP, MongoDB, and driver versions with lowest dependencies on Debian
- matrix_name: "test-oldest-supported"
  matrix_spec: { "os": "debian92", "mongodb-edge-versions": "oldest-supported", "php-edge-versions": "oldest-supported", "driver-versions": "oldest-supported", "dependencies": "lowest" }
  display_name: "Lowest Dependencies: ${os}, ${mongodb-edge-versions}, ${php-edge-versions}, ${driver-versions}"
  tasks:
    - name: "test-standalone"
    - name: "test-replica_set"
    - name: "test-sharded_cluster"

- matrix_name: "atlas-data-lake-test"
  matrix_spec: { "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  display_name: "Atlas Data Lake"
  run_on: debian11
  expansions:
     VARIANT: debian11 # Referenced by ADL build script for downloading MQLRun
  tasks:
    - name: "test-atlas-data-lake"

# Stable API is available from MongoDB 5.0+
- matrix_name: "test-requireApiVersion"
  matrix_spec: { "os": "debian11", "mongodb-versions": ["5.0", "6.0"], "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  display_name: "Versioned API - ${mongodb-versions}"
  tasks:
    - .versioned-api

- matrix_name: "serverless"
  matrix_spec: { "os": "debian11", "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  display_name: "Serverless"
  tasks:
    - .serverless

# Load balancer is available from MongoDB 5.0+
- matrix_name: "test-loadBalanced"
  matrix_spec: { "os": "debian11", "mongodb-versions": ["5.0", "6.0"], "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  display_name: "Load balanced - ${mongodb-versions}"
  tasks:
    - name: "test-loadBalanced"

# CSFLE crypt_shared is available from MongoDB 6.0+, so explicitly test without it to allow use of mongocryptd
- matrix_name: "test-csfle-skip_crypt_shared"
  matrix_spec: { "os": "debian11", "mongodb-versions": "6.0", "php-edge-versions": "latest-stable", "driver-versions": "latest-stable" }
  display_name: "CSFLE skip_crypt_shared - ${mongodb-versions}"
  tasks:
    - name: "test-skip_crypt_shared"
