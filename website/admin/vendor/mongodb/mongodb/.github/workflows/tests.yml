name: "Tests"

on:
  pull_request:
    branches:
      - "v*.*"
      - "master"
      - "feature/*"
  push:
    branches:
      - "v*.*"
      - "master"
      - "feature/*"

jobs:
  phpunit:
    name: "PHPUnit tests"
    runs-on: "${{ matrix.os }}"

    strategy:
      fail-fast: true
      matrix:
        os:
          - "ubuntu-20.04"
        php-version:
          - "7.4"
          - "8.0"
          - "8.1"
          - "8.2"
        mongodb-version:
          - "4.4"
        driver-version:
          - "mongodb/mongo-php-driver@master"
        topology:
          - "server"
        include:
          - os: "ubuntu-20.04"
            php-version: "8.0"
            mongodb-version: "6.0"
            driver-version: "mongodb/mongo-php-driver@master"
            topology: "replica_set"
          - os: "ubuntu-20.04"
            php-version: "8.0"
            mongodb-version: "6.0"
            driver-version: "mongodb/mongo-php-driver@master"
            topology: "sharded_cluster"
          - os: "ubuntu-20.04"
            php-version: "8.0"
            mongodb-version: "5.0"
            driver-version: "mongodb/mongo-php-driver@master"
            topology: "server"
          - os: "ubuntu-20.04"
            php-version: "8.0"
            mongodb-version: "4.4"
            driver-version: "mongodb/mongo-php-driver@master"
            topology: "replica_set"
          - os: "ubuntu-20.04"
            php-version: "8.0"
            mongodb-version: "4.4"
            driver-version: "mongodb/mongo-php-driver@master"
            topology: "sharded_cluster"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"
        with:
          fetch-depth: 2

      - id: setup-mongodb
        uses: mongodb-labs/drivers-evergreen-tools@master
        with:
          version: ${{ matrix.mongodb-version }}
          topology: ${{ matrix.topology }}

      - name: Setup cache environment
        id: extcache
        uses: shivammathur/cache-extensions@v1
        with:
          php-version: ${{ matrix.php-version }}
          extensions: "mongodb-${{ matrix.driver-version }}"
          key: "extcache-v1"

      - name: Cache extensions
        uses: actions/cache@v2
        with:
          path: ${{ steps.extcache.outputs.dir }}
          key: ${{ steps.extcache.outputs.key }}
          restore-keys: ${{ steps.extcache.outputs.key }}

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          tools: "pecl"
          extensions: "mongodb-${{ matrix.driver-version }}"
          coverage: "none"
          ini-values: "zend.assertions=1"

      - name: "Show driver information"
        run: "php --ri mongodb"

      - name: "Cache dependencies installed with composer"
        uses: "actions/cache@v2"
        with:
          path: "~/.composer/cache"
          key: "php-${{ matrix.php-version }}-composer-locked-${{ hashFiles('composer.lock') }}"
          restore-keys: "php-${{ matrix.php-version }}-composer-locked-"

      - name: "Install dependencies with composer"
        run: "composer update --no-interaction --no-progress"

      - name: "Run PHPUnit"
        run: "vendor/bin/simple-phpunit -v"
        env:
          SYMFONY_DEPRECATIONS_HELPER: 999999
          MONGODB_URI: ${{ steps.setup-mongodb.outputs.cluster-uri }}
