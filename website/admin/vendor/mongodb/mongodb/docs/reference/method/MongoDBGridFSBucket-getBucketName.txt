========================================
MongoDB\\GridFS\\Bucket::getBucketName()
========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getBucketName()

   Returns the name of this bucket.

   .. code-block:: php

      function getBucketName(): string

Return Values
-------------

The name of this bucket as a string.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   var_dump($bucket->getBucketName());

The output would then resemble::

   string(2) "fs"
