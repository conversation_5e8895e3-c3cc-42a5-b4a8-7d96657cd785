===============================
MongoDB\\GridFS\\Bucket::drop()
===============================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::drop()

   Drops the files and chunks collections associated with this GridFS bucket.

   .. code-block:: php

      function drop(): void

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $database = (new MongoDB\Client)->test;

   $bucket = $database->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $bucket->uploadFromStream('filename', $stream);

   $bucket->drop();
