==================================
MongoDB\\GridFS\\Bucket::findOne()
==================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::findOne()

   Finds a single document from the GridFS bucket's files collection matching
   the query.

   .. code-block:: php

      function findOne($filter = [], array $options = []): array|object|null

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-findOne-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-findOne-option.rst

Return Values
-------------

An array or object for the :term:`first document <natural order>` that matched
the query, or ``null`` if no document matched the query. The return type will
depend on the ``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Behavior
--------

.. include:: /includes/extracts/note-bson-comparison.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $bucket->uploadFromStream('b', $stream);

   $fileDocument = $bucket->findOne(
       ['length' => ['$lte' => 6]],
       [
           'projection' => [
               'filename' => 1,
               'length' => 1,
               '_id' => 0,
           ],
           'sort' => ['length' => -1],
       ]
   );

   var_dump($fileDocument);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#3004 (1) {
     ["storage":"ArrayObject":private]=>
     array(2) {
       ["filename"]=>
       string(1) "b"
       ["length"]=>
       int(6)
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Collection::findOne()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::find()`
