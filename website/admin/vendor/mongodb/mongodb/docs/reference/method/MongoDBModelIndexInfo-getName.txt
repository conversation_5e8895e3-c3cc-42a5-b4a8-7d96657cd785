====================================
MongoDB\\Model\\IndexInfo::getName()
====================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\IndexInfo::getName()

   Return the index name. This correlates with the return value of
   :phpmethod:`MongoDB\\Collection::createIndex()`. An index name may be derived
   from the ``$key`` parameter or or explicitly specified via the ``name``
   option.

   .. code-block:: php

      function getName(): string

Return Values
-------------

The index name.

Examples
--------

.. code-block:: php

   <?php

   $info = new IndexInfo([
       'name' => 'x_1',
   ]);

   echo $info->getName();

The output would then resemble::

   x_1

See Also
--------

- :phpmethod:`MongoDB\\Collection::createIndex()`
- :manual:`listIndexes </reference/command/listIndexes>` command reference in
  the MongoDB manual
