===============================
MongoDB\\GridFS\\Bucket::find()
===============================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::find()

   Finds documents from the GridFS bucket's files collection matching the query.

   .. code-block:: php

      function find($filter = [], array $options = []): MongoDB\Driver\Cursor

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-find-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-find-option.rst

Return Values
-------------

A :php:`MongoDB\\Driver\\Cursor <class.mongodb-driver-cursor>` object.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Behavior
--------

.. include:: /includes/extracts/note-bson-comparison.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $bucket->uploadFromStream('b', $stream);

   $cursor = $bucket->find(
       ['length' => ['$lte' => 6]],
       [
           'projection' => [
               'filename' => 1,
               'length' => 1,
               '_id' => 0,
            ],
            'sort' => ['length' => -1],
        ]
   );

   var_dump($cursor->toArray());

The output would then resemble::

   array(1) {
     [0]=>
     object(MongoDB\Model\BSONDocument)#3015 (1) {
       ["storage":"ArrayObject":private]=>
       array(2) {
         ["filename"]=>
         string(1) "b"
         ["length"]=>
         int(6)
       }
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Collection::find()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::findOne()`
