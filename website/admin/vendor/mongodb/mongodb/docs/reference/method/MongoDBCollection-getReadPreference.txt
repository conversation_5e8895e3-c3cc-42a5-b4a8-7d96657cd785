========================================
MongoDB\\Collection::getReadPreference()
========================================

.. versionadded:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::getReadPreference()

   Returns the read preference for this collection.

   .. code-block:: php

      function getReadPreference(): MongoDB\Driver\ReadPreference

Return Values
-------------

A :php:`MongoDB\\Driver\\ReadPreference <class.mongodb-driver-readpreference>`
object.

Example
-------

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->selectCollection('test', 'users', [
       'readPreference' => new MongoDB\Driver\ReadPreference('primaryPreferred'),
   ]);

   var_dump($collection->getReadPreference());

The output would then resemble::

   object(MongoDB\Driver\ReadPreference)#5 (1) {
     ["mode"]=>
     string(16) "primaryPreferred"
   }

See Also
--------

- :manual:`Read Preference </reference/read-preference>` in the MongoDB manual
- :phpmethod:`MongoDB\\Client::getReadPreference()`
- :phpmethod:`MongoDB\\Database::getReadPreference()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::getReadPreference()`
