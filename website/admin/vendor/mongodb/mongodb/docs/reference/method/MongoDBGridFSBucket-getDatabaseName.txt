==========================================
MongoDB\\GridFS\\Bucket::getDatabaseName()
==========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getDatabaseName()

   Returns the name of the database containing this bucket.

   .. code-block:: php

      function getDatabaseName(): string

Return Values
-------------

The name of the database containing this bucket as a string.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   var_dump($bucket->getDatabaseName());

The output would then resemble::

   string(4) "test"

