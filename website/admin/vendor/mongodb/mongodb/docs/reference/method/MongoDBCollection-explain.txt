==============================
MongoDB\\Collection::explain()
==============================

.. versionadded:: 1.4

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::explain()

   Explain the given command.

   .. code-block:: php

      function explain(MongoDB\\Operation\\Explainable $explainable, array $options = []): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-explain-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-explain-option.rst

Return Values
-------------

An array or object with the result document of the :manual:`explain
</reference/command/explain>` command. The return type will depend on the
``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Explainable Commands
--------------------

Explainable commands include, but are not limited to:

   - :phpclass:`MongoDB\\Operation\\Aggregate`
   - :phpclass:`MongoDB\\Operation\\Count`
   - :phpclass:`MongoDB\\Operation\\DeleteMany`
   - :phpclass:`MongoDB\\Operation\\DeleteOne`
   - :phpclass:`MongoDB\\Operation\\Distinct`
   - :phpclass:`MongoDB\\Operation\\Find`
   - :phpclass:`MongoDB\\Operation\\FindOne`
   - :phpclass:`MongoDB\\Operation\\FindOneAndDelete`
   - :phpclass:`MongoDB\\Operation\\FindOneAndReplace`
   - :phpclass:`MongoDB\\Operation\\FindOneAndUpdate`
   - :phpclass:`MongoDB\\Operation\\UpdateMany`
   - :phpclass:`MongoDB\\Operation\\UpdateOne`

Examples
--------

This example explains a count command.

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->restaurants;

   $count = new MongoDB\Operation\Count(
       $collection->getDatabaseName(),
       $collection->getCollectionName(),
       ['cuisine' => 'Italian']
   );

   $result = $collection->explain($count);

   var_dump($result);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#29 (1) {
     ["storage":"ArrayObject":private]=>
     array(4) {
       ["queryPlanner"]=>
       object(MongoDB\Model\BSONDocument)#21 (1) {
         ["storage":"ArrayObject":private]=>
         array(6) {
           ["plannerVersion"]=>
           int(1)
           ["namespace"]=>
           string(16) "test.restaurants"
           ["indexFilterSet"]=>
           bool(false)
           ["parsedQuery"]=>
           object(MongoDB\Model\BSONDocument)#15 (1) {
             ["storage":"ArrayObject":private]=>
             array(1) {
               ["cuisine"]=>
               object(MongoDB\Model\BSONDocument)#14 (1) {
                 ["storage":"ArrayObject":private]=>
                 array(1) {
                   ["$eq"]=>
                   string(7) "Italian"
                 }
               }
             }
           }
           ["winningPlan"]=>
           object(MongoDB\Model\BSONDocument)#19 (1) {
             ["storage":"ArrayObject":private]=>
             array(2) {
               ["stage"]=>
               string(5) "COUNT"
               ["inputStage"]=>
               object(MongoDB\Model\BSONDocument)#18 (1) {
                 ["storage":"ArrayObject":private]=>
                 array(3) {
                   ["stage"]=>
                   string(8) "COLLSCAN"
                   ["filter"]=>
                   object(MongoDB\Model\BSONDocument)#17 (1) {
                     ["storage":"ArrayObject":private]=>
                     array(1) {
                       ["cuisine"]=>
                       object(MongoDB\Model\BSONDocument)#16 (1) {
                         ["storage":"ArrayObject":private]=>
                         array(1) {
                           ["$eq"]=>
                           string(7) "Italian"
                         }
                       }
                     }
                   }
                   ["direction"]=>
                   string(7) "forward"
                 }
               }
             }
           }
           ["rejectedPlans"]=>
           object(MongoDB\Model\BSONArray)#20 (1) {
             ["storage":"ArrayObject":private]=>
             array(0) {
             }
           }
         }
       }
       ["executionStats"]=>
       object(MongoDB\Model\BSONDocument)#27 (1) {
         ["storage":"ArrayObject":private]=>
         array(7) {
           ["executionSuccess"]=>
           bool(true)
           ["nReturned"]=>
           int(0)
           ["executionTimeMillis"]=>
           int(24)
           ["totalKeysExamined"]=>
           int(0)
           ["totalDocsExamined"]=>
           int(25359)
           ["executionStages"]=>
           object(MongoDB\Model\BSONDocument)#25 (1) {
             ["storage":"ArrayObject":private]=>
             array(14) {
               ["stage"]=>
               string(5) "COUNT"
               ["nReturned"]=>
               int(0)
               ["executionTimeMillisEstimate"]=>
               int(20)
               ["works"]=>
               int(25361)
               ["advanced"]=>
               int(0)
               ["needTime"]=>
               int(25360)
               ["needYield"]=>
               int(0)
               ["saveState"]=>
               int(198)
               ["restoreState"]=>
               int(198)
               ["isEOF"]=>
               int(1)
               ["invalidates"]=>
               int(0)
               ["nCounted"]=>
               int(1069)
               ["nSkipped"]=>
               int(0)
               ["inputStage"]=>
               object(MongoDB\Model\BSONDocument)#24 (1) {
                 ["storage":"ArrayObject":private]=>
                 array(14) {
                   ["stage"]=>
                   string(8) "COLLSCAN"
                   ["filter"]=>
                   object(MongoDB\Model\BSONDocument)#23 (1) {
                     ["storage":"ArrayObject":private]=>
                     array(1) {
                       ["cuisine"]=>
                       object(MongoDB\Model\BSONDocument)#22 (1) {
                         ["storage":"ArrayObject":private]=>
                         array(1) {
                           ["$eq"]=>
                           string(7) "Italian"
                         }
                       }
                     }
                   }
                   ["nReturned"]=>
                   int(1069)
                   ["executionTimeMillisEstimate"]=>
                   int(20)
                   ["works"]=>
                   int(25361)
                   ["advanced"]=>
                   int(1069)
                   ["needTime"]=>
                   int(24291)
                   ["needYield"]=>
                   int(0)
                   ["saveState"]=>
                   int(198)
                   ["restoreState"]=>
                   int(198)
                   ["isEOF"]=>
                   int(1)
                   ["invalidates"]=>
                   int(0)
                   ["direction"]=>
                   string(7) "forward"
                   ["docsExamined"]=>
                   int(25359)
                 }
               }
             }
           }
           ["allPlansExecution"]=>
           object(MongoDB\Model\BSONArray)#26 (1) {
             ["storage":"ArrayObject":private]=>
             array(0) {
             }
           }
         }
       }
       ["serverInfo"]=>
       object(MongoDB\Model\BSONDocument)#28 (1) {
         ["storage":"ArrayObject":private]=>
         array(4) {
           ["host"]=>
           string(9) "localhost"
           ["port"]=>
           int(27017)
           ["version"]=>
           string(5) "3.6.1"
           ["gitVersion"]=>
           string(40) "025d4f4fe61efd1fb6f0005be20cb45a004093d1"
         }
       }
       ["ok"]=>
       float(1)
     }
   }

See Also
--------

- :manual:`explain </reference/command/explain>` command reference in the MongoDB
  manual
