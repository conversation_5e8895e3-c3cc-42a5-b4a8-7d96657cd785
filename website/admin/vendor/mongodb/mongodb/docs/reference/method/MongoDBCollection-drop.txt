===========================
MongoDB\\Collection::drop()
===========================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::drop()

   Drop the collection.

   .. code-block:: php

      function drop(array $options = []): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-drop-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-drop-option.rst

Return Values
-------------

An array or object with the result document of the :manual:`drop
</reference/command/drop>` command. The return type will depend on the
``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Example
-------

The following operation drops the ``restaurants`` collection in the ``test``
database:

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->restaurants;

   $result = $collection->drop();

   var_dump($result);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#9 (1) {
     ["storage":"ArrayObject":private]=>
     array(3) {
       ["ns"]=>
       string(16) "test.restaurants"
       ["nIndexesWas"]=>
       int(3)
       ["ok"]=>
       float(1)
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Database::dropCollection()`
- :manual:`drop </reference/command/drop>` command reference in the MongoDB
  manual
