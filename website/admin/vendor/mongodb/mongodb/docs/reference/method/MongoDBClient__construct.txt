==============================
MongoDB\\Client::__construct()
==============================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Client::__construct()

   Constructs a new :phpclass:`Client <MongoDB\\Client>` instance.

   .. code-block:: php

      function __construct(?string $uri = null, array $uriOptions = [], array $driverOptions = [])

   This constructor has the following parameters:

   .. include:: /includes/apiargs/MongoDBClient-method-construct-param.rst

   The ``$driverOptions`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBClient-method-construct-driverOptions.rst

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Behavior
--------

A :php:`MongoDB\\Driver\\Manager <mongodb-driver-manager>` is constructed
internally. Per the `Server Discovery and Monitoring
<https://github.com/mongodb/specifications/blob/master/source/server-discovery-and-monitoring/server-discovery-and-monitoring.rst#single-threaded-client-construction>`_
specification, :php:`MongoDB\\Driver\\Manager::__construct()
<mongodb-driver-manager.construct>` performs no I/O. Connections will be
initialized on demand, when the first operation is executed.

Examples
--------

.. start-connecting-include

Connecting to a Standalone server
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If you do not specify a ``$uri`` value, the driver connects to a standalone
:program:`mongod` on ``127.0.0.1`` via port ``27017``. To connect to a different
server, pass the corresponding connection string as the first parameter when
creating the :phpclass:`Client <MongoDB\\Client>` instance:

.. code-block:: php

   <?php

   $client = new MongoDB\Client('mongodb://mongodb-deployment:27017');

Connecting to a Replica Set
~~~~~~~~~~~~~~~~~~~~~~~~~~~

The following example demonstrates how to connect to a replica set with a custom
read preference:

.. code-block:: php

   <?php

   $client = new MongoDB\Client(
       'mongodb://rs1.example.com,rs2.example.com/?replicaSet=myReplicaSet',
       [
           'readPreference' => 'secondaryPreferred',
       ]
   );

Connecting with SSL and Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The following example demonstrates how to connect to a MongoDB replica set with
SSL and authentication, as is used for `MongoDB Atlas
<https://cloud.mongodb.com/?jmp=docs>`_:

.. code-block:: php

   <?php

   $client = new MongoDB\Client(
       'mongodb://myUsername:<EMAIL>,rs2.example.com/?ssl=true&replicaSet=myReplicaSet&authSource=admin'
   );

Alternatively, the authentication credentials and URI parameters may be
specified in the constructor's ``$uriOptions`` parameter:

.. code-block:: php

   <?php

   $client = new MongoDB\Client(
       'mongodb://rs1.example.com,rs2.example.com/'
       [
           'username' => 'myUsername',
           'password' => 'myPassword',
           'ssl' => true,
           'replicaSet' => 'myReplicaSet',
           'authSource' => 'admin',
       ],
   );

The driver supports additional :php:`SSL options
<mongodb-driver-manager.construct#mongodb-driver-manager.construct-driveroptions>`,
which may be specified in the constructor's ``$driverOptions`` parameter. Those
options are covered in the :php:`MongoDB\\Driver\\Manager::__construct()
<mongodb-driver-manager.construct>` documentation.

.. end-connecting-include

Specifying a Custom Type Map
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

By default, the |php-library| deserializes BSON documents and arrays
as :phpclass:`MongoDB\\Model\\BSONDocument` and
:phpclass:`MongoDB\\Model\\BSONArray` objects, respectively. The following
example demonstrates how to have the library unserialize everything as a PHP
array, as was done in the legacy ``mongo`` extension.

.. code-block:: php

   <?php

   $client = new MongoDB\Client(
       null,
       [],
       [
           'typeMap' => [
               'root' => 'array',
               'document' => 'array',
               'array' => 'array',
           ],
       ]
   );

See Also
--------

- :php:`MongoDB\\Driver\\Manager::__construct()
  <mongodb-driver-manager.construct>`
- :manual:`Connection String URI Format </reference/connection-string>` in the
  MongoDB manual
- `Server Discovery and Monitoring
  <https://github.com/mongodb/specifications/blob/master/source/server-discovery-and-monitoring/server-discovery-and-monitoring.rst#single-threaded-client-construction>`_
  specification
