========================
MongoDB\\Client::watch()
========================

.. versionadded:: 1.4

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Client::watch()

   Executes a :manual:`change stream </changeStreams>` operation on the client.
   The change stream can be watched for cluster-level changes.

   .. code-block:: php

      function watch(array $pipeline = [], array $options = []): MongoDB\ChangeStream

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBClient-method-watch-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBClient-method-watch-option.rst

Return Values
-------------

A :phpclass:`MongoDB\\ChangeStream` object, which allows for iteration of
events in the change stream via the :php:`Iterator <class.iterator>` interface.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unexpectedvalueexception.rst
.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

This example reports events while iterating a change stream.

.. code-block:: php

   <?php

   $uri = 'mongodb://rs1.example.com,rs2.example.com/?replicaSet=myReplicaSet';

   $client = new MongoDB\Client($uri);

   $changeStream = $client->watch();

   for ($changeStream->rewind(); true; $changeStream->next()) {
       if ( ! $changeStream->valid()) {
           continue;
       }

       $event = $changeStream->current();

       if ($event['operationType'] === 'invalidate') {
           break;
       }

       $ns = sprintf('%s.%s', $event['ns']['db'], $event['ns']['coll']);
       $id = json_encode($event['documentKey']['_id']);

       switch ($event['operationType']) {
           case 'delete':
               printf("Deleted document in %s with _id: %s\n\n", $ns, $id);
               break;

           case 'insert':
               printf("Inserted new document in %s\n", $ns);
               echo json_encode($event['fullDocument']), "\n\n";
               break;

           case 'replace':
               printf("Replaced new document in %s with _id: %s\n", $ns, $id);
               echo json_encode($event['fullDocument']), "\n\n";
               break;

           case 'update':
               printf("Updated document in %s with _id: %s\n", $ns, $id);
               echo json_encode($event['updateDescription']), "\n\n";
               break;
       }
   }

Assuming that a document was inserted, updated, and deleted while the above
script was iterating the change stream, the output would then resemble:

.. code-block:: none

   Inserted new document in app.user
   {"_id":{"$oid":"5b329b6674083047cc05e607"},"username":"bob"}

   Inserted new document in app.products
   {"_id":{"$oid":"5b329b6a74083047cc05e608"},"name":"Widget","quantity":5}

   Inserted new document in logs.messages
   {"_id":{"$oid":"5b329b7374083047cc05e609"},"msg":"bob purchased a widget"}

See Also
--------

- :phpmethod:`MongoDB\\Collection::watch()`
- :phpmethod:`MongoDB\\Database::watch()`
- :manual:`Aggregation Pipeline </core/aggregation-pipeline>` documentation in
  the MongoDB Manual
- :manual:`Change Streams </changeStreams>` documentation in the MongoDB manual
- :manual:`Change Events </reference/change-events/>` documentation in the
  MongoDB manual
