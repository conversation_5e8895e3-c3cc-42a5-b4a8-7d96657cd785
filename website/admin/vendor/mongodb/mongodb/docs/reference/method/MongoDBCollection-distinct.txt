===============================
MongoDB\\Collection::distinct()
===============================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::distinct()

   Finds the distinct values for a specified field across the collection.

   .. code-block:: php

      function distinct(string $fieldName, $filter = [], array $options = []): mixed[]

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-distinct-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-distinct-option.rst

Return Values
-------------

An array of the distinct values.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unexpectedvalueexception.rst
.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Behavior
--------

.. include:: /includes/extracts/note-bson-comparison.rst

Examples
--------

Return Distinct Values for a Field
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The following example identifies the distinct values for the ``borough`` field
in the ``restaurants`` collection in the ``test`` database.

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->restaurants;

   $distinct = $collection->distinct('borough');

   var_dump($distinct);

The output would then resemble::

   array(6) {
     [0]=>
     string(5) "Bronx"
     [1]=>
     string(8) "Brooklyn"
     [2]=>
     string(9) "Manhattan"
     [3]=>
     string(7) "Missing"
     [4]=>
     string(6) "Queens"
     [5]=>
     string(13) "Staten Island"
   }

Return Distinct Values Using a Filter
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The following example identifies the distinct values for the ``cuisine`` field
in the ``restaurants`` collection in the ``test`` database for documents where
the ``borough`` is ``Queens``:

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->restaurants;

   $distinct = $collection->distinct('cuisine', ['borough' => 'Queens']);

   var_dump($distinct);

The output would then resemble::

   array(75) {
     [0]=>
     string(6) "Afghan"
     [1]=>
     string(7) "African"
     [2]=>
     string(9) "American "
     [3]=>
     string(8) "Armenian"
     [4]=>
     string(5) "Asian"
     [5]=>
     string(10) "Australian"
     [6]=>
     string(15) "Bagels/Pretzels"
     [7]=>
     string(6) "Bakery"
     [8]=>
     string(11) "Bangladeshi"
     [9]=>
     string(8) "Barbecue"
     [10]=>
     string(55) "Bottled beverages, including water, sodas, juices, etc."
     [11]=>
     string(9) "Brazilian"
     [12]=>
     string(4) "Cafe"
     [13]=>
     string(16) "Café/Coffee/Tea"
     [14]=>
     string(5) "Cajun"
     [15]=>
     string(9) "Caribbean"
     [16]=>
     string(7) "Chicken"
     [17]=>
     string(7) "Chinese"
     [18]=>
     string(13) "Chinese/Cuban"
     [19]=>
     string(16) "Chinese/Japanese"
     [20]=>
     string(11) "Continental"
     [21]=>
     string(6) "Creole"
     [22]=>
     string(5) "Czech"
     [23]=>
     string(12) "Delicatessen"
     [24]=>
     string(6) "Donuts"
     [25]=>
     string(16) "Eastern European"
     [26]=>
     string(8) "Egyptian"
     [27]=>
     string(7) "English"
     [28]=>
     string(8) "Filipino"
     [29]=>
     string(6) "French"
     [30]=>
     string(17) "Fruits/Vegetables"
     [31]=>
     string(6) "German"
     [32]=>
     string(5) "Greek"
     [33]=>
     string(10) "Hamburgers"
     [34]=>
     string(16) "Hotdogs/Pretzels"
     [35]=>
     string(31) "Ice Cream, Gelato, Yogurt, Ices"
     [36]=>
     string(6) "Indian"
     [37]=>
     string(10) "Indonesian"
     [38]=>
     string(5) "Irish"
     [39]=>
     string(7) "Italian"
     [40]=>
     string(8) "Japanese"
     [41]=>
     string(13) "Jewish/Kosher"
     [42]=>
     string(30) "Juice, Smoothies, Fruit Salads"
     [43]=>
     string(6) "Korean"
     [44]=>
     string(64) "Latin (Cuban, Dominican, Puerto Rican, South & Central American)"
     [45]=>
     string(13) "Mediterranean"
     [46]=>
     string(7) "Mexican"
     [47]=>
     string(14) "Middle Eastern"
     [48]=>
     string(8) "Moroccan"
     [49]=>
     string(25) "Not Listed/Not Applicable"
     [50]=>
     string(18) "Nuts/Confectionary"
     [51]=>
     string(5) "Other"
     [52]=>
     string(9) "Pakistani"
     [53]=>
     string(16) "Pancakes/Waffles"
     [54]=>
     string(8) "Peruvian"
     [55]=>
     string(5) "Pizza"
     [56]=>
     string(13) "Pizza/Italian"
     [57]=>
     string(6) "Polish"
     [58]=>
     string(10) "Portuguese"
     [59]=>
     string(7) "Russian"
     [60]=>
     string(6) "Salads"
     [61]=>
     string(10) "Sandwiches"
     [62]=>
     string(30) "Sandwiches/Salads/Mixed Buffet"
     [63]=>
     string(7) "Seafood"
     [64]=>
     string(9) "Soul Food"
     [65]=>
     string(18) "Soups & Sandwiches"
     [66]=>
     string(12) "Southwestern"
     [67]=>
     string(7) "Spanish"
     [68]=>
     string(5) "Steak"
     [69]=>
     string(5) "Tapas"
     [70]=>
     string(7) "Tex-Mex"
     [71]=>
     string(4) "Thai"
     [72]=>
     string(7) "Turkish"
     [73]=>
     string(10) "Vegetarian"
     [74]=>
     string(29) "Vietnamese/Cambodian/Malaysia"
   }

See Also
--------

- :manual:`distinct </reference/command/distinct>` command reference in the
  MongoDB manual
