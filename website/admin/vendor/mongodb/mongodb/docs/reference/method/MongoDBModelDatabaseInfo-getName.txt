=======================================
MongoDB\\Model\\DatabaseInfo::getName()
=======================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\DatabaseInfo::getName()

   Return the database name.

   .. code-block:: php

      function getName(): string

Return Values
-------------

The database name.

Examples
--------

.. code-block:: php

   <?php

   $info = new DatabaseInfo(['name' => 'foo']);

   echo $info->getName();

The output would then resemble::

   foo

See Also
--------

- :phpmethod:`MongoDB\\Database::getDatabaseName()`
- :manual:`listDatabases </reference/command/listDatabases>` command reference
  in the MongoDB manual
