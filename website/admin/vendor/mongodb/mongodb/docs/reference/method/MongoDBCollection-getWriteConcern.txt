======================================
MongoDB\\Collection::getWriteConcern()
======================================

.. versionadded:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::getWriteConcern()

   Returns the write concern for this collection.

   .. code-block:: php

      function getWriteConcern(): MongoDB\Driver\WriteConcern

Return Values
-------------

A :php:`MongoDB\\Driver\\WriteConcern <class.mongodb-driver-writeconcern>`
object.

Example
-------

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->selectCollection('test', 'users', [
      'writeConcern' => new MongoDB\Driver\WriteConcern(1, 0, true),
   ]);

   var_dump($collection->getWriteConcern());

The output would then resemble::

   object(MongoDB\Driver\WriteConcern)#5 (2) {
     ["w"]=>
     int(1)
     ["j"]=>
     bool(true)
   }

See Also
--------

- :manual:`Write Concern </reference/read-concern>` in the MongoDB manual
- :php:`MongoDB\\Driver\\WriteConcern::isDefault() <mongodb-driver-writeconcern.isdefault>`
- :phpmethod:`MongoDB\\Client::getWriteConcern()`
- :phpmethod:`MongoDB\\Database::getWriteConcern()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::getWriteConcern()`
