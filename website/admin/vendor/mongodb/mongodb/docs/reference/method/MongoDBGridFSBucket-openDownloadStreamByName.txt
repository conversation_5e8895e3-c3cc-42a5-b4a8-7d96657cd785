===================================================
MongoDB\\GridFS\\Bucket::openDownloadStreamByName()
===================================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::openDownloadStreamByName()

   Selects a GridFS file by its ``filename`` and opens it as a readable stream.

   .. code-block:: php

      function openDownloadStreamByName(string $filename, array $options = []): resource

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-openDownloadStreamByName-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-openDownloadStreamByName-option.rst

Return Values
-------------

A readable stream resource.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-gridfs-filenotfoundexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $bucket->uploadFromStream('filename', $stream);

   var_dump(stream_get_contents($bucket->openDownloadStreamByName('filename')));

The output would then resemble::

   string(6) "foobar"

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::downloadToStream()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::downloadToStreamByName()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::openDownloadStream()`
