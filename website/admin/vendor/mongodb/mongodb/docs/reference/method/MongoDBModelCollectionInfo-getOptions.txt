============================================
MongoDB\\Model\\CollectionInfo::getOptions()
============================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\CollectionInfo::getOptions()

   Return the collection options. This correlates with the options for
   :phpmethod:`MongoDB\\Database::createCollection()`, but may include
   additional fields set by the server.

   .. code-block:: php

      function getOptions(): array

Return Values
-------------

The collection options. This corresponds to the ``options`` field returned in
the ``listCollections`` command reply.

Examples
--------

.. code-block:: php

   <?php

   $info = new CollectionInfo([
       'name' => 'foo',
       'options' => [
           'capped' => true,
           'size' => 1048576,
       ]
   ]);

   var_dump($info->getOptions());

The output would then resemble::

   array(2) {
     ["capped"]=>
     bool(true)
     ["size"]=>
     int(1048576)
   }

See Also
--------

- :phpmethod:`MongoDB\\Database::createCollection()`
- :manual:`listCollections </reference/command/listCollections>` command
  reference in the MongoDB manual
