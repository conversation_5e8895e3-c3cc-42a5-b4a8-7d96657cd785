=========================================
MongoDB\\UpdateResult::getModifiedCount()
=========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\UpdateResult::getModifiedCount()

   Return the number of documents that were modified.

   .. code-block:: php

      function getModifiedCount(): integer|null

   This method should only be called if the write was acknowledged.

   .. note::

      If an update/replace operation results in no change to the document
      (e.g. setting the value of a field to its current value), the modified
      count may be less than the value returned by :phpmethod:`getMatchedCount()
      <MongoDB\\UpdateResult::getMatchedCount()>`.

Return Values
-------------

The number of documents that were modified.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-badmethodcallexception-write-result.rst

See Also
--------

- :phpmethod:`MongoDB\\UpdateResult::getMatchedCount()`
- :php:`MongoDB\\Driver\\WriteResult::getModifiedCount()
  <manual/en/mongodb-driver-writeresult.getmodifiedcount.php>`
