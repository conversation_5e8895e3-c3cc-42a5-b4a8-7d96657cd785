==============================
MongoDB\\Collection::findOne()
==============================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::findOne()

   Finds a single document matching the query.

   .. code-block:: php

      function findOne($filter = [], array $options = []): array|object|null

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-findOne-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-findOne-option.rst

Return Values
-------------

An array or object for the :term:`first document <natural order>` that matched
the query, or ``null`` if no document matched the query. The return type will
depend on the ``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Behavior
--------

.. include:: /includes/extracts/note-bson-comparison.rst

Examples
--------

Matching BSON Types in Query Criteria
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

In the following example, documents in the ``restaurants`` collection use an
:manual:`ObjectId </reference/object-id/>` for their identifier (the default)
and documents in the ``zips`` collection use a string. Since ObjectId is a
special BSON type, the query criteria for selecting a restaurant must use the
:php:`MongoDB\\BSON\\ObjectId <class.mongodb-bson-objectid>` class.

.. code-block:: php

   $database = (new MongoDB\Client)->test;

   $zip = $database->zips->findOne(['_id' => '10036']);

   $restaurant = $database->restaurants->findOne([
       '_id' => new MongoDB\BSON\ObjectId('594d5ef280a846852a4b3f70'),
   ]);

Projecting Fields
~~~~~~~~~~~~~~~~~

The following example finds a restaurant based on the ``cuisine`` and
``borough`` fields and uses a :manual:`projection
</tutorial/project-fields-from-query-results>` to limit the fields that are
returned.

.. code-block:: php

   $collection = (new MongoDB\Client)->test->restaurants;

   $restaurant = $collection->findOne(
       [
           'cuisine' => 'Italian',
           'borough' => 'Manhattan',
       ],
       [
           'projection' => [
               'name' => 1,
               'borough' => 1,
               'cuisine' => 1,
           ],
       ]
   );

   var_dump($restaurant);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#10 (1) {
     ["storage":"ArrayObject":private]=>
     array(4) {
       ["_id"]=>
       object(MongoDB\BSON\ObjectId)#8 (1) {
         ["oid"]=>
         string(24) "576023c6b02fa9281da3f983"
       }
       ["borough"]=>
       string(9) "Manhattan"
       ["cuisine"]=>
       string(7) "Italian"
       ["name"]=>
       string(23) "Isle Of Capri Resturant"
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Collection::find()`
- :manual:`find </reference/command/find>` command reference in the MongoDB
  manual
