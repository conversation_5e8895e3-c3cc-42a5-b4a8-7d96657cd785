===========================================
MongoDB\\GridFS\\Bucket::openUploadStream()
===========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::openUploadStream()

   Opens a writable stream for a new GridFS file.

   .. code-block:: php

      function openUploadStream(string $filename, array $options = []): resource

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-openUploadStream-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-openUploadStream-option.rst

Return Values
-------------

A writable stream resource.

Behavior
--------

Chunk documents will be created as data is written to the writable stream. The
metadata document will be created when the writable stream is closed.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $uploadStream = $bucket->openUploadStream('filename');
   fwrite($uploadStream, 'foobar');
   fclose($uploadStream);

   $downloadStream = $bucket->openDownloadStreamByName('filename');
   var_dump(stream_get_contents($downloadStream));

The output would then resemble::

   string(6) "foobar"

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::uploadFromStream()`
