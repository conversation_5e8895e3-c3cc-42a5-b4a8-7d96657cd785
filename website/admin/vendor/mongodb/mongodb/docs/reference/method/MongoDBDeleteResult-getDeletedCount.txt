========================================
MongoDB\\DeleteResult::getDeletedCount()
========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\DeleteResult::getDeletedCount()

   Return the number of documents that were deleted.

   .. code-block:: php

      function getDeletedCount(): integer

   This method should only be called if the write was acknowledged.

Return Values
-------------

The number of documents that were deleted.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-badmethodcallexception-write-result.rst

See Also
--------

- :php:`MongoDB\\Driver\\WriteResult::getDeletedCount()
  <manual/en/mongodb-driver-writeresult.getdeletedcount.php>`
