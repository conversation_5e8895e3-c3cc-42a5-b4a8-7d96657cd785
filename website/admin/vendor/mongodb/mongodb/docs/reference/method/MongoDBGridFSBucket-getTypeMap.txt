=====================================
MongoDB\\GridFS\\Bucket::getTypeMap()
=====================================

.. versionadded:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getTypeMap()

   Returns the type map for this GridFS bucket.

   .. code-block:: php

      function getTypeMap(): array

Return Values
-------------

A :ref:`type map <php-type-map>` array.

Example
-------

.. code-block:: php

   <?php

   $database = (new MongoDB\Client)->selectDatabase('test');
   $bucket = $database->selectGridFSBucket([
       'typeMap' => [
           'root' => 'array',
           'document' => 'array',
           'array' => 'array',
       ],
   ]);

   var_dump($bucket->getTypeMap());

The output would then resemble::

   array(3) {
     ["root"]=>
     string(5) "array"
     ["document"]=>
     string(5) "array"
     ["array"]=>
     string(5) "array"
   }

See Also
--------

- :doc:`/reference/bson`
- :phpmethod:`MongoDB\\Client::getTypeMap()`
- :phpmethod:`MongoDB\\Collection::getTypeMap()`
- :phpmethod:`MongoDB\\Database::getTypeMap()`
