arg_name: param
name: $session
type: :php:`MongoDB\\Driver\\Session <mongodb-driver-session>`
description: |
  A client session used to execute the transaction.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $callback
type: callback
description: |
  A callback that will be run inside the transaction. The callback must accept a
  :php:`MongoDB\\Driver\\Session <mongodb-driver-session>` object as first
  argument.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $transactionOptions
type: array
description: |
  Transaction options, which will be passed to
  :php:`MongoDB\\Driver\\Session::startTransaction <mongodb-driver-session.starttransaction>`.
  See the extension documentation for a list of supported options.
interface: phpmethod
operation: ~
optional: true
...
