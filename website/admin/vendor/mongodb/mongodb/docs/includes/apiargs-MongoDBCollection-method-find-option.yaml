arg_name: option
name: projection
type: array|object
description: |
  The :ref:`projection specification <projections>` to determine which fields to
  include in the returned documents. See :manual:`Project Fields to Return from
  Query </tutorial/project-fields-from-query-results>` and
  :manual:`Projection Operators </reference/operator/projection>` in the MongoDB
  manual.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: sort
type: array|object
description: |
  The sort specification for the ordering of the results.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: skip
type: integer
description: |
  Number of documents to skip. Defaults to ``0``.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: limit
type: integer
description: |
  The maximum number of documents to return. If unspecified, then defaults to no
  limit. A limit of ``0`` is equivalent to setting no limit.

  A negative limit is similar to a positive limit but closes the cursor after
  returning a single batch of results. As such, with a negative limit, if the
  limited result set does not fit into a single batch, the number of documents
  received will be less than the specified limit. By passing a negative limit, the
  client indicates to the server that it will not ask for a subsequent batch via
  getMore.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: allowDiskUse
type: boolean
description: |
  Enables writing to temporary files. When set to ``true``, queries can write
  data to the ``_tmp`` sub-directory in the ``dbPath`` directory.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: batchSize
type: integer
description: |
  The number of documents to return in the first batch. Defaults to ``101``. A
  batchSize of ``0`` means that the cursor will be established, but no documents
  will be returned in the first batch.

  Unlike the previous wire protocol version, a batchSize of ``1`` for the
  :dbcommand:`find` command does not close the cursor.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  The comment can be any valid BSON type for server versions 4.4 and above.
  Earlier server versions only support string values.
---
arg_name: option
name: cursorType
type: integer
description: |
  Indicates the type of cursor to use. ``cursorType`` supports the following
  values:

   - ``MongoDB\Operation\Find::NON_TAILABLE`` (*default*)
   - ``MongoDB\Operation\Find::TAILABLE``
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: hint
post: |
  .. versionadded:: 1.2
---
arg_name: option
name: maxAwaitTimeMS
type: integer
description: |
  Positive integer denoting the time limit in milliseconds for the server to
  block a getMore operation if no data is available. This option should only be
  used if cursorType is TAILABLE_AWAIT.

  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readPreference
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
arg_name: option
name: max
type: array|object
description: |
  The exclusive upper bound for a specific index.

  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: maxScan
type: integer
description: |
  Maximum number of documents or index keys to scan when executing the query.

  .. deprecated:: 1.4
  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: min
type: array|object
description: |
  The inclusive lower bound for a specific index.

  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: oplogReplay
type: boolean
description: |
  Internal use for replica sets. To use ``oplogReplay``, you must include the
  following condition in the filter:

  .. code-block:: javascript

     { ts: { $gte: <timestamp> } }

  The :php:`MongoDB\\BSON\\Timestamp <class.mongodb-bson-timestamp>` class
  reference describes how to represent MongoDB's BSON timestamp type with PHP.

  .. deprecated:: 1.7
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: noCursorTimeout
type: boolean
description: |
  Prevents the server from timing out idle cursors after an inactivity period
  (10 minutes).
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: returnKey
type: boolean
description: |
  If true, returns only the index keys in the resulting documents.

  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: showRecordId
type: boolean
description: |
  Determines whether to return the record identifier for each document. If true,
  adds a field $recordId to the returned documents.

  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: snapshot
type: boolean
description: |
  Prevents the cursor from returning a document more than once because of an
  intervening write operation.

  .. deprecated:: 1.4
  .. versionadded:: 1.2
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: allowPartialResults
type: boolean
description: |
  For queries against a sharded collection, returns partial results from the
  :program:`mongos` if some shards are unavailable instead of throwing an error.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
---
arg_name: option
name: modifiers
type: array|object
description: |
  :manual:`Meta operators </reference/operator/query-modifier>` that modify the
  output or behavior of a query. Use of these operators is deprecated in favor
  of named options.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.13
...
