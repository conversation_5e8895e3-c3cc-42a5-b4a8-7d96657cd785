source:
  file: apiargs-aggregate-option.yaml
  ref: allowDiskUse
---
source:
  file: apiargs-aggregate-option.yaml
  ref: batchSize
---
source:
  file: apiargs-aggregate-option.yaml
  ref: bypassDocumentValidation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  The comment can be any valid BSON type for server versions 4.4 and above.
  Earlier server versions only support string values.

  .. versionadded:: 1.3
---
source:
  file: apiargs-aggregate-option.yaml
  ref: explain
post: |
  .. versionadded:: 1.4
---
source:
  file: apiargs-common-option.yaml
  ref: hint
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.9
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readPreference
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
---
arg_name: option
name: useCursor
type: boolean
description: |
  Indicates whether the command will request that the server provide results
  using a cursor. The default is ``true``.

  .. note::

     MongoDB 3.6+ no longer supports returning results without a cursor (excluding
     when the explain option is used) and specifying false for this option will
     result in a server error.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
post: |
  This only applies when a :ref:`$out <agg-out>` or :ref:`$merge <agg-merge>`
  stage is specified.
...
