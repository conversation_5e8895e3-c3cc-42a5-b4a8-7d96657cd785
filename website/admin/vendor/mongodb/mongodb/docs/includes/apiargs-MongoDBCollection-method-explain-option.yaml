source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  Defaults to the ``comment`` of the explained operation (if any).

  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readPreference
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
post: |
  This will be used for the returned command result document.
---
arg_name: option
name: verbosity
type: string
description: |
  The verbosity level at which to run the command. See the :manual:`explain
  </reference/command/explain>` command for more information.
interface: phpmethod
operation: ~
optional: true
...
