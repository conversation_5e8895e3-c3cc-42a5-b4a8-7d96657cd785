source:
  file: apiargs-aggregate-option.yaml
  ref: allowDiskUse
---
source:
  file: apiargs-aggregate-option.yaml
  ref: batchSize
---
source:
  file: apiargs-aggregate-option.yaml
  ref: bypassDocumentValidation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  The comment can be any valid BSON type for server versions 4.4 and above.
  Earlier server versions only support string values.
---
source:
  file: apiargs-aggregate-option.yaml
  ref: explain
---
source:
  file: apiargs-common-option.yaml
  ref: hint
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.9
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBDatabase-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBDatabase-common-option.yaml
  ref: readPreference
---
source:
  file: apiargs-common-option.yaml
  ref: session
---
source:
  file: apiargs-MongoDBDatabase-common-option.yaml
  ref: typeMap
---
source:
  file: apiargs-MongoDBDatabase-common-option.yaml
  ref: writeConcern
post: |
  This only applies when a :ref:`$out <agg-out>` or :ref:`$merge <agg-merge>`
  stage is specified.
...
