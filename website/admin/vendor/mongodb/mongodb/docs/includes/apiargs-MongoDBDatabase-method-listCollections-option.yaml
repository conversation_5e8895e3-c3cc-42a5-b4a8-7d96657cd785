arg_name: option
name: authorizedCollections
type: boolean
description: |
  A flag that determines which collections are returned based on the user
  privileges when access control is enabled. For more information, see the
  `listCollections command documentation <https://mongodb.com/docs/manual/reference/command/listCollections/>`_.

  For servers < 4.0, this option is ignored.

  .. versionadded:: 1.12
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
arg_name: option
name: filter
type: array|object
description: |
  A query expression to filter the list of collections.

  You can specify a query expression for collection fields (e.g. ``name``,
  ``options``).
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
...
