source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: projection
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: sort
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
source:
  file: apiargs-common-option.yaml
  ref: hint
post: |
  This option is available in MongoDB 4.4+ and will result in an exception at
  execution time if specified for an older server version.

  .. versionadded:: 1.7
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: bypassDocumentValidation
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.13
---
arg_name: option
name: returnDocument
type: integer
description: |
  Specifies whether to return the document before the replacement is applied, or
  after. ``returnDocument`` supports the following values:

  - ``MongoDB\Operation\FindOneAndReplace::RETURN_DOCUMENT_BEFORE`` (*default*)
  - ``MongoDB\Operation\FindOneAndReplace::RETURN_DOCUMENT_AFTER``
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
post: |
  This will be used for the returned result document.
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: upsert
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
...
