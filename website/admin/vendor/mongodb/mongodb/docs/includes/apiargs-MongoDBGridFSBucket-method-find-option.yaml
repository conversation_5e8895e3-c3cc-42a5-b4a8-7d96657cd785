source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: projection
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: sort
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: skip
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: limit
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: allowDiskUse
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: batchSize
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: collation
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: comment
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: cursorType
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: readConcern
description: |
   :manual:`Read concern </reference/read-concern>` to use for the operation.
   Defaults to the bucket's read concern.
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: readPreference
description: |
   :manual:`Read preference </reference/read-preference>` to use for the
   operation. Defaults to the bucket's read preference.
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: oplogReplay
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: noCursorTimeout
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: allowPartialResults
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: typeMap
replacement:
  parent: "bucket"
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: modifiers
...
