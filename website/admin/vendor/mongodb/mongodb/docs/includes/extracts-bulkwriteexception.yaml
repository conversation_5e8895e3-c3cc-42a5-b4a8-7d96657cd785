ref: bulkwriteexception-result
content: |
  If a :php:`MongoDB\\Driver\\Exception\\BulkWriteException
  <mongodb-driver-exception-bulkwriteexception>` is thrown, users should call
  :php:`getWriteResult() <mongodb-driver-writeexception.getwriteresult>` and
  inspect the returned :php:`MongoDB\\Driver\\WriteResult
  <mongodb-driver-writeresult>` object to determine the nature of the error.

  For example, a write operation may have been successfully applied to the
  primary server but failed to satisfy the write concern (e.g. replication took
  too long). Alternatively, a write operation may have failed outright (e.g.
  unique key violation).
---
ref: bulkwriteexception-ordered
content: |
  In the case of a bulk write, the result may indicate multiple successful write
  operations and/or errors. If the ``ordered`` option is ``true``, some
  operations may have succeeded before the first error was encountered and the
  exception thrown. If the ``ordered`` option is ``false``, multiple errors may
  have been encountered.
...
