source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: projection
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: sort
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: skip
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: allowDiskUse
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: comment
---
source:
  file: apiargs-common-option.yaml
  ref: hint
post: |
  .. versionadded:: 1.2
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readPreference
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
post: |
  This will be used for the returned result document.
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: max
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: maxScan
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: min
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: returnKey
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: showRecordId
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: modifiers
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.13
...
