arg_name: option
name: arrayFilters
type: array
description: |
   An array of filter documents that determines which array elements to modify
   for an update operation on an array field.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: bypassDocumentValidation
type: boolean
description: |
   If ``true``, allows the write operation to circumvent document level
   validation. Defaults to ``false``.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: collation
post: |
   If the collation is unspecified but the collection has a default collation,
   the operation uses the collation specified for the collection. If no
   collation is specified for the collection or for the operation, MongoDB uses
   the simple binary comparison used in prior versions for string comparisons.
---
arg_name: option
name: readConcern
type: :php:`MongoDB\\Driver\\ReadConcern <class.mongodb-driver-readconcern>`
description: |
   :manual:`Read concern </reference/read-concern>` to use for the operation.
   Defaults to the collection's read concern.

   It is not possible to specify a :manual:`read concern
   </reference/read-concern>` for individual operations as part of a
   transaction. Instead, set the ``readConcern`` option when starting the
   transaction with :php:`startTransaction <mongodb-driver-session.starttransaction>`.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: readPreference
type: :php:`MongoDB\\Driver\\ReadPreference <class.mongodb-driver-readpreference>`
description: |
   :manual:`Read preference </reference/read-preference>` to use for the
   operation. Defaults to the collection's read preference.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: typeMap
replacement:
  parent: "collection"
---
arg_name: option
name: writeConcern
type: :php:`MongoDB\\Driver\\WriteConcern <class.mongodb-driver-writeconcern>`
description: |
   :manual:`Write concern </reference/write-concern>` to use for the operation.
   Defaults to the collection's write concern.

   It is not possible to specify a :manual:`write concern
   </reference/write-concern>` for individual operations as part of a
   transaction. Instead, set the ``writeConcern`` option when starting the
   transaction with :php:`startTransaction <mongodb-driver-session.starttransaction>`.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: upsert
type: boolean
description: |
   If set to ``true``, creates a new document when no document matches the query
   criteria. The default value is ``false``, which does not insert a new
   document when no match is found.
interface: phpmethod
operation: ~
optional: true
...
