services:
  website:
    build: website/
    volumes:
      - ./api.env:/etc/massacremc/config/api.env:ro
      - ./admin.env:/etc/massacremc/config/admin.env:ro
      - ./sites-enabled:/etc/apache2/sites-enabled:ro
      #- ./website:/var/www/html/Massacre-Website:ro
      - ./certs:/etc/ssl/certs:ro
    ports:
      - 80:80
      - 443:443
    networks:
      - default
    restart: unless-stopped

networks:
  default:
    external: true
    name: production_default
