# HTTP Virtual Host - redirect to HTTPS
<VirtualHost *:80>
    ServerName help.massacremc.net
    DocumentRoot /var/www/html/Massacre-Website/help

    # Redirect HTTP to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
</VirtualHost>

# HTTPS Virtual Host
<VirtualHost *:443>
    ServerName help.massacremc.net
    DocumentRoot /var/www/html/Massacre-Website/help

    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/massacremc.net.pem
    SSLCertificateKeyFile /etc/ssl/certs/massacremc.net.key

    # SSL Security Settings
    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite HIGH:!aNULL:!MD5:!3DES
    SSLHonorCipherOrder on

    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' https://browser.sentry-cdn.com https://js-de.sentry-cdn.com https://code.jquery.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://static.cloudflareinsights.com 'unsafe-inline' blob:; style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data: https://mc-heads.net https://crafthead.net; font-src 'self' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; worker-src blob:; connect-src 'self' https://*.cloudflare.com https://*.ingest.sentry.io https://o4508487756480512.ingest.de.sentry.io https://minecraftpocket-servers.com https://api.massacremc.net https://api.mcsrvstat.us; form-action 'self'; base-uri 'self'; object-src 'none'; frame-ancestors 'none';"

    # Enable Gzip Compression
    AddOutputFilterByType DEFLATE text/plain text/css text/xml application/xml application/xhtml+xml text/javascript application/javascript application/x-javascript application/json image/svg+xml

    # Static file caching
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, immutable"
    </FilesMatch>

    # Deny access to sensitive files
    <FilesMatch "^\.">
        Require all denied
    </FilesMatch>

    <FilesMatch "(composer\.(json|lock)|package\.(json|lock)|\.env|\.git.*|\.htaccess)$">
        Require all denied
    </FilesMatch>

    # Directory Configuration
    <Directory "/var/www/html/Massacre-Website/help">
        Options -Indexes +FollowSymLinks
        AllowOverride FileInfo
        Require all granted

        # Restrict HTTP Methods
        <LimitExcept GET POST HEAD>
            Deny from all
        </LimitExcept>

        # Clean URLs - remove .php extension
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_URI} !\.php$
        RewriteRule ^(.*)$ $1.php [L]

        # Redirect .php URLs to clean URLs (preserve query string)
        RewriteCond %{THE_REQUEST} "^[A-Z]+\s(.*)\.php[?\s]"
        RewriteRule ^(.*)\.php$ /$1 [R=301,L,QSA]
    </Directory>

    # PHP Configuration
    php_value upload_max_filesize 10M
    php_value post_max_size 10M

    # Error and Access Logs
    CustomLog /var/log/apache2/help.massacremc.net_access.log combined
    ErrorLog /var/log/apache2/help.massacremc.net_error.log
</VirtualHost>