<?php
/**
 * Ticket Email Notification System
 * Handles sending email notifications for ticket updates
 */

require_once __DIR__ . '/logging.php';

/**
 * Custom config file parser that handles problematic characters like ~
 */
function parse_config_file($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }

    $config = [];
    $lines = file($file_path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

    if ($lines === false) {
        return false;
    }

    foreach ($lines as $line_num => $line) {
        $line = trim($line);

        // Skip comments and empty lines
        if (empty($line) || $line[0] === '#' || $line[0] === ';') {
            continue;
        }

        // Look for key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);

            // Remove quotes if present
            if (strlen($value) >= 2 &&
                (($value[0] === '"' && $value[strlen($value)-1] === '"') ||
                 ($value[0] === "'" && $value[strlen($value)-1] === "'"))) {
                $value = substr($value, 1, -1);
            }

            // Handle ~ character by expanding it to home directory if needed
            if (strpos($value, '~') === 0) {
                $value = str_replace('~', '/home', $value);
            }

            $config[$key] = $value;
        }
    }

    return $config;
}

/**
 * Send ticket confirmation email to customer
 */
function sendTicketConfirmationEmail($ticketId, $customerName, $customerEmail, $subject, $conversationToken) {
    try {
        // Load email configuration
        $env = [];
        $config_loaded = false;

        if (file_exists('/etc/massacremc/config/api.env')) {
            $env = parse_config_file('/etc/massacremc/config/api.env');
            $config_loaded = true;
            secure_log("Loaded email config from /etc/massacremc/config/api.env", "info");
        } elseif (file_exists(__DIR__ . '/../api.env')) {
            $env = parse_config_file(__DIR__ . '/../api.env');
            $config_loaded = true;
            secure_log("Loaded email config from api.env", "info");
        } elseif (file_exists(__DIR__ . '/../admin.env')) {
            $env = parse_config_file(__DIR__ . '/../admin.env');
            $config_loaded = true;
            secure_log("Loaded email config from admin.env", "info");
        }

        if (!$config_loaded) {
            secure_log("No email configuration file found", "error");
            return false;
        }

        if (empty($env['MAILGUN_API_KEY']) || empty($env['MAILGUN_DOMAIN']) || empty($env['SENDER_EMAIL'])) {
            secure_log("Email configuration incomplete for ticket $ticketId - API Key: " . (!empty($env['MAILGUN_API_KEY']) ? "YES" : "NO") .
                      ", Domain: " . (!empty($env['MAILGUN_DOMAIN']) ? "YES" : "NO") .
                      ", Sender: " . (!empty($env['SENDER_EMAIL']) ? "YES" : "NO"), "warning");
            return false;
        }

        secure_log("Attempting to send ticket confirmation email for $ticketId to $customerEmail", "info");

        $conversationUrl = "https://help.massacremc.net/conversations?id=" . urlencode($ticketId) . "&token=" . urlencode($conversationToken);
        
        $emailSubject = "🎫 Support Ticket Created - " . $ticketId;
        
        $now = new DateTime();
        $now->setTimezone(new DateTimeZone('America/New_York'));
        $timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';
        $currentYear = $now->format('Y');

        $htmlBody = generateTicketConfirmationEmailHTML($ticketId, $customerName, $subject, $conversationUrl, $timestamp, $currentYear);
        
        return sendEmail($customerEmail, $emailSubject, $htmlBody, $env);
        
    } catch (Exception $e) {
        secure_log("Error sending ticket confirmation email: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * Send ticket reply notification to customer
 */
function sendTicketReplyNotification($ticket, $replyMessage, $staffName) {
    try {
        // Load email configuration
        $env = [];
        $config_loaded = false;

        if (file_exists('/etc/massacremc/config/api.env')) {
            $env = parse_config_file('/etc/massacremc/config/api.env');
            $config_loaded = true;
            secure_log("Loaded email config from /etc/massacremc/config/api.env for reply notification", "info");
        } elseif (file_exists(__DIR__ . '/../api.env')) {
            $env = parse_config_file(__DIR__ . '/../api.env');
            $config_loaded = true;
            secure_log("Loaded email config from api.env for reply notification", "info");
        } elseif (file_exists(__DIR__ . '/../admin.env')) {
            $env = parse_config_file(__DIR__ . '/../admin.env');
            $config_loaded = true;
            secure_log("Loaded email config from admin.env for reply notification", "info");
        }

        if (!$config_loaded) {
            secure_log("No email configuration file found for reply notification", "error");
            return false;
        }

        if (empty($env['MAILGUN_API_KEY']) || empty($env['MAILGUN_DOMAIN']) || empty($env['SENDER_EMAIL'])) {
            secure_log("Email configuration incomplete for reply notification - API Key: " . (!empty($env['MAILGUN_API_KEY']) ? "YES" : "NO") .
                      ", Domain: " . (!empty($env['MAILGUN_DOMAIN']) ? "YES" : "NO") .
                      ", Sender: " . (!empty($env['SENDER_EMAIL']) ? "YES" : "NO"), "warning");
            return false;
        }

        $conversationUrl = "https://help.massacremc.net/conversations?id=" . urlencode($ticket['ticket_id']) . "&token=" . urlencode($ticket['conversation_token']);
        
        $emailSubject = "💬 New Reply to Your Support Ticket - " . $ticket['ticket_id'];
        
        $now = new DateTime();
        $now->setTimezone(new DateTimeZone('America/New_York'));
        $timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';
        $currentYear = $now->format('Y');

        $htmlBody = generateTicketReplyEmailHTML($ticket, $replyMessage, $staffName, $conversationUrl, $timestamp, $currentYear);
        
        return sendEmail($ticket['customer_email'], $emailSubject, $htmlBody, $env);
        
    } catch (Exception $e) {
        secure_log("Error sending ticket reply notification: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * Generate HTML for ticket confirmation email
 */
function generateTicketConfirmationEmailHTML($ticketId, $customerName, $subject, $conversationUrl, $timestamp, $currentYear) {
    return "
    <!DOCTYPE html>
    <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Support Ticket Confirmation</title>
        </head>
        <body style='margin: 0; padding: 0; font-family: \"Segoe UI\", Arial, sans-serif; background-color: #121212; color: #e0e0e0;'>
            <div style='max-width: 600px; margin: 20px auto; background: #1e1e1e; border: 1px solid #333; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);'>
                <!-- Header -->
                <div style='background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 30px; text-align: center;'>
                    <img src='https://portal.massacremc.net/img/logo.png' alt='MassacreMC Logo' style='max-width: 150px; margin-bottom: 15px;'>
                    <h1 style='margin: 0; font-size: 28px; letter-spacing: 0.5px;'>Support Ticket Created</h1>
                    <p style='margin: 10px 0 0; font-size: 16px; opacity: 0.9;'>Ticket ID: {$ticketId}</p>
                </div>

                <!-- Main Content -->
                <div style='padding: 35px; background-color: #1e1e1e;'>
                    <h2 style='color: #007bff; font-size: 22px; margin-top: 0; margin-bottom: 25px;'>Hello {$customerName},</h2>
                    
                    <p style='color: #e0e0e0; line-height: 1.6; margin-bottom: 25px;'>
                        Thank you for contacting MassacreMC support! We've received your ticket and our team will review it shortly.
                    </p>

                    <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #007bff;'>
                        <h3 style='color: #007bff; margin-top: 0; font-size: 18px;'>Ticket Details</h3>
                        <table style='width: 100%; border-collapse: collapse;'>
                            <tr>
                                <td style='padding: 8px 0; border-bottom: 1px solid #333;'>
                                    <strong style='color: #007bff;'>Ticket ID:</strong>
                                    <span style='color: #e0e0e0; font-family: monospace;'>{$ticketId}</span>
                                </td>
                            </tr>
                            <tr>
                                <td style='padding: 8px 0; border-bottom: 1px solid #333;'>
                                    <strong style='color: #007bff;'>Subject:</strong>
                                    <span style='color: #e0e0e0;'>{$subject}</span>
                                </td>
                            </tr>
                            <tr>
                                <td style='padding: 8px 0;'>
                                    <strong style='color: #007bff;'>Created:</strong>
                                    <span style='color: #e0e0e0;'>{$timestamp}</span>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #28a745;'>
                        <h3 style='color: #28a745; margin-top: 0; font-size: 18px;'>What's Next?</h3>
                        <p style='color: #e0e0e0; line-height: 1.6; margin-bottom: 15px;'>
                            Our support team will review your ticket and respond within 24 hours. You can track the progress and reply to your ticket using the link below or by logging in to your account via <a href='https://portal.massacremc.net/account/tickets' style='color: #bb86fc; text-decoration: none;'>https://portal.massacremc.net/account/tickets</a>.
                        </p>
                        <div style='text-align: center; margin: 20px 0;'>
                            <a href='{$conversationUrl}' style='display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>
                                View Conversation
                            </a>
                        </div>
                    </div>

                    <div style='background: #252525; border-radius: 8px; padding: 20px; border-left: 4px solid #ffc107;'>
                        <h3 style='color: #ffc107; margin-top: 0; font-size: 18px;'>Important Notes</h3>
                        <ul style='color: #e0e0e0; line-height: 1.6; padding-left: 20px;'>
                            <li>You can reply to your ticket at any time using the conversation link or by logging in to your account via <a href='https://portal.massacremc.net/account/tickets' style='color: #bb86fc; text-decoration: none;'>https://portal.massacremc.net/account/tickets</a></li>
                            <li>Our typical response time is within 24 hours, but may vary depending on the complexity of the issue</li>
                        </ul>
                    </div>  
                </div>

                <!-- Footer -->
                <div style='background: linear-gradient(135deg, #6f42c1, #5a2d91); padding: 30px; text-align: center;'>
                    <h3 style='color: #ffffff; margin-top: 0; font-size: 20px; margin-bottom: 15px;'>MassacreMC Support</h3>
                    <p style='color: #ffffff; margin: 0; opacity: 0.9;'>
                        Server IP: <strong>play.massacremc.net</strong>
                    </p>
                    <p style='color: #ffffff; margin: 5px 0 0; opacity: 0.7; font-size: 14px;'>
                        © {$currentYear} MassacreMC. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
    </html>";
}

/**
 * Generate HTML for ticket reply notification email
 */
function generateTicketReplyEmailHTML($ticket, $replyMessage, $staffName, $conversationUrl, $timestamp, $currentYear) {
    $ticketId = $ticket['ticket_id'];
    $customerName = $ticket['customer_name'];
    $subject = $ticket['subject'];
    
    // Truncate reply message for email preview
    $previewMessage = strlen($replyMessage) > 200 ? substr($replyMessage, 0, 200) . '...' : $replyMessage;
    
    return "
    <!DOCTYPE html>
    <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>New Reply to Your Support Ticket</title>
        </head>
        <body style='margin: 0; padding: 0; font-family: \"Segoe UI\", Arial, sans-serif; background-color: #121212; color: #e0e0e0;'>
            <div style='max-width: 600px; margin: 20px auto; background: #1e1e1e; border: 1px solid #333; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);'>
                <!-- Header -->
                <div style='background: linear-gradient(135deg, #28a745, #1e7e34); color: white; padding: 30px; text-align: center;'>
                    <img src='https://portal.massacremc.net/img/logo.png' alt='MassacreMC Logo' style='max-width: 150px; margin-bottom: 15px;'>
                    <h1 style='margin: 0; font-size: 28px; letter-spacing: 0.5px;'>New Reply Received</h1>
                    <p style='margin: 10px 0 0; font-size: 16px; opacity: 0.9;'>Ticket ID: {$ticketId}</p>
                </div>

                <!-- Main Content -->
                <div style='padding: 35px; background-color: #1e1e1e;'>
                    <h2 style='color: #28a745; font-size: 22px; margin-top: 0; margin-bottom: 25px;'>Hello {$customerName},</h2>
                    
                    <p style='color: #e0e0e0; line-height: 1.6; margin-bottom: 25px;'>
                        Great news! Our support team has replied to your ticket. You can view the full response and continue the conversation using the link below.
                    </p>

                    <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #28a745;'>
                        <h3 style='color: #28a745; margin-top: 0; font-size: 18px;'>Reply from {$staffName}</h3>
                        <div style='background: #1a1a1a; padding: 15px; border-radius: 6px; margin-top: 15px;'>
                            <p style='color: #e0e0e0; line-height: 1.6; margin: 0; font-style: italic;'>
                                \"{$previewMessage}\"
                            </p>
                        </div>
                        <p style='color: #888; font-size: 14px; margin: 10px 0 0; text-align: right;'>
                            Replied on {$timestamp}
                        </p>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$conversationUrl}' style='display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;'>
                            View Full Conversation & Reply
                        </a>
                    </div>

                    <div style='background: #252525; border-radius: 8px; padding: 20px; border-left: 4px solid #17a2b8;'>
                        <h3 style='color: #17a2b8; margin-top: 0; font-size: 18px;'>Need to Reply?</h3>
                        <p style='color: #e0e0e0; line-height: 1.6; margin: 0;'>
                            Click the button above to view the full conversation and send your response. Our team is here to help!
                        </p>
                    </div>
                </div>

                <!-- Footer -->
                <div style='background: linear-gradient(135deg, #6f42c1, #5a2d91); padding: 30px; text-align: center;'>
                    <h3 style='color: #ffffff; margin-top: 0; font-size: 20px; margin-bottom: 15px;'>MassacreMC Support</h3>
                    <p style='color: #ffffff; margin: 0; opacity: 0.9;'>
                        Server IP: <strong>play.massacremc.net</strong>
                    </p>
                    <p style='color: #ffffff; margin: 5px 0 0; opacity: 0.7; font-size: 14px;'>
                        © {$currentYear} MassacreMC. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
    </html>";
}

/**
 * Send email using Mailgun API
 */
function sendEmail($to, $subject, $htmlBody, $env) {
    try {
        $mailgunDomain = $env['MAILGUN_DOMAIN'];
        $mailgunApiKey = $env['MAILGUN_API_KEY'];
        $senderEmail = $env['SENDER_EMAIL'];
        
        $url = "https://api.mailgun.net/v3/{$mailgunDomain}/messages";
        
        $postData = [
            'from' => "MassacreMC Support <{$senderEmail}>",
            'to' => $to,
            'subject' => $subject,
            'html' => $htmlBody
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$mailgunApiKey}");
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        // Handle SSL certificate verification
        $cert_paths = [
            '/etc/ssl/certs/ca-certificates.crt',
            '/etc/ssl/certs/cacert.pem',
            '/etc/pki/tls/certs/ca-bundle.crt',
            '/usr/share/ssl/certs/ca-bundle.crt',
            '/usr/local/share/certs/ca-root-nss.crt',
            '/etc/ssl/cert.pem'
        ];

        $cert_found = false;
        foreach ($cert_paths as $cert_path) {
            if (file_exists($cert_path)) {
                curl_setopt($ch, CURLOPT_CAINFO, $cert_path);
                $cert_found = true;
                secure_log("Using SSL certificate: $cert_path", "info");
                break;
            }
        }

        if (!$cert_found) {
            secure_log("No certificate file found, disabling SSL verification for email", "warning");
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        $curl_errno = curl_errno($ch);
        curl_close($ch);
        
        if ($curl_error) {
            secure_log("cURL error sending email: {$curl_error} (Error code: {$curl_errno})", "error");

            // If it's an SSL certificate error, try again with SSL verification disabled
            if ($curl_errno == 60 || $curl_errno == 77) { // SSL certificate problem
                secure_log("Retrying email with SSL verification disabled", "warning");

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
                curl_setopt($ch, CURLOPT_USERPWD, "api:{$mailgunApiKey}");
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
            } else {
                return false;
            }
        }

        if ($httpCode === 200) {
            secure_log("Email sent successfully to: $to", "info");
            return true;
        } else {
            secure_log("Failed to send email. HTTP Code: $httpCode, Response: $response", "error");
            return false;
        }
        
    } catch (Exception $e) {
        secure_log("Error sending email: " . $e->getMessage(), "error");
        return false;
    }
}
?>
