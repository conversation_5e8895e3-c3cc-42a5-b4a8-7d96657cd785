<?php
/**
 * Database Optimization Functions
 *
 * This file provides functions for optimizing database queries.
 */


if (!function_exists('create_mongo_regex_query')) {
    require_once __DIR__ . '/mongo_security.php';
}


// Ensure logging function is available
if (!function_exists('secure_log')) {
    require_once __DIR__ . '/logging.php';
}

/**
 * Create necessary indexes for optimal query performance
 *
 * @param MongoDB\Database $db MongoDB database instance
 */
function create_database_indexes($db) {
    try {

        $db->player_data->createIndex(['username' => 1]);
        $db->player_data->createIndex(['lastlogin' => -1]);
        $db->player_data->createIndex(['xuid' => 1], ['unique' => true, 'sparse' => true]);

        // Faction stats indexes for leaderboards
        $db->player_data->createIndex(['faction.stats.kills' => -1]);
        $db->player_data->createIndex(['faction.stats.deaths' => -1]);
        $db->player_data->createIndex(['faction.stats.doubloons' => -1]);
        $db->player_data->createIndex(['faction.stats.killstreak' => -1]);
        $db->player_data->createIndex(['faction.stats.strength' => -1]);

        $db->player_data->createIndex(
            ['username' => 'text', 'xuid' => 'text'],
            ['weights' => ['username' => 10, 'xuid' => 5]]
        );


        $db->player_data->createIndex(['username' => 1, 'lastlogin' => -1]);


        $db->faction_data->createIndex(['name' => 1], ['unique' => true]);
        $db->faction_data->createIndex(['strength' => -1]);
        $db->faction_data->createIndex(['bankdoubloons' => -1]);
        $db->faction_data->createIndex(['kills' => -1]);
        $db->faction_data->createIndex(['deaths' => -1]);
        $db->faction_data->createIndex(['killstreak' => -1]);
        $db->faction_data->createIndex(['bounties' => -1]);
        $db->faction_data->createIndex(['members' => 1]); // For member lookups


        $db->faction_data->createIndex(['strength' => -1, 'name' => 1]);
        $db->faction_data->createIndex(['bankdoubloons' => -1, 'name' => 1]);


        $db->punishments->createIndex(['player_name' => 1]);
        $db->punishments->createIndex(['punishment_id' => 1], ['unique' => true]);
        $db->punishments->createIndex(['active' => 1]);
        $db->punishments->createIndex(['expiration' => 1]);
        $db->punishments->createIndex(['punishment_type' => 1]);


        $db->punishments->createIndex(['player_name' => 1, 'active' => 1]);
        $db->punishments->createIndex(['player_name' => 1, 'expiration' => 1]);


        $db->appeals->createIndex(['player_name' => 1]);
        $db->appeals->createIndex(['punishment_id' => 1]);
        $db->appeals->createIndex(['status' => 1]);
        $db->appeals->createIndex(['created_at' => -1]);


        $db->appeals->createIndex(['player_name' => 1, 'punishment_id' => 1], ['unique' => true]);
        $db->appeals->createIndex(['status' => 1, 'created_at' => -1]);


        $db->reports->createIndex(['offender_name' => 1]);
        $db->reports->createIndex(['reporter_name' => 1]);
        $db->reports->createIndex(['status' => 1]);
        $db->reports->createIndex(['created_at' => -1]);
        $db->reports->createIndex(['rule_broken' => 1]); // For filtering by rule type


        $db->reports->createIndex(['status' => 1, 'created_at' => -1]);
        $db->reports->createIndex(['offender_name' => 1, 'status' => 1]);

        // Player accounts collection indexes
        $db->player_accounts->createIndex(['discord_id' => 1], ['unique' => true]);
        $db->player_accounts->createIndex(['discord_email' => 1]);
        $db->player_accounts->createIndex(['minecraft_usernames' => 1]);
        $db->player_accounts->createIndex(['verified_usernames' => 1]);
        $db->player_accounts->createIndex(['created_at' => -1]);
        $db->player_accounts->createIndex(['last_login' => -1]);

        // Compound indexes for player accounts
        $db->player_accounts->createIndex(['minecraft_usernames' => 1, 'discord_id' => 1]);
        $db->player_accounts->createIndex(['discord_email' => 1, 'discord_id' => 1]);

        secure_log("Database indexes created successfully", "info");
        return true;
    } catch (Exception $e) {
        secure_log("Error creating database indexes: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * Execute a database query with performance monitoring
 *
 * @param MongoDB\Collection $collection MongoDB collection
 * @param array $query Query filter
 * @param array $options Query options
 * @param string $queryName Optional name for the query for logging
 * @return MongoDB\Cursor Query result
 */
function execute_monitored_query($collection, $query, $options = [], $queryName = '') {
    $start = microtime(true);

    // Execute the query
    $result = $collection->find($query, $options);

    // Calculate query duration
    $duration = microtime(true) - $start;

    // Only log slow queries in production
    if ($duration > 0.5) {
        $queryInfo = [
            'collection' => $collection->getCollectionName(),
            'duration' => round($duration, 4) . 's'
        ];

        if ($queryName) {
            $queryInfo['name'] = $queryName;
        }

        secure_log("Slow query detected", "warning", $queryInfo);
    }

    return $result;
}




/**
 * Optimize a MongoDB query by adding proper projection and limits
 *
 * @param array $query Base query
 * @param array $fields Fields to include in projection
 * @param int $limit Maximum number of results
 * @param array $sort Sort order
 * @return array Optimized query options
 */
function optimize_query($query, $fields = [], $limit = 20, $sort = []) {
    $options = [];


    if (!empty($fields)) {
        $projection = [];
        foreach ($fields as $field) {
            $projection[$field] = 1;
        }
        $options['projection'] = $projection;
    }


    if ($limit > 0) {
        $options['limit'] = $limit;
    }


    if (!empty($sort)) {
        $options['sort'] = $sort;
    }

    return [
        'query' => $query,
        'options' => $options
    ];
}

/**
 * Cache query results for improved performance
 *
 * @param string $key Cache key
 * @param callable $queryFunction Function that executes the query
 * @param int $ttl Cache TTL in seconds
 * @param bool $force_refresh Force refresh the cache
 * @return mixed Query result
 */
function cached_query($key, $queryFunction, $ttl = 300, $force_refresh = false) {

    $normalized_key = 'cache_' . preg_replace('/[^a-zA-Z0-9_]/', '_', $key);

    // Use APCu if available
    if (function_exists('apcu_fetch') && !$force_refresh) {
        $result = apcu_fetch($normalized_key);

        if ($result !== false) {
            return $result;
        }

        $result = $queryFunction();


        if ($result !== null && $result !== false &&
            (!is_array($result) || !empty($result))) {
            apcu_store($normalized_key, $result, $ttl);
        }

        return $result;
    }


    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['query_cache'])) {
        $_SESSION['query_cache'] = [];
    }


    if (!$force_refresh &&
        isset($_SESSION['query_cache'][$normalized_key]) &&
        $_SESSION['query_cache'][$normalized_key]['expires'] > time()) {

        return $_SESSION['query_cache'][$normalized_key]['data'];
    }

    // Execute the query function
    $result = $queryFunction();

    // Store in session cache if valid result
    if ($result !== null && $result !== false &&
        (!is_array($result) || !empty($result))) {

        $_SESSION['query_cache'][$normalized_key] = [
            'data' => $result,
            'expires' => time() + $ttl
        ];

        // Occasionally clean up expired cache entries
        if (mt_rand(1, 100) === 1) {
            $now = time();
            foreach ($_SESSION['query_cache'] as $k => $v) {
                if ($v['expires'] < $now) {
                    unset($_SESSION['query_cache'][$k]);
                }
            }
        }
    }

    return $result;
}
