services:
  website:
    build: website/
    volumes:
      - ./website/api.env:/etc/massacremc/config/api.env:ro
      - ./website/admin.env:/etc/massacremc/config/admin.env:ro
      - ./website/sites-enabled:/etc/apache2/sites-enabled:ro
      #- ./website:/var/www/html/Massacre-Website:ro
      - ./certs:/etc/ssl/certs:ro
      - ./certs:/etc/ssl/private:ro
    ports:
      - 80:80
      - 443:443
    networks:
      - default
    restart: unless-stopped
  servers:
    build: ./core
    environment:
      - DATABASE_URI=mongodb://mongo:27017
      - JWT_SECRET=978794897848787484878456456164561515664545156584561561564
    volumes:
      - "./certs:/certs:ro"
      - "./core/worlds:/server/worlds:rw"
      - "./core/resources:/server/resources:ro"
      - "./core/config:/server/config:ro"
    restart: unless-stopped
    tty: true
    stdin_open: true
    ports:
      - "19132:19132/udp"
    depends_on:
      - mongo

  mongo:
    container_name: mongodb
    image: mongo:8
    restart: unless-stopped
    ports:
      - "127.0.0.1:27017:27017"
    volumes:
      - mongo:/data/db

volumes:
  mongo:

networks:
  default:
    external: true
    name: production_default